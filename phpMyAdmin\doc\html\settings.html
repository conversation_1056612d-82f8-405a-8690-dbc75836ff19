
<!DOCTYPE html>

<html lang="en">
  <head>
    <meta charset="utf-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" /><meta name="generator" content="Docutils 0.19: https://docutils.sourceforge.io/" />

    <title>Configuring phpMyAdmin &#8212; phpMyAdmin 5.2.2 documentation</title>
    <link rel="stylesheet" type="text/css" href="_static/pygments.css" />
    <link rel="stylesheet" type="text/css" href="_static/classic.css" />
    
    <script data-url_root="./" id="documentation_options" src="_static/documentation_options.js"></script>
    <script src="_static/jquery.js"></script>
    <script src="_static/underscore.js"></script>
    <script src="_static/_sphinx_javascript_frameworks_compat.js"></script>
    <script src="_static/doctools.js"></script>
    <script src="_static/sphinx_highlight.js"></script>
    
    <link rel="index" title="Index" href="genindex.html" />
    <link rel="search" title="Search" href="search.html" />
    <link rel="copyright" title="Copyright" href="copyright.html" />
    <link rel="next" title="Two-factor authentication" href="two_factor.html" />
    <link rel="prev" title="User Guide" href="user.html" /> 
  </head><body>
    <div class="related" role="navigation" aria-label="related navigation">
      <h3>Navigation</h3>
      <ul>
        <li class="right" style="margin-right: 10px">
          <a href="genindex.html" title="General Index"
             accesskey="I">index</a></li>
        <li class="right" >
          <a href="two_factor.html" title="Two-factor authentication"
             accesskey="N">next</a> |</li>
        <li class="right" >
          <a href="user.html" title="User Guide"
             accesskey="P">previous</a> |</li>
        <li class="nav-item nav-item-0"><a href="index.html">phpMyAdmin 5.2.2 documentation</a> &#187;</li>
          <li class="nav-item nav-item-1"><a href="user.html" accesskey="U">User Guide</a> &#187;</li>
        <li class="nav-item nav-item-this"><a href="">Configuring phpMyAdmin</a></li> 
      </ul>
    </div>  

    <div class="document">
      <div class="documentwrapper">
        <div class="bodywrapper">
          <div class="body" role="main">
            
  <section id="configuring-phpmyadmin">
<h1>Configuring phpMyAdmin<a class="headerlink" href="#configuring-phpmyadmin" title="Permalink to this heading">¶</a></h1>
<p>There are many configuration settings that can be used to customize the
interface. Those settings are described in
<a class="reference internal" href="config.html#config"><span class="std std-ref">Configuration</span></a>. There are several layers of the configuration.</p>
<p>The global settings can be configured in <code class="file docutils literal notranslate"><span class="pre">config.inc.php</span></code> as described in
<a class="reference internal" href="config.html#config"><span class="std std-ref">Configuration</span></a>. This is only way to configure connections to databases and other
system wide settings.</p>
<p>On top of this there are user settings which can be persistently stored in
<a class="reference internal" href="setup.html#linked-tables"><span class="std std-ref">phpMyAdmin configuration storage</span></a>, possibly automatically configured through
<a class="reference internal" href="setup.html#zeroconf"><span class="std std-ref">Zero configuration</span></a>.  If the <a class="reference internal" href="setup.html#linked-tables"><span class="std std-ref">phpMyAdmin configuration storage</span></a> are not configured, the settings
are temporarily stored in the session data; these are valid only until you
logout.</p>
<p>You can also save the user configuration for further use, either download them
as a file or to the browser local storage. You can find both those options in
the <span class="guilabel">Settings</span> tab. The settings stored in browser local storage will
be automatically offered for loading upon your login to phpMyAdmin.</p>
</section>


            <div class="clearer"></div>
          </div>
        </div>
      </div>
      <div class="sphinxsidebar" role="navigation" aria-label="main navigation">
        <div class="sphinxsidebarwrapper">
  <div>
    <h4>Previous topic</h4>
    <p class="topless"><a href="user.html"
                          title="previous chapter">User Guide</a></p>
  </div>
  <div>
    <h4>Next topic</h4>
    <p class="topless"><a href="two_factor.html"
                          title="next chapter">Two-factor authentication</a></p>
  </div>
  <div role="note" aria-label="source link">
    <h3>This Page</h3>
    <ul class="this-page-menu">
      <li><a href="_sources/settings.rst.txt"
            rel="nofollow">Show Source</a></li>
    </ul>
   </div>
<div id="searchbox" style="display: none" role="search">
  <h3 id="searchlabel">Quick search</h3>
    <div class="searchformwrapper">
    <form class="search" action="search.html" method="get">
      <input type="text" name="q" aria-labelledby="searchlabel" autocomplete="off" autocorrect="off" autocapitalize="off" spellcheck="false"/>
      <input type="submit" value="Go" />
    </form>
    </div>
</div>
<script>document.getElementById('searchbox').style.display = "block"</script>
        </div>
      </div>
      <div class="clearer"></div>
    </div>
    <div class="related" role="navigation" aria-label="related navigation">
      <h3>Navigation</h3>
      <ul>
        <li class="right" style="margin-right: 10px">
          <a href="genindex.html" title="General Index"
             >index</a></li>
        <li class="right" >
          <a href="two_factor.html" title="Two-factor authentication"
             >next</a> |</li>
        <li class="right" >
          <a href="user.html" title="User Guide"
             >previous</a> |</li>
        <li class="nav-item nav-item-0"><a href="index.html">phpMyAdmin 5.2.2 documentation</a> &#187;</li>
          <li class="nav-item nav-item-1"><a href="user.html" >User Guide</a> &#187;</li>
        <li class="nav-item nav-item-this"><a href="">Configuring phpMyAdmin</a></li> 
      </ul>
    </div>
    <div class="footer" role="contentinfo">
        &#169; <a href="copyright.html">Copyright</a> 2012 - 2024, The phpMyAdmin devel team.
      Created using <a href="https://www.sphinx-doc.org/">Sphinx</a> 5.3.0.
    </div>
  </body>
</html>