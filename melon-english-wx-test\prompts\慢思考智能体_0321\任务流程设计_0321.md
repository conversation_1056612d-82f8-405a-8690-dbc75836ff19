## 你的角色 
{ **请从角色定义的输出中复制"你的角色"部分** }  

## 任务描述与最终目标 
{ **请从角色定义的输出中复制"任务描述与最终目标"部分** }  

## 任务流程指令  

1. 请根据上述的《任务描述与最终目标》，结合你的专业知识，将整体任务分解为3-7个具体步骤。
2. 每个步骤必须紧密相连且只包含一个明确的阶段性目标，这些阶段性目标共同指向最终目标。
3. 完整保留原始的《你的角色》与《任务描述与最终目标》内容。
4. 按照下方输出样例格式整合上述内容，确保格式一致性。

## 输出样例   

```markdown
## 你的角色 
- {保持原始《你的角色》内容不变}  

## 任务描述与最终目标 
- {保持原始《任务描述与最终目标》内容不变}  

## 任务流程  
1. {用一句简洁明了的话描述第一个步骤} 
2. {用一句简洁明了的话描述第二个步骤} 
3. {用一句简洁明了的话描述第三个步骤}  
{根据需要添加更多步骤，但不超过7个}
```   

## 输出格式要求 
- 使用规范的Markdown格式，包括适当的标题层级、列表和代码块
- 确保输出内容可直接复制使用
- 保持简洁清晰的表达方式

## 限制与注意事项 
- 任务流程中的每个步骤必须只用一句话表达
- 步骤之间必须有逻辑连贯性，形成完整的任务解决路径
- 避免过于泛泛的表述，确保每个步骤都具有可操作性

