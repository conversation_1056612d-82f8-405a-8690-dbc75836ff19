// index.js
// 默认头像URL - 当用户还没有设置自己的头像时使用的图片地址
const defaultAvatarUrl = 'https://mmbiz.qpic.cn/mmbiz/icTdbqWNOwNRna42FI242Lcia07jQodd2FJGIYQfG0LAJGFxM4FbnQP6yfMxBgJ0F3YRqJCJ1aPAK2dQagdusBZg/0'

// 创建一个组件
Component({
  // 组件的数据（就像是组件的记忆）
  data: {
    motto: 'Hello World', // 显示的问候语
    userInfo: {  // 用户信息对象
      avatarUrl: defaultAvatarUrl, // 用户头像地址，默认使用系统头像
      nickName: '', // 用户昵称，初始为空
    },
    hasUserInfo: false, // 标记是否已获取用户信息
    canIUseGetUserProfile: wx.canIUse('getUserProfile'), // 检查设备是否支持getUserProfile接口
    canIUseNicknameComp: wx.canIUse('input.type.nickname'), // 检查设备是否支持昵称输入组件
    loading: false, // 加载状态
    apiResponse: null, // 保存接口返回结果
    errorMsg: '', // 错误信息
  },
  
  // 组件生命周期函数，在组件实例进入页面节点树时执行
  attached() {
    // 组件加载时不自动跳转到登录页面，留在当前测试页面
    // wx.navigateTo({
    //   url: '/pages/auth/index'
    // });
  },
  
  // 组件的方法集合
  methods: {
    // 跳转到API测试页面
    goToApiTest() {
      wx.navigateTo({
        url: '/pages/api-test/index'
      })
    },
    
    // 处理点击头像事件的函数
    // 参数：无特定参数
    // 用途：当用户点击头像时，跳转到日志页面
    bindViewTap() {
      wx.navigateTo({
        url: '../logs/logs' // 跳转到日志页面的路径
      })
    },
    
    // 处理选择头像事件的函数
    // 参数：e (对象) - 事件对象，包含用户选择的头像信息
    // 用途：当用户选择了新头像后，更新头像并检查用户信息是否完整
    onChooseAvatar(e) {
      const { avatarUrl } = e.detail  // 从事件中获取新头像URL
      const { nickName } = this.data.userInfo  // 获取当前昵称
      this.setData({
        "userInfo.avatarUrl": avatarUrl,  // 更新头像URL
        // 检查用户信息是否完整（有昵称和有效头像）
        hasUserInfo: nickName && avatarUrl && avatarUrl !== defaultAvatarUrl,
      })
    },
    
    // 处理昵称输入变化事件的函数
    // 参数：e (对象) - 事件对象，包含用户输入的昵称
    // 用途：当用户输入昵称时，更新昵称并检查用户信息是否完整
    onInputChange(e) {
      const nickName = e.detail.value  // 获取输入的昵称
      const { avatarUrl } = this.data.userInfo  // 获取当前头像URL
      this.setData({
        "userInfo.nickName": nickName,  // 更新昵称
        // 检查用户信息是否完整（有昵称和有效头像）
        hasUserInfo: nickName && avatarUrl && avatarUrl !== defaultAvatarUrl,
      })
    },
    
    // 获取用户个人资料的函数
    // 参数：e (对象) - 事件对象
    // 用途：通过微信API获取用户的个人资料（头像和昵称）
    getUserProfile(e) {
      // 调用微信API获取用户信息，每次调用都需要用户确认
      wx.getUserProfile({
        desc: '展示用户信息', // 获取用户信息的目的说明
        // 获取成功的回调函数
        success: (res) => {
          console.log(res)  // 打印获取到的结果
          this.setData({
            userInfo: res.userInfo,  // 更新用户信息
            hasUserInfo: true  // 标记已获取到用户信息
          })
        }
      })
    },
    
    // 微信登录按钮点击事件
    wxLogin() {
      this.setData({
        loading: true,
        apiResponse: null,
        errorMsg: ''
      });
      
      // 调用微信登录接口获取code
      wx.login({
        success: (res) => {
          if (res.code) {
            console.log('获取code成功:', res.code);
            this.callTestAPI(res.code);
          } else {
            this.setData({
              loading: false,
              errorMsg: '登录失败: ' + (res.errMsg || '未知错误')
            });
            console.error('登录失败:', res);
          }
        },
        fail: (err) => {
          this.setData({
            loading: false,
            errorMsg: '登录失败: ' + (err.errMsg || '未知错误')
          });
          console.error('登录失败:', err);
        }
      });
    },
    
    // 调用测试接口
    callTestAPI(code) {
      // 使用接口文档中的实际接口地址
      wx.request({
        url: 'https://www.shuimitao.online/wp-json/shuimitao/v1/get_course_list', 
        method: 'POST',
        data: {
          code: code,
          key: 'melon-english-test' // 替换为实际的API Key
        },
        header: {
          'content-type': 'application/json'
        },
        success: (res) => {
          console.log('接口调用成功:', res.data);
          
          // 检查HTTP状态码
          if (res.statusCode !== 200) {
            this.setData({
              loading: false,
              errorMsg: `HTTP错误: ${res.statusCode}`
            });
            return;
          }
          
          // 检查响应数据是否为空
          if (!res.data) {
            this.setData({
              loading: false,
              errorMsg: '接口返回了空数据'
            });
            return;
          }
          
          this.setData({
            loading: false,
            apiResponse: res.data
          });
          
          // 保存接口返回的数据到本地存储（可选）
          if (res.data.success) {
            try {
              wx.setStorageSync('melon_api_response', res.data);
            } catch (e) {
              console.error('保存数据失败', e);
            }
          }
        },
        fail: (err) => {
          console.error('接口调用失败:', err);
          this.setData({
            loading: false,
            errorMsg: '接口调用失败: ' + (err.errMsg || '未知错误') + '\n请检查网络连接或服务器状态'
          });
        },
        complete: () => {
          // 确保在任何情况下都关闭loading状态
          if (this.data.loading) {
            this.setData({
              loading: false
            });
          }
        }
      });
    }
  },
})
