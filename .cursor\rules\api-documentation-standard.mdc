---
description: 
globs: 
alwaysApply: true
---
# API 接口文档编写标准

**目标:** 确保所有在此 WordPress 主题中编写的 API 接口文档 (`.md` 文件) 遵循统一的结构、格式和详细程度。

**通用指南:** 请结合 @cursor_rules.mdc 中的通用规则执行以下步骤（例如，使用中文回复，使用反引号包裹标识符，通过 `edit_file` 工具进行修改等）。

**!!! 严格执行:** 在编写任何新的 API 接口文档 (`.md` 文件) 之前，**必须**先检查 `wp-content/themes/shuimitao.online/api/v1/apidoc/` 目录下的现有文档。使用 `list_dir` 查看文件列表，并使用 `read_file` 阅读一到两个相关或结构良好的现有文档，以理解当前的格式、详细程度和常用术语。目标是保持新文档与现有文档风格一致且内容详尽。

## 文档结构和内容要求

每个 API 接口文档 (`[接口名称].md`) 应至少包含以下部分：

### 1. 接口概述 (Overview)
*   **目的:** 简要说明此 API 接口的作用和主要功能。
*   **场景:** 描述在什么业务场景下会调用此接口。

### 2. 端点信息 (Endpoint Information)
*   **请求方法 (Method):** 明确指出是 `GET`, `POST`, `PUT`, `DELETE` 等。
*   **URL 路径 (Path):** 完整的相对路径，例如 `/shuimitao/v1/user/progress`。

### 3. 请求参数 (Request Parameters)
*   **路径参数 (Path Parameters):** 如果 URL 路径中包含动态参数（例如 `/orders/{order_id}`），在此列出参数名称、类型、描述和示例。
*   **查询参数 (Query Parameters):** 对于 `GET` 请求，列出所有可能的查询参数。包括：
    *   参数名称
    *   是否必需 (Required/Optional)
    *   数据类型 (e.g., `string`, `int`, `boolean`, `array`)
    *   描述 (说明参数用途)
    *   示例值
*   **请求体 (Request Body):** 对于 `POST`, `PUT` 请求，详细描述请求体的结构。
    *   **格式:** 通常是 `application/json`。
    *   **字段:** 列出所有字段，包括：
        *   字段名称 (嵌套结构使用点表示法或缩进)
        *   是否必需 (Required/Optional)
        *   数据类型 (e.g., `string`, `int`, `boolean`, `array`, `object`)
        *   描述 (说明字段含义和约束条件)
        *   示例值
    *   **提供完整的 JSON 示例。**

### 4. 请求头 (Request Headers) - 可选
*   如果需要特定的请求头（例如 `Authorization` Token, `X-WP-Nonce` 等），在此说明。

### 5. 权限要求 (Permissions)
*   明确说明调用此接口所需的权限。
*   例如：需要用户登录、需要特定用户角色、或引用了 `permission_callback` 函数（如果适用）。

### 6. 成功响应 (Success Response)
*   **状态码 (Status Code):** 明确指出成功时的 HTTP 状态码（通常是 `200 OK`）。
*   **响应体 (Response Body):** 详细描述成功时返回的 JSON 结构。
    *   **格式:** `application/json`。
    *   **字段:** 列出所有字段，包括 `success: true` 以及 `data` 和 `message` 字段。详细说明 `data` 字段的结构：
        *   字段名称
        *   数据类型
        *   描述
    *   **提供完整的成功 JSON 响应示例。**

### 7. 错误响应 (Error Response)
*   列出可能发生的常见错误情况。
*   对于每种错误情况，说明：
    *   **状态码 (Status Code):** 例如 `400`, `401`, `403`, `404`, `500`。
    *   **响应体 (Response Body):** 描述 `WP_Error` 返回的 JSON 结构，包含 `success: false`, `code` (错误码字符串), `message` (错误描述), `data` (附加信息，可选)。
    *   **提供具体的错误 JSON 响应示例。**

### 8. 示例 (Examples) - 可选但推荐
*   提供一个或多个完整的请求/响应示例。
*   可以使用 `cURL` 命令或 JavaScript `fetch` 代码片段作为请求示例。

### 9. 注意事项 (Notes) - 可选
*   任何需要特别强调的逻辑、限制或其他说明。

**重要提示:**

*   **参考现有文档:** 再次强调，务必参考 `apidoc` 目录下的现有文档来保持风格和细节水平的一致性。
*   **清晰和准确:** 使用清晰、无歧义的语言描述每个部分。确保类型、必需性、示例值等信息准确无误。
*   **Markdown 格式:** 使用 Markdown 的格式化功能（如代码块、列表、表格）使文档易于阅读。
*   **与代码同步:** 当接口实现发生变化时，务必及时更新对应的文档。 