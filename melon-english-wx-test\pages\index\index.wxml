<!--index.wxml-->
<navigation-bar title="接口测试" back="{{false}}" color="black" background="#FFF"></navigation-bar>
<view class="container">
  <view class="logo-container">
    <image class="logo" src="/assets/images/avatar.jpg" mode="aspectFit"></image>
  </view>
  
  <view class="title">Melon English</view>
  
  <button class="api-test-btn" type="primary" bindtap="goToApiTest">
    进入接口测试页面
  </button>
  
  <button class="login-btn" type="primary" bindtap="wxLogin" loading="{{loading}}" disabled="{{loading}}">
    微信登录
  </button>
  
  <!-- 错误信息展示 -->
  <view class="error-msg" wx:if="{{errorMsg}}">
    {{errorMsg}}
  </view>
  
  <!-- 接口返回数据展示 -->
  <view class="api-response" wx:if="{{apiResponse}}">
    <view class="response-title">接口返回数据:</view>
    <view class="response-success" wx:if="{{apiResponse.success}}">
      <view class="success-tag">请求成功</view>
      
      <view class="data-item">
        <text class="label">连续学习天数:</text>
        <text class="value">{{apiResponse.continuous_learning_days}}</text>
      </view>
      
      <view class="data-item">
        <text class="label">累计学习天数:</text>
        <text class="value">{{apiResponse.total_learning_days}}</text>
      </view>
      
      <view class="data-item">
        <text class="label">总课时:</text>
        <text class="value">{{apiResponse.total_course_hours}}</text>
      </view>
      
      <view class="data-item">
        <text class="label">已完成课时:</text>
        <text class="value">{{apiResponse.completed_course_hours}}</text>
      </view>
      
      <view class="data-item">
        <text class="label">打卡状态:</text>
        <text class="value">{{apiResponse.check_in_days}}</text>
      </view>
      
      <view class="course-title" wx:if="{{apiResponse.course_items.length > 0}}">课程列表:</view>
      <view class="course-list">
        <view class="course-item" wx:for="{{apiResponse.course_items}}" wx:key="course_id">
          <view class="course-header">
            <image class="course-image" src="{{item.course_img || '/assets/images/course1.jpg'}}" mode="aspectFill"></image>
            <view class="course-info">
              <text class="course-name">{{item.course_name}}</text>
              <text class="course-difficulty">{{item.course_difficulty}}</text>
            </view>
          </view>
          <text class="course-desc">{{item.course_desc}}</text>
          <text class="course-id">课程ID: {{item.course_id}}</text>
        </view>
      </view>
    </view>
    
    <view class="response-error" wx:else>
      <view class="error-tag">请求失败</view>
      <view class="error-message">{{apiResponse.message}}</view>
      <view class="error-detail" wx:if="{{apiResponse.error}}">{{apiResponse.error}}</view>
    </view>
  </view>
</view>
