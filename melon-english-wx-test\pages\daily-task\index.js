const mockData = require("../../mock/dailyTask.js");
import { request } from "../../utils/request.js";

Page({
  data: {
    continuousLearningDays: 5,
    totalLearningDays: 5,
    calendarData: {
      year: 2025,
      month: 2,
      days: [
        { day: 17 },
        { day: 18 },
        { day: 19 },
        { day: 20 },
        { day: 21 },
        { day: 22, completed: true },
        { day: 23 },
      ],
    },
    remainingTasks: "学习任意2课",
    remainingTaskCount: 2,
  },
  onLoad() {
    this.setCurrentYearMonth();
    this.fetchDailyTaskData();
  },
  setCurrentYearMonth() {
    const date = new Date();
    const year = date.getFullYear();
    const month = date.getMonth() + 1;
    this.setData({
      currentYear: year,
      currentMonth: month,
      [`calendarData.year`]: year,
      [`calendarData.month`]: month,
    });
  },
  getDayOfWeek(date) {
    const daysOfWeek = ["日", "一", "二", "三", "四", "五", "六"];
    return daysOfWeek[date.getDay()];
  },
  getWeekDays() {
    const today = new Date();
    const dayOfWeek = today.getDay(); // 0 (Sunday) to 6 (Saturday)
    const startDate = new Date(today);
    startDate.setDate(today.getDate() - dayOfWeek); // Start from Sunday

    const weekDays = [];
    for (let i = 0; i < 7; i++) {
      const currentDate = new Date(startDate);
      currentDate.setDate(startDate.getDate() + i);
      weekDays.push({
        date: currentDate.getDate(),
        dayOfWeek: this.getDayOfWeek(currentDate),
        completed: i % 2 === 0, // Mock completed status
      });
    }
    return weekDays;
  },
  async fetchDailyTaskData() {
    try {
      const response = await request.getDailyTask();
      if (response.success) {
        this.setData({
          continuousLearningDays: response.continuous_learning_days,
          totalLearningDays: response.total_learning_days,
          remainingTasks: response.remaining_tasks,
          remainingTaskCount: response.remaining_task_count,
        });
      }
    } catch (error) {
      console.error("获取每日任务数据失败:", error);
      this.setData({
        continuousLearningDays: mockData.dailyTask.continuousLearningDays,
        totalLearningDays: mockData.dailyTask.totalLearningDays,
        remainingTasks: mockData.dailyTask.remainingTasks,
        remainingTaskCount: mockData.dailyTask.remainingTaskCount,
      });
    }
  },
  onLoad() {
    this.setCurrentYearMonth();
    this.setData({
      weekDays: this.getWeekDays(),
    });
    this.fetchDailyTaskData();
  },
  onSwiperChange(e) {
    const { current } = e.detail;
    const date = new Date();
    date.setDate(date.getDate() + (current - 3)); // 假设当前显示的是一周的中间日期
    const year = date.getFullYear();
    const month = date.getMonth() + 1;
    this.setData({
      [`calendarData.year`]: year,
      [`calendarData.month`]: month,
    });
  },
});
