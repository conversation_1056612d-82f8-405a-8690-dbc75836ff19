﻿<view class="container">
  <view class="title">水蜜桃英语API测试</view>
  
  <!-- API选择选项卡 -->
  <view class="api-tabs">
    <!-- <view class="tab {{selectedApi === 'get_user' ? 'active' : ''}}" 
          bindtap="onSelectApi" 
          data-api="get_user">获取用户信息</view> -->
    <view class="tab {{selectedApi === 'refresh_user_info' ? 'active' : ''}}" 
          bindtap="onSelectApi" 
          data-api="refresh_user_info">刷新用户信息</view>
    <view class="tab {{selectedApi === 'login' ? 'active' : ''}}" 
          bindtap="onSelectApi" 
          data-api="login">用户登录</view>
    <view class="tab {{selectedApi === 'pronounce' ? 'active' : ''}}" 
          bindtap="onSelectApi" 
          data-api="pronounce">单词发音</view>
    <view class="tab {{selectedApi === 'update_user_avatar' ? 'active' : ''}}" 
          bindtap="onSelectApi" 
          data-api="update_user_avatar">更新头像</view>
    <view class="tab {{selectedApi === 'wechat_payment' ? 'active' : ''}}"
          bindtap="onSelectApi"
          data-api="wechat_payment">微信支付</view>
    <view class="tab {{selectedApi === 'get_vip_packages' ? 'active' : ''}}"
          bindtap="onSelectApi"
          data-api="get_vip_packages">获取VIP套餐</view>
  </view>
  
  <!-- 刷新用户信息API -->
  <view class="api-card" wx:if="{{selectedApi === 'refresh_user_info'}}">
    <view class="card-title">刷新用户信息接口测试</view>
    <view class="api-path">/wp-json/shuimitao/v1/refresh_user_info</view>
    
    <view class="api-note">
      <view class="note-title">接口说明:</view>
      <view class="note-content">
        <view>此接口用于获取当前登录用户的最新完整信息。</view>
        <view>需要先登录以获取 user_id。</view>
        <view class="note-tips">成功后，返回的数据会显示在下方，可用于验证用户信息是否正确或更新。</view>
      </view>
    </view>
    
    <view class="api-status">
      <view class="status-info">
        <text class="status-label">API Key:</text>
        <text class="status-value">已设置固定值</text>
      </view>
      <view class="status-info">
        <text class="status-label">User ID:</text>
        <text class="status-value {{user_id ? '' : 'empty'}}">{{user_id || '请先登录'}}</text>
      </view>
    </view>
    
    <button 
      class="test-btn" 
      bindtap="testRefreshUserInfoApi" 
      loading="{{loading}}"
      disabled="{{!user_id}}">测试刷新用户信息</button>
  </view>
  
  <!-- 登录API -->
  <view class="api-card" wx:if="{{selectedApi === 'login'}}">
    <view class="api-card-header">
      <view class="card-title">用户登录接口测试</view>
      <view class="api-path">/wp-json/shuimitao/v1/login</view>
    </view>
    
    <view class="api-note">
      <view class="note-title">接口说明:</view>
      <view class="note-content">
        <view>此接口用于用户登录，需要微信登录code、手机号数据和API Key</view>
        <view>测试步骤:</view>
        <view class="step">1. 点击"测试登录接口"获取临时code</view>
        <view class="step">2. 点击"获取手机号"获取加密数据并调用接口</view>
        <view class="note-warning">⚠ 重要提示：获取真实手机号必须在真机环境中测试!</view>
        <view class="note-warning">⚠ 每次请求时会自动获取新的code，确保不会过期!</view>
      </view>
    </view>
    
    <view class="api-status">
      <view class="status-info">
        <text class="status-label">API Key:</text>
        <text class="status-value">已设置固定值</text>
      </view>
    </view>
    
    <view class="button-group">
      <button 
        class="test-btn" 
        bindtap="testLoginApi" 
        loading="{{loading}}">测试登录接口</button>
      
      <button 
        class="phone-btn" 
        open-type="getPhoneNumber"
        bindgetphonenumber="getPhoneNumber"
        disabled="{{!code || loading}}"
        loading="{{loading}}">
        <text class="btn-icon">📱</text>获取手机号
      </button>
    </view>
    
    <view class="debug-panel">
      <view class="debug-item">
        <text class="debug-label">Code:</text>
        <text class="debug-value" wx:if="{{code}}">已获取</text>
        <text class="debug-value empty" wx:else>未获取</text>
      </view>
      
      <view class="debug-item">
        <text class="debug-label">EncryptedData:</text>
        <text class="debug-value" wx:if="{{encryptedData}}">已获取</text>
        <text class="debug-value empty" wx:else>未获取</text>
      </view>
      
      <view class="debug-item">
        <text class="debug-label">IV:</text>
        <text class="debug-value" wx:if="{{iv}}">已获取</text>
        <text class="debug-value empty" wx:else>未获取</text>
      </view>
    </view>
    
    <view class="phone-status" wx:if="{{encryptedData && iv}}">
      <view class="success-icon">✓</view>
      <text>已成功获取手机号加密数据，可以进行登录请求</text>
    </view>
  </view>
  
  <!-- 发音API -->
  <view class="api-card" wx:if="{{selectedApi === 'pronounce'}}">
    <view class="api-card-header">
      <view class="card-title">单词发音测试</view>
      <view class="api-path">https://dict.youdao.com/dictvoice</view>
    </view>
    
    <view class="api-note">
      <view class="note-title">功能说明:</view>
      <view class="note-content">
        <view>此功能使用有道词典API播放英文单词或短语的发音</view>
        <view>使用步骤:</view>
        <view class="step">1. 输入要发音的单词或短语</view>
        <view class="step">2. 点击"播放发音"按钮</view>
      </view>
    </view>
    
    <view class="input-group">
      <view class="input-label">单词/短语:</view>
      <input 
        class="api-key-input" 
        value="{{pronounceWord}}" 
        bindinput="updatePronounceWord"
        placeholder="输入要发音的单词或短语" />
    </view>
    
    <button 
      class="pronounce-btn" 
      bindtap="playPronunciation" 
      loading="{{loading}}">
      <text class="btn-icon">🔊</text>播放发音
    </button>
  </view>

  <!-- 更新头像API -->
  <view class="api-card" wx:if="{{selectedApi === 'update_user_avatar'}}">
    <view class="api-card-header">
      <view class="card-title">更新用户头像接口测试</view>
      <view class="api-path">/wp-json/shuimitao/v1/update_user_avatar</view>
    </view>
    
    <view class="api-note">
      <view class="note-title">功能说明:</view>
      <view class="note-content">
        <view>此功能用于获取微信用户最新头像并上传到服务器</view>
        <view>测试步骤:</view>
        <view class="step">点击"选择头像"按钮，从相册或拍照获取最新头像</view>
        <view class="step">选择后系统会自动将头像上传到服务器</view>
        <view class="note-warning">⚠ 重要提示：获取的临时文件URL会自动上传转为永久链接</view>
        <view class="note-tips">上传结果会显示在下方</view>
      </view>
    </view>
    
    
    <view class="button-group">
      <button 
        class="avatar-btn" 
        open-type="chooseAvatar" 
        bindchooseavatar="onChooseAvatar"
        loading="{{loading}}">
        <text class="btn-icon">👤</text>选择并上传头像
      </button>
    </view>
    
    
    <!-- Base64数据展示区域 -->
    <view class="base64-display" wx:if="{{avatarBase64}}">
      <view class="base64-title">Base64格式预览:</view>
      <image 
        class="avatar-image-small" 
        src="{{avatarBase64}}" 
        mode="aspectFit"></image>
      <view class="base64-info">Base64数据已自动上传到服务器</view>
    </view>
  </view>
  
  <!-- 微信支付API -->
  <view class="api-card" wx:if="{{selectedApi === 'wechat_payment'}}">
    <view class="api-card-header">
      <view class="card-title">微信支付测试</view>
      <view class="api-path">/wp-json/wechat-pay-orders/v1/payment</view>
    </view>
    
    <!-- 检查是否有套餐信息 -->
    <block wx:if="{{vipPackages && vipPackages.data && vipPackages.data.vip_packages && vipPackages.data.vip_packages.length > 0}}">
      <view class="package-buttons-container">
        <!-- 遍历 vipPackages.data.vip_packages 数组来创建按钮 -->
        <!-- 建议使用套餐的唯一ID作为key，如果title可能重复 -->
        <button 
          wx:for="{{vipPackages.data.vip_packages}}" 
          wx:for-item="packageItem" 
          wx:key="title" 
          class="test-btn package-btn" 
          bindtap="testWechatPayApi" 
          data-package="{{packageItem}}" 
          loading="{{loading}}">
          支付 {{packageItem.title}} (￥{{packageItem.price}})
        </button>
      </view>
    </block>
    <block wx:else>
      <!-- 如果没有套餐信息，显示提示 -->
      <view class="empty-package-tip">
        请先在"获取VIP套餐"选项卡中拉取套餐信息，或当前无可购买的套餐。
      </view>
    </block>
  </view>
  
  <!-- 新增：获取VIP套餐API -->
  <view class="api-card" wx:if="{{selectedApi === 'get_vip_packages'}}">
    <view class="api-card-header">
      <view class="card-title">获取VIP套餐信息测试</view>
      <view class="api-path">/wp-json/shuimitao/v1/get_vip_messages</view>
    </view>
    <view class="api-note">
      <view class="note-title">接口说明:</view>
      <view class="note-content">
        <view>此接口用于从后端拉取VIP套餐配置信息。</view>
        <view class="note-tips">成功后，返回的数据会显示在下方，并尝试保存到本地缓存。</view>
      </view>
    </view>
    <button 
      class="test-btn" 
      bindtap="testGetVipMessagesApi" 
      loading="{{loading}}">拉取套餐信息</button>
  </view>
  
  <!-- 错误提示区域 -->
  <view wx:if="{{errorMsg}}" class="error-container">
    <view class="error-msg">
      <text class="warning-icon">⚠</text>{{errorMsg}}
    </view>
    <!-- 当出现错误时显示重试按钮 -->
    <button 
      class="retry-btn" 
      bindtap="resetState" 
      size="mini">重置状态</button>
  </view>
  
  <view class="api-response {{loading ? 'loading' : ''}}">
    <view class="response-header">
      <view class="response-title">接口返回数据:</view>
      <button class="copy-btn" size="mini" bindtap="copyApiResponse" disabled="{{!apiResponse}}">
        {{copied ? '已复制' : '复制数据'}}
      </button>
    </view>
    
    <view wx:if="{{apiResponse}}" class="status-tags">
      <view class="success-tag" wx:if="{{apiResponse.success}}">请求成功</view>
      <view class="error-tag" wx:else>请求失败</view>
    </view>
    
    <view wx:if="{{loading}}" class="loading-text">加载中...</view>
    
    <scroll-view wx:if="{{apiResponse}}" scroll-y class="json-scroll">
      <view class="json-content">{{jsonString}}</view>
    </scroll-view>
    
    <view wx:elif="{{!loading}}" class="empty-state">
      <block wx:if="{{selectedApi === 'refresh_user_info'}}">
        请点击"测试刷新用户信息"按钮进行测试
      </block>
      <block wx:elif="{{selectedApi === 'login'}}">
        请按照步骤1-2测试登录接口
      </block>
      <block wx:elif="{{selectedApi === 'pronounce'}}">
        输入单词或短语点击"播放发音"按钮
      </block>
      <!-- 新增：获取VIP套餐的空状态提示 -->
      <block wx:elif="{{selectedApi === 'get_vip_packages'}}">
        请点击"拉取套餐信息"按钮进行测试
      </block>
    </view>
  </view>
</view>
