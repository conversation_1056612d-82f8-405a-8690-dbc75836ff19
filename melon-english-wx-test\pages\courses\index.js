// pages/courses/index.js
Page({
  /**
   * 页面的初始数据
   */
  data: {
    // 课程列表数组，包含不同状态的课程对象
    courses: [
      {
        id: 1,  // 课程ID
        title: "零基础发音课",  // 课程标题
        level: "难度: 小学/初中",  // 课程难度级别
        desc: "学英语的基础，从字母发音到音标，全方面讲解。",  // 课程描述
        status: "not-studied",  // 课程状态（用于样式控制）
        statusText: "未学习",  // 课程状态文本（显示给用户）
      },
      {
        id: 2,
        title: "不可试听课程",
        level: "难度: 小学/初中",
        desc: "不可试听课程",
        status: "not-available",
        statusText: "不可试听",
      },
      {
        id: 3,
        title: "需复习课程",
        level: "难度: 小学/初中",
        desc: "昨日已学习课程",
        status: "need-review",
        statusText: "需复习",
      },
      {
        id: 4,
        title: "等待复习课程",
        level: "难度: 小学/初中",
        desc: "今日已学习课程",
        status: "waiting-review",
        statusText: "等待复习",
      },
      {
        id: 5,
        title: "全部完成课程",
        level: "难度: 小学/初中",
        desc: "已复习2次",
        status: "completed",
        statusText: "全部完成",
      },
      {
        id: 6,
        title: "未学习课程",
        level: "难度: 小学/初中",
        desc: "未学习课程",
        status: "not-studied",
        statusText: "未学习",
        statusText: "未学习",  // 注意：这里有重复的属性
      },
      {
        id: 7,
        title: "未学习课程",
        level: "难度: 小学/初中",
        desc: "未学习课程",
        status: "not-studied",
        statusText: "未学习",
      },
      {
        id: 8,
        title: "未学习课程",
        level: "难度: 小学/初中",
        desc: "未学习课程",
        status: "not-studied",
        statusText: "未学习",
      },
      {
        id: 9,
        title: "未学习课程",
        level: "难度: 小学/初中",
        desc: "未学习课程",
        status: "not-studied",
        statusText: "未学习",
      },
    ],
  },

  /**
   * 生命周期函数--监听页面加载
   * 参数：options (对象) - 页面启动参数
   * 用途：当页面第一次加载时运行，可以获取页面参数和初始化数据
   */
  onLoad(options) {
    // 被注释掉的代码示例：如何获取课程ID并跳转到课程详情页
    // const courseId = options.courseId;
    // wx.navigateTo({
    //   url: `/pages/course-detail/index?id=${courseId}`,
    // });
  },

  /**
   * 生命周期函数--监听页面初次渲染完成
   * 参数：无
   * 用途：当页面首次渲染完成后运行
   */
  onReady() {},

  /**
   * 生命周期函数--监听页面显示
   * 参数：无
   * 用途：当页面显示/切入前台时运行
   */
  onShow() {},

  /**
   * 生命周期函数--监听页面隐藏
   * 参数：无
   * 用途：当页面隐藏/切入后台时运行
   */
  onHide() {},

  /**
   * 生命周期函数--监听页面卸载
   * 参数：无
   * 用途：当页面卸载时运行
   */
  onUnload() {},

  /**
   * 页面相关事件处理函数--监听用户下拉动作
   * 参数：无
   * 用途：当用户下拉页面时运行，通常用于刷新页面数据
   */
  onPullDownRefresh() {},

  /**
   * 页面上拉触底事件的处理函数
   * 参数：无
   * 用途：当用户上拉页面到底部时运行，通常用于加载更多数据
   */
  onReachBottom() {},

  /**
   * 用户点击右上角分享
   * 参数：无
   * 用途：配置页面的分享信息
   */
  onShareAppMessage() {},

  /**
   * 跳转到每日任务页面的函数
   * 参数：无
   * 用途：点击按钮时导航到每日任务页面
   */
  goToDailyTask: function () {
    wx.navigateTo({
      url: "/pages/daily-task/index",  // 每日任务页面的路径
    });
  },
});
