---
description: 
globs: 
alwaysApply: true
---
# API 接口创建标准流程

**目标:** 确保所有在此 WordPress 主题中创建的 API 接口遵循统一的结构、命名和注册方式。

**通用指南:** 请结合 @cursor_rules.mdc 中的通用规则执行以下步骤（例如，使用中文回复，使用反引号包裹标识符，通过 `edit_file` 工具进行修改等）。

**!!! 严格执行:** AI 必须严格遵循以下步骤和文件模板创建新的 API 接口。

## 创建步骤

1.  **创建路由文件:**
    *   **位置:** `wp-content/themes/shuimitao.online/api/v1/routes/`
    *   **命名:** `[接口名称].php` (使用小写字母和下划线，例如 `user_progress.php`)
    *   **操作:** 使用 `edit_file` 工具，并基于下面的"路由文件模板"创建新文件。**必须**替换模板中的所有占位符。
    *   **示例路径:** `wp-content/themes/shuimitao.online/api/v1/routes/user_progress.php`

2.  **创建处理器文件:**
    *   **位置:** `wp-content/themes/shuimitao.online/api/v1/handlers/`
    *   **命名:** `[接口名称].php` (与路由文件名保持一致)
    *   **操作:** 使用 `edit_file` 工具，并基于下面的"处理器文件模板"创建新文件。**必须**替换模板中的所有占位符，并实现具体的业务逻辑。
    *   **示例路径:** `wp-content/themes/shuimitao.online/api/v1/handlers/user_progress.php`

3.  **更新引导文件 (`bootstrap.php`):**
    *   **文件:** `wp-content/themes/shuimitao.online/api/bootstrap.php`
    *   **操作:** 使用 `edit_file` 工具，在文件的**末尾**添加对新创建的路由文件和处理器文件的 `require_once` 引用。
    *   **添加内容格式:**
        ```php
        // ... 其他 require_once 语句 ...

        // 加载 [功能描述] 接口
        require_once __DIR__ . '/v1/routes/[接口名称].php';
        require_once __DIR__ . '/v1/handlers/[接口名称].php';
        ```
    *   **注意:** 确保替换 `[接口名称]` 和 `[功能描述]`，并添加到文件现有内容的**最后面**。

4.  **创建接口文档文件:**
    *   **位置:** `wp-content/themes/shuimitao.online/api/v1/apidoc/`
    *   **命名:** `[接口名称].md` (与路由和处理器文件名保持一致，后缀为 `.md`)
    *   **操作:** 使用 `edit_file` 工具，创建一个**空白**的同名 Markdown 文件。**此步骤也必须严格执行。**
    *   **示例路径:** `wp-content/themes/shuimitao.online/api/v1/apidoc/user_progress.md`

## 文件模板

### 1. 路由文件模板 (`routes/[接口名称].php`)

```php
<?php
/**
 * API 路由: [接口功能简要描述]
 *
 * 该文件定义了用于 [具体说明接口用途，例如：保存用户课程进度] 的 API 路由。
 *
 * @package shuimitao_online_theme
 */

defined( 'ABSPATH' ) || exit; // 防止直接访问文件

/**
 * 注册 [接口名称] API 路由
 *
 * 将 /shuimitao/v1/[接口路径] 端点映射到处理函数。
 */
function register_[接口名称]_route() {
    register_rest_route(
        'shuimitao/v1', // 命名空间 (保持不变)
        '/[接口路径]',  // API 路径 (例如: /user_progress, 注意：如果[接口名称]本身就是路径，则可以直接用 '/[接口名称]')
        array(
            'methods'             => 'POST', // 或 'GET', 'PUT', 'DELETE' 等，根据需要选择
            'callback'            => 'handle_[接口名称]_request', // 处理器函数名称 (见下方处理器模板)
            // 权限回调。初期可以记录IP并返回true，但生产环境必须实现严格的权限检查（如API Key）。
            'permission_callback' => function( \WP_REST_Request $request ) {
                // 记录请求来源IP，用于初步调试和安全分析
                $ip_address = sanitize_text_field( $_SERVER['REMOTE_ADDR'] ?? 'unknown' );
                error_log('[' . __FUNCTION__ . '] API request for "' . $request->get_route() . '" from IP: ' . $ip_address);

                // TODO: 实现实际的权限检查逻辑。
                // 示例：基于请求头中的 API Key 进行验证
                // $received_api_key = $request->get_header('X-Your-Plugin-Api-Key'); 
                // $expected_api_key = get_option('your_plugin_setting_expected_api_key', '');
                // if ( empty($received_api_key) || empty($expected_api_key) || !hash_equals($expected_api_key, $received_api_key) ) {
                //     error_log('[' . __FUNCTION__ . '] Permission Denied for "' . $request->get_route() . '" from IP: ' . $ip_address . '. Reason: Invalid or missing API Key.');
                //     return new \WP_Error(
                //         'rest_forbidden_api_key',
                //         '提供的API Key无效或缺失。',
                //         array( 'status' => 403 )
                //     );
                // }
                // error_log('[' . __FUNCTION__ . '] API Key authentication successful for "' . $request->get_route() . '" from IP: ' . $ip_address);
                return true; // 警告：当前允许所有请求，请务必替换为实际的权限检查！
            }
        )
    );
}

// 将路由注册函数挂载到 rest_api_init 动作钩子上
add_action( 'rest_api_init', 'register_[接口名称]_route' );

```

### 2. 处理器文件模板 (`handlers/[接口名称].php`)

```php
<?php
/**
 * API 处理器: [接口功能简要描述]
 *
 * 该文件包含处理 /shuimitao/v1/[接口名称] 请求的业务逻辑。
 *
 * @package shuimitao_online_theme
 */

defined( 'ABSPATH' ) || exit; // 防止直接访问文件

/**
 * 处理 [接口名称] API 请求
 *
 * @param WP_REST_Request $request 完整的请求数据对象.
 * @return WP_REST_Response|WP_Error 成功时返回 WP_REST_Response 对象，失败时返回 WP_Error 对象。
 */
function handle_[接口名称]_request( WP_REST_Request $request ) {
    // 1. 获取和验证参数 (推荐使用 $request->get_params() 或 $request->get_json_params())
    // $params = $request->get_params();
    // error_log('[API DEBUG] ' . __FUNCTION__ . ' Params: ' . print_r($params, true)); // 添加日志方便调试

    // $required_param = $params['required_param'] ?? null;
    // if ( empty($required_param) ) {
    //     return new WP_Error( 'missing_param', '缺少必要的参数: required_param', array( 'status' => 400 ) );
    // }

    // 2. (可选) 用户认证和权限检查 (如果 permission_callback 不够用)
    // $user_id = get_current_user_id();
    // if ( !$user_id ) {
    //     return new WP_Error( 'unauthorized', '用户未登录', array( 'status' => 401 ) );
    // }

    // 3. 执行核心业务逻辑
    try {
        // --- 在这里编写你的主要代码 ---
        // 例如：与数据库交互、调用其他函数/类等
        // $result_data = some_function_or_method( $params );
        $result_data = array( 'message' => '操作成功执行' ); // 示例数据

        // 4. 准备成功响应数据
        $response_data = array(
            'success' => true,
            'data'    => $result_data, // 将业务逻辑结果放入 data 字段
            'message' => '[接口功能描述]成功', // 具体的成功消息
        );
        $response_status = 200; // HTTP 状态码

        error_log('[API SUCCESS] ' . __FUNCTION__ . ' executed successfully for user ' . ($user_id ?? 'guest') );

        // 5. 返回成功响应
        return new WP_REST_Response( $response_data, $response_status );

    } catch ( Exception $e ) {
        // 捕获业务逻辑中的异常
        error_log('[API ERROR] ' . __FUNCTION__ . ' Exception: ' . $e->getMessage() . ' for user ' . ($user_id ?? 'guest') );
        return new WP_Error(
            'internal_error', // 错误代码
            '处理请求时发生内部错误: ' . $e->getMessage(), // 错误消息
            array( 'status' => 500 ) // HTTP 状态码
        );
    }
}
```

**重要提示:**

*   **占位符替换:** 在使用模板时，务必将所有方括号 `[]` 包裹的占位符（如 `[接口名称]`, `[接口功能简要描述]`, `[接口路径]` 等）替换为实际、有意义的内容。
*   **错误处理:** 处理器模板中包含了基本的错误处理和日志记录示例 (`WP_Error`, `try-catch`, `error_log`)，请根据实际情况完善。
*   **安全性:** 确保对所有输入参数进行严格的验证和清理 (`validate_callback`, `sanitize_callback`)，并实施恰当的权限检查 (`permission_callback`)。
*   **日志:** 使用 `error_log` 添加调试信息，有助于追踪问题。
*   **一致性:** 严格遵循此流程和模板，保持 API 的一致性和可维护性。 
*   **占位符替换:** 在使用模板时，务必将所有方括号 `[]` 包裹的占位符（如 `[接口名称]`, `[接口功能简要描述]`, `[接口路径]` 等）替换为实际、有意义的内容。
*   **错误处理:** 处理器模板中包含了基本的错误处理和日志记录示例 (`WP_Error`, `try-catch`, `error_log`)，请根据实际情况完善。
*   **安全性:** 确保对所有输入参数进行严格的验证和清理 (`validate_callback`, `sanitize_callback`)，并实施恰当的权限检查 (`permission_callback`)。
*   **日志:** 使用 `error_log` 添加调试信息，有助于追踪问题。
*   **一致性:** 严格遵循此流程和模板，保持 API 的一致性和可维护性。 