## 接口说明

### **获取用户信息接口 (get_user.php)**

#### **1. 接口基本信息**
- **路由路径:** `/wp-json/shuimitao/v1/get_user`
- **请求方法:** `POST`
- **请求参数:**
  - `code`: 微信小程序通过 `wx.login()` 获取的临时登录凭证 code。
  - `key`: API Key，用于验证请求的合法性。

#### **2. 处理流程**
1. **参数校验**
   - 检查 `code` 和 `key` 是否为空。
   - 如果缺少必要参数，返回错误信息：
     ```json
     {
         "success": false,
         "message": "缺少必要参数"
     }
     ```

2. **API Key 鉴权**
   - 从 WordPress 选项中获取 `shuimitao_api_key`。
   - 校验客户端传入的 `key` 是否与存储的 `shuimitao_api_key` 匹配。
   - 如果鉴权失败，返回错误信息：
     ```json
     {
         "success": false,
         "message": "API Key 鉴权失败"
     }
     ```

3. **获取微信小程序配置**
   - 从 WordPress 选项中获取小程序的 `appid` 和 `appsecret`。

4. **通过 code 换取 session_key 和 openid**
   - 向微信服务器发起请求，调用 `jscode2session` 接口，传入 `appid`、`appsecret` 和 `code`。
   - 如果请求失败，返回错误信息：
     ```json
     {
         "success": false,
         "message": "请求微信服务器失败"
     }
     ```
   - 如果微信返回错误码，返回错误信息：
     ```json
     {
         "success": false,
         "message": "获取用户信息失败",
         "error": "微信返回的错误信息"
     }
     ```

5. **用户注册状态检查**
   - 通过 `unionid` 检查用户是否已注册。
   - 如果用户未注册，返回错误信息：
     ```json
     {
         "success": false,
         "message": "用户未注册"
     }
     ```

6. **获取用户信息**
   - 如果用户已注册，获取用户的所有元数据。
   - 返回用户信息，并标记 `success` 为 `true`。

#### **3. 返回数据**
- **成功返回:**
  ```json
  {
      "success": true,
      "heika_vip_info": "用户黑卡VIP信息",
      "openid_to_wechat_mini_program": "用户在小程序的唯一标识",
      "telephone_number": "用户手机号",
      "union_id": "用户在微信生态的唯一标识",
      "user_avatar": "用户头像",
      "user_id": "用户ID",
      "user_last_login_time": "用户最后登录时间",
      "user_level": "用户等级",
      "user_nickname": "用户昵称",
      "user_orders": "用户订单信息",
      "user_tags": "用户标签",
      "weekly_reminder_status": "每周提醒状态"
  }
  ```

- **失败返回:**
  ```json
  {
      "success": false,
      "message": "错误信息",
      "error": "错误详情（可选）"
  }
  ```

#### **4. 前端调用示例**
```javascript
wx.login({
  success(res) {
    if (res.code) {
      wx.request({
        url: 'https://www.shuimitao.com/wp-json/shuimitao/v1/get_user',
        method: 'POST',
        data: {
          code: res.code,
          key: 'your_api_key'
        },
        success(response) {
          if (response.data.success) {
            console.log('获取用户信息成功', response.data);
          } else {
            console.error('获取用户信息失败', response.data.message);
          }
        },
        fail(error) {
          console.error('请求失败', error);
        }
      });
    } else {
      console.error('获取 code 失败', res.errMsg);
    }
  }
});
```

#### **5. 注意事项**
- **API Key 安全性:** 确保 `key` 的安全性，避免泄露。
- **微信小程序配置:** 确保 `appid` 和 `appsecret` 配置正确。
- **用户注册状态:** 如果用户未注册，前端需要引导用户进行注册。

---

### **更新说明**
- 新增了 **前端调用示例**，帮助前端开发者快速理解如何调用接口。
- 详细描述了 **处理流程**，确保与 `get_user.php` 代码逻辑一致。
- 补充了 **注意事项**，提醒开发者关注关键点。




### 新增字段说明 3.21

| 字段名                    | 类型   | 说明                                         |
|--------------------------|-------|---------------------------------------------|
| continuous_learning_days  | int   | 连续学习天数，表示用户连续学习的天数           |
| total_learning_days       | int   | 总学习天数，表示用户总共学习的天数             |
| total_course_hours        | int   | 总课程时长，表示所有课程的总时长（小时）       |
| completed_course_hours    | int   | 已完成课程时长，表示用户已完成的课程时长（小时）|
| check_in_days             | int   | 签到天数，表示用户签到的总天数                 |

---