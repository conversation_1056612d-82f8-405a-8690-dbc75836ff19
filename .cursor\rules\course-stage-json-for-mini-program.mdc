---
description: 
globs: 
alwaysApply: true
---
# course-stage-json-for-mini-program 规则

该规则用于帮助 AI 定位和理解新版课程关卡 JSON 数据及其处理流程：

- **样例文件路径:** `wp-content/themes/shuimitao.online/json/course_stage_json_for_mini_program.json`
- **数据库字段 (post_meta):** `course_stage_json_for_mini_program`，存储于文章(post)的自定义字段中。
- **获取方式:** 在 WordPress 环境下，通过以下代码获取原始 JSON 字符串：
  ```php
  $json = get_post_meta($post_id, 'course_stage_json_for_mini_program', true);
  ```

- **JSON 特征:**
  - 顶层为数组，每个元素为一个阶段卡片对象。
  - 通用字段：
    - `stageType` (string): 卡片类型，如 `begin`、`sentence`、`knowledgeHasImage`、`transition`、`word`、`choice`、`lack`、`spell`、`voice`、`wordchoice` 等。
    - `classifyType` (string): 分类，通常为 `course`（课程内容）或 `exercise`（练习）。
    - `timestamp` (int): 毫秒级时间戳，表示该阶段对应音频时间点。

  - 常见字段（根据 `stageType` 不同而变化）：
    - **begin/transition:** `showtext_1`、`showtext_2`、`img`、`cat_name`、`brand_name`
    - **sentence:** `english_sentence`、`chinese_sentence`、`rich_text`
    - **knowledgeHasImage:** `headline`、`img`、`rich_text`
    - **knowledge:** `headline`、`rich_text`
    - **word:** `learn_word`、`word_explain`、`word_phonetic`、`rich_text`
    - **choice:** `headline`、`question_stem_1`、`question_stem_2`、`option_1`、`option_2`、`option_3`、`right_option`
    - **lack:** `english_sentence`、`chinese_sentence`、`english_sentence_has_dot`、`english_sentence_no_dot`
    - **spell:** `headline`、`learn_word`、`word_explain`
    - **voice:** `headline`、`question_stem_1`
    - **wordchoice:** `headline`、`learn_word`、`word_explain`、`word_phonetic`

- **示例 JSON:**
  ```json
  [
    {
      "stageType": "begin",
      "classifyType": "course",
      "timestamp": 0,
      "showtext_1": "第一行文字",
      "showtext_2": "第二行文字",
      "cat_name": "begin 卡片",
      "brand_name": "品牌",
      "img": "https://www.shuimitao.online/wp-content/uploads/2019/01/qimen.jpg"
    },
    {
      "stageType": "sentence",
      "classifyType": "course",
      "timestamp": 1030,
      "english_sentence": "I m looking for someone.",
      "chinese_sentence": "我正在找个人",
      "rich_text": "<p>这是讲解部分</p>"
    }
  ]
  ```

- **处理流程:** 在 `wp-content/themes/shuimitao.online/api/v1/models/CourseDataHandler.php` 中的 `processCourseStageData` 方法里：
  1. 使用 `get_post_meta` 获取原始 JSON。
  2. 进行 JSON 解码、递归 `urldecode`、字符编码修复等。
  3. 清理空值字段、时间戳转换、字段合并、冗余字段删除。
  4. 最终使用 `update_post_meta` 保存标准化后的 JSON。

- **参考:** 请查看 [CourseDataHandler.php](mdc:api/v1/models/CourseDataHandler.php) 的 `processCourseStageData` 方法。







