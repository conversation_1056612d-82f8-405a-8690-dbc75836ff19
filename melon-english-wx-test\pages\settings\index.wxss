.container {
  min-height: 100vh;
  background: #f5f5f5;
  padding: 25rpx 10rpx;
  box-sizing: border-box;
  width: 100%;
}

.settings-list {
  width: 100%;
  background: #fff;
  border-radius: 12rpx;
  overflow: hidden;
  box-sizing: border-box;
}

.setting-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 30rpx 20rpx;
  background: #fff;
  border-bottom: 1rpx solid #f5f5f5;
  width: 100%;
  box-sizing: border-box;
}

.setting-item:last-child {
  border-bottom: none;
}

.setting-left {
  display: flex;
  align-items: center;
}

.setting-title {
  font-size: 28rpx;
  color: #333;
  font-weight: 400;
}

.setting-right {
  display: flex;
  align-items: center;
}

.clear-text {
  font-size: 28rpx;
  color: #999;
}

.status-text {
  font-size: 28rpx;
  color: #666;
  margin-right: 10rpx;
}

.toggle-icon {
  width: 60rpx;
  height: 36rpx;
}
