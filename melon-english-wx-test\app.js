/**
 * @功能概述: 小程序的全局逻辑入口文件。
 *           定义了小程序的生命周期方法和全局数据。
 */
App({
  /**
   * @功能概述: 小程序初始化完成时触发。
   *           全局只触发一次。可以在这里获取设备信息、执行登录等操作。
   *
   * @return {void}
   */
  onLaunch() {
    // 展示本地存储能力：获取日志缓存（一般存储小程序最近若干次启动的时间戳，可在微信开发者工具的"本地存储"面板查看）
    const logs = wx.getStorageSync('logs') || []
    logs.unshift(Date.now())
    // 将当前启动时间加入日志并存回本地
    wx.setStorageSync('logs', logs)

    // 登录：调用微信登录接口获取 code
    // wx.login({
    //   success: res => {
    //     // 发送 res.code 到后台换取 openId, sessionKey, unionId
    //     // TODO: 调用后端API，发送code进行用户身份验证和登录
    //   }
    // })
  },
  /**
   * @功能概述: 全局共享数据对象。
   *           可以在小程序的任何地方通过 getApp().globalData 访问。
   */
  globalData: {
    userInfo: null // 用户信息，初始化为 null
  }
})
