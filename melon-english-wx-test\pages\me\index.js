// pages/me/index.js
Page({
  /**
   * 页面的初始数据
   */
  data: {
    userInfo: {
      avatarUrl: "/assets/images/avatar.jpg",
      name: "匿名用户",
      id: "361198",
      isVip: false,
      vipExpire: "2026.02.17 18:15",
    },
  },

  /**
   * 生命周期函数--监听页面加载
   */
  onLoad(options) {
    // 获取用户信息
    const userInfo = wx.getStorageSync("userInfo");
    if (userInfo) {
      this.setData({
        userInfo,
      });
    }
    const userInfoExtra = wx.getStorageSync("user_info_extra");
    if (userInfoExtra) {
      this.setData({
        "userInfo.id": userInfoExtra.user_id,
        "userInfo.isVip": false,
      });
    }
    this.getUserInfo();
  },

  /**
   * 生命周期函数--监听页面初次渲染完成
   */
  onReady() {},

  /**
   * 生命周期函数--监听页面显示
   */
  onShow() {},

  /**
   * 生命周期函数--监听页面隐藏
   */
  onHide() {},

  /**
   * 生命周期函数--监听页面卸载
   */
  onUnload() {},

  /**
   * 页面相关事件处理函数--监听用户下拉动作
   */
  onPullDownRefresh() {},

  /**
   * 页面上拉触底事件的处理函数
   */
  onReachBottom() {},

  /**
   * 用户点击右上角分享
   */
  onShareAppMessage() {},

  getUserInfo() {
    wx.getUserInfo({
      success: (res) => {
        console.log("拿到用户信息", res);
        this.setData({
          userInfo: {
            ...this.data.userInfo,
            avatarUrl: res.userInfo.avatarUrl,
            name: res.userInfo.nickName,
          },
        });
        wx.setStorageSync("avatar_url", res.userInfo.avatarUrl);
      },
      fail: (err) => {
        console.error("获取用户信息失败", err);
      },
    });
  },

  // 查看详情
  checkDetail() {
    wx.navigateTo({
      url: "/pages/daily-task/index",
    });
  },

  // 今日复习
  goToReview() {
    wx.navigateTo({
      url: "/pages/review/index",
    });
  },

  // 设置
  goToSettings() {
    wx.navigateTo({
      url: "/pages/settings/index",
    });
  },

  // 开通会员
  goToPay() {
    wx.navigateTo({
      url: "/pages/pay/index",
    });
  },
});
