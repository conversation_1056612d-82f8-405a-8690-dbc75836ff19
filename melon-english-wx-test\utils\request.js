import { config } from "../config/index";

// 请求状态码
const HTTP_STATUS = {
  SUCCESS: 200,
  CLIENT_ERROR: 400,
  AUTHENTICATE: 401,
  FORBIDDEN: 403,
  NOT_FOUND: 404,
  SERVER_ERROR: 500,
  BAD_GATEWAY: 502,
  SERVICE_UNAVAILABLE: 503,
  GATEWAY_TIMEOUT: 504,
};

// 请求队列
let requestQueue = [];

// 请求工具类
class Request {
  constructor() {
    this._baseUrl = config.baseUrl;
    this._defaultHeader = {
      "Content-Type": "application/json",
    };
  }

  // 获取完整URL
  _getUrl(url) {
    if (url.startsWith("http")) {
      return url;
    }
    return this._baseUrl + url;
  }

  // 获取请求头
  _getHeader(options) {
    const token = wx.getStorageSync("token");
    const header = {
      ...this._defaultHeader,
      ...options.header,
    };

    if (token) {
      header.Authorization = token;
    }

    if (options.contentType) {
      header["Content-Type"] = options.contentType;
    }

    return header;
  }

  // 处理请求结果
  _handleResponse(response, resolve, reject) {
    const statusCode = response.statusCode;

    if (statusCode >= 200 && statusCode < 300) {
      resolve(response.data);
    } else {
      // 处理特定状态码
      switch (statusCode) {
        case HTTP_STATUS.AUTHENTICATE:
          // 处理未登录状态
          wx.navigateTo({
            url: "/pages/auth/index",
          });
          break;
        case HTTP_STATUS.FORBIDDEN:
          wx.showToast({
            title: "没有权限访问",
            icon: "none",
          });
          break;
        default:
          wx.showToast({
            title: response.data.message || "请求失败",
            icon: "none",
          });
      }
      reject(response);
    }
  }

  // 基础请求方法
  _request(options) {
    const that = this;
    return new Promise((resolve, reject) => {
      const requestTask = wx.request({
        url: this._getUrl(options.url),
        data: options.data,
        header: this._getHeader(options),
        method: options.method || "GET",
        timeout: config.timeout,
        success(response) {
          that._handleResponse(response, resolve, reject);
        },
        fail(error) {
          wx.showToast({
            title: "网络错误",
            icon: "none",
          });
          reject(error);
        },
        complete() {
          // 从请求队列中移除
          requestQueue = requestQueue.filter((task) => task !== requestTask);
        },
      });

      // 添加到请求队列
      requestQueue.push(requestTask);
    });
  }

  // GET请求
  get(url, data = {}, options = {}) {
    return this._request({
      url,
      data,
      method: "GET",
      ...options,
    });
  }

  // POST请求
  post(url, data = {}, options = {}) {
    return this._request({
      url,
      data,
      method: "POST",
      ...options,
    });
  }

  // 上传文件
  upload(url, filePath, formData = {}, options = {}) {
    const that = this;
    return new Promise((resolve, reject) => {
      const uploadTask = wx.uploadFile({
        url: this._getUrl(url),
        filePath,
        name: options.name || "file",
        formData,
        header: this._getHeader({
          ...options,
          contentType: "multipart/form-data",
        }),
        success(response) {
          // 上传接口返回的是字符串，需要转换为对象
          const data = JSON.parse(response.data);
          that._handleResponse({ ...response, data }, resolve, reject);
        },
        fail(error) {
          wx.showToast({
            title: "上传失败",
            icon: "none",
          });
          reject(error);
        },
      });

      // 添加到请求队列
      requestQueue.push(uploadTask);
    });
  }

  // 取消所有请求
  cancelAll() {
    requestQueue.forEach((task) => task.abort());
    requestQueue = [];
  }
}

export const request = new Request();

// 获取每日任务
request.getDailyTask = function (data = {}, options = {}) {
  return this.get("/get_daily_task", data, options);
};
