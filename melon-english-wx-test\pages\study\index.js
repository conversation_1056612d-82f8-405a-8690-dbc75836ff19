// pages/study/index.js
Page({

  /**
   * 页面的初始数据
   */
  data: {
    // 课程列表数组，包含多个课程对象
    classList: [
      { logo: '/assets/images/review.png', status: '未学习' },
      { logo: '/assets/images/review.png', status: '已学习' },
      { logo: '/assets/images/review.png', status: '未学习' },
      { logo: '/assets/images/review.png', status: '未学习' },
      { logo: '/assets/images/review.png', status: '未学习' },
      // ... 其他课程
    ],
    // 卡片列表数组，包含多个卡片对象
    cardList: [
      { title: '卡片1', image: '/assets/images/review.png', description: '这是卡片1的简洁描述' },
      { title: '卡片2', image: '/assets/images/review.png', description: '这是卡片2的简洁描述' },
      { title: '卡片3', image: '/assets/images/review.png', description: '这是卡片3的简洁描述' },
      { title: '卡片4', image: '/assets/images/review.png', description: '这是卡片4的简洁描述' },
      { title: '卡片5', image: '/assets/images/review.png', description: '这是卡片5的简洁描述' },
      { title: '卡片6', image: '/assets/images/review.png', description: '这是卡片6的简洁描述' },
      { title: '卡片6', image: '/assets/images/review.png', description: '这是卡片6的简洁描述' },
      { title: '卡片6', image: '/assets/images/review.png', description: '这是卡片6的简洁描述' },
      { title: '卡片6', image: '/assets/images/review.png', description: '这是卡片6的简洁描述' },
      { title: '卡片6', image: '/assets/images/review.png', description: '这是卡片6的简洁描述' },
    ]
  },

  /**
   * 生命周期函数--监听页面加载
   * 参数：options (对象) - 页面启动参数
   * 用途：当页面第一次加载时运行，可以用来获取启动参数和初始化数据
   */
  onLoad(options) {
    // 暂无操作
  },

  /**
   * 生命周期函数--监听页面初次渲染完成
   * 参数：无
   * 用途：当页面首次渲染完成后运行，可以用来执行需要等待页面显示后的操作
   */
  onReady() {
    // 暂无操作
  },

  /**
   * 生命周期函数--监听页面显示
   * 参数：无
   * 用途：当页面显示/切入前台时运行，可以用来刷新或重新获取数据
   */
  onShow() {
    // 暂无操作
  },

  /**
   * 生命周期函数--监听页面隐藏
   * 参数：无
   * 用途：当页面隐藏/切入后台时运行，可以用来暂停或停止某些操作
   */
  onHide() {
    // 暂无操作
  },

  /**
   * 生命周期函数--监听页面卸载
   * 参数：无
   * 用途：当页面卸载时运行，可以用来清理资源或保存数据
   */
  onUnload() {
    // 暂无操作
  },

  /**
   * 页面相关事件处理函数--监听用户下拉动作
   * 参数：无
   * 用途：当用户下拉页面时运行，通常用于刷新页面数据
   */
  onPullDownRefresh() {
    // 暂无操作
  },

  /**
   * 页面上拉触底事件的处理函数
   * 参数：无
   * 用途：当用户上拉页面到底部时运行，通常用于加载更多数据
   */
  onReachBottom() {
    // 暂无操作
  },

  /**
   * 用户点击右上角分享
   * 参数：无
   * 用途：配置页面的分享信息，如分享标题、图片等
   */
  onShareAppMessage() {
    // 暂无操作
  }
})