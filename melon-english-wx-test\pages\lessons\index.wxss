/* 页面容器 */
.container {
  display: flex;
  flex-direction: column;
  background-color: #f8f8f8;
  height: 100%;
  padding: 20px;
}

/* 顶部区域 */
.header {
  text-align: center;
  margin-bottom: 20px;
}

.course-name {
  font-size: 24px;
  font-weight: bold;
  margin-bottom: 10px;
}

.progress {
  font-size: 14px;
  color: #666;
}

/* 课程表区域 */
.course-grid {
  display: flex;
  flex-wrap: wrap;
  justify-content: space-between;
}

.course-card {
  width: 30%;
  background-color: #fff;
  border-radius: 10px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  margin-bottom: 15px;
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 10px;
}

/* 卡片上部分：数字 */
.card-top {
  font-size: 18px;
  font-weight: bold;
  color: #333;
}

/* 卡片中间部分：Logo */
.card-middle {
  width: 40px;
  height: 40px;
  margin: 10px 0;
}

.card-middle image {
  width: 100%;
  height: 100%;
}

/* 卡片下部分：文案 */
.card-bottom {
  font-size: 14px;
  color: #666;
}