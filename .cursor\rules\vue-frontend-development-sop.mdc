---
description: 
globs: 
alwaysApply: false
---
### **Vue 前端项目新页面开发标准流程 (SOP)**

**文档目的:** 本文档旨在为 `vue-frontend` 项目建立一个清晰、统一的新页面开发流程。所有开发者，尤其是从传统 WordPress 后端开发转来的新成员，都应遵循此流程进行开发，以确保代码的结构一致性和可维护性。

**核心架构理念:** 我们的项目采用"前后端分离"架构。WordPress ([shuimitao.online](mdc:wp-content/themes/shuimitao.online)) 担任后端，仅负责提供数据接口(API)。前端 ([vue-frontend](mdc:vue-frontend)) 项目则是一个独立的单页应用(SPA)，负责所有用户界面的展示和交互。**本流程完全聚焦于前端部分的开发。**

---

### **第一部分：核心开发流程 (抽象步骤)**

在本项目中，创建一个新页面需要遵循以下三个核心步骤。这保证了页面的"组件化"、"可访问性"和"可导航性"。

#### **步骤 1: 创建视图组件 (View Component)**

"视图组件"就是页面的本体。它是一个 `.vue` 后缀的单文件，内部封装了该页面所需的一切：HTML结构、JavaScript逻辑和CSS样式。

*   **存放位置**: 所有页面级别的组件都必须存放在 [`src/views/`](mdc:vue-frontend/src/views) 目录下。
*   **命名规范**: 文件名使用大驼峰命名法 (PascalCase)，并以 `View` 作为后缀，例如 `MyProfileView.vue` 或 `SettingsView.vue`。
*   **内部结构**: 每个视图组件都包含三个基本部分：
    *   `<template>`: 用于编写该页面的 **HTML 结构**。
    *   `<script setup>`: 用于编写该页面的 **JavaScript 逻辑**，例如处理用户点击、调用API获取数据、定义变量等。
    *   `<style scoped>`: 用于编写 **仅对此页面生效的 CSS 样式**。`scoped` 属性确保样式不会"泄露"出去影响到其他页面。

#### **步骤 2: 注册页面路由 (Route Registration)**

创建了页面文件后，需要告诉整个应用"这个页面的访问地址是什么"。这个过程叫做"注册路由"，它相当于在 WordPress 中设置一个固定链接规则。

*   **配置文件**: 所有的路由规则都集中在 [`src/router/index.js`](mdc:vue-frontend/src/router/index.js) 这一个文件中进行管理。
*   **操作方法**:
    1.  在 `index.js` 文件顶部，使用 `import` 导入你刚刚创建的视图组件。
    2.  在 `routes` 数组中，添加一个新的 JavaScript 对象，用于定义新页面的访问规则。
*   **规则对象**: 每个路由对象至少包含三个关键属性：
    *   `path`: 定义页面的 URL 路径，例如 `'/my-profile'`。
    *   `name`: 为路由指定一个唯一的英文名称，例如 `'my-profile'`，便于程序内部调用。
    *   `component`: 指定该路径对应要显示的视图组件。

#### **步骤 3: 创建导航链接 (Navigation)**

最后，你需要在应用的某个地方提供一个链接，让用户可以点击并跳转到你新创建的页面。

*   **链接组件**: 在 Vue Router 中，我们**不使用**传统的 `<a>` 标签进行内部页面跳转，而是使用一个名为 `<RouterLink>` 的专用组件。
*   **使用方法**: 在任何页面的 `<template>` 中，使用 `<RouterLink>` 并通过 `to` 属性指定目标路径。
    *   `to="/your-path"`: 直接链接到在 `index.js` 中定义的路径。
*   **优点**: `<RouterLink>` 能实现"无刷新跳转"。它不会让浏览器重新加载整个网页，而是在当前页面内部平滑地切换组件内容，提供了更好的用户体验。

---

### **第二部分：实例演练：创建一个新的"关于我们"页面**

现在，我们通过一个具体的例子来实践以上流程。假设我们要创建一个"关于我们"页面，它的访问路径是 `/about`。

#### **演练步骤 1: 创建 `AboutView.vue` 组件**

1.  **位置与命名**: 在 [`src/views/`](mdc:vue-frontend/src/views) 目录下创建一个新文件，名为 `AboutView.vue`。

2.  **编写内容**: 向 [`AboutView.vue`](mdc:vue-frontend/src/views/AboutView.vue) 文件中添加以下代码：

    ```vue
    <template>
      <div class="about-container">
        <h1 class="title">关于我们</h1>
        <p class="content">
          我们是一个致力于提供高质量在线英语教育的团队。
        </p>
        <!-- 我们在这里添加一个返回课程列表的链接 -->
        <RouterLink to="/" class="back-link">返回课程列表</RouterLink>
      </div>
    </template>

    <script setup>
    // 导入 RouterLink 组件，以便在模板中使用
    import { RouterLink } from 'vue-router';
    import { onMounted } from 'vue';

    // onMounted 是一个生命周期钩子，功能类似于 WordPress 中的 `wp_enqueue_scripts` 执行完毕后的前端脚本
    // 或者 jQuery 的 `$(document).ready()`，它会在页面组件加载到屏幕上后执行。
    onMounted(() => {
      console.log(""关于我们"页面已成功加载。");
    });
    </script>

    <style scoped>
    .about-container {
      padding: 2rem;
      text-align: center;
      background-color: #fff;
      max-w-md;
      mx-auto;
    }
    .title {
      font-size: 1.5rem;
      font-weight: 600;
      margin-bottom: 1rem;
    }
    .content {
      color: #666;
      margin-bottom: 2rem;
    }
    .back-link {
      color: #EA5960;
      text-decoration: none;
    }
    .back-link:hover {
      text-decoration: underline;
    }
    </style>
    ```

#### **演练步骤 2: 在 `router/index.js` 中注册路由**

1.  **打开文件**: [`src/router/index.js`](mdc:vue-frontend/src/router/index.js)。

2.  **修改代码**:
    *   导入 `AboutView.vue`。
    *   在 `routes` 数组中添加新的路由对象。

    ```javascript
    import { createRouter, createWebHistory } from 'vue-router'
    import CoursesView from '../views/CoursesView.vue'
    // 导入新创建的"关于我们"视图
    import AboutView from '../views/AboutView.vue'

    const router = createRouter({
      history: createWebHistory(import.meta.env.BASE_URL),
      routes: [
        {
          path: '/',
          name: 'courses',
          component: CoursesView
        },
        // 添加新的路由规则
        {
          path: '/about',
          name: 'about',
          component: AboutView
        }
      ]
    })

    export default router
    ```

#### **演练步骤 3: 在课程页面底部添加一个导航链接**

为了能访问到"关于我们"页面，我们在课程页面底部临时加一个入口。

1.  **打开文件**: [`src/views/CoursesView.vue`](mdc:vue-frontend/src/views/CoursesView.vue)。
2.  **修改模板**: 在底部导航栏旁边添加一个链接。

    ```html
    <template>
      <!-- ... 页面其他内容 ... -->
      <div class="fixed inset-x-0 bottom-0 ...">
          <!-- ... 底部导航栏 ... -->
      </div>

      <!-- 在页面底部临时添加一个入口 -->
      <div class="text-center p-4 pb-20">
          <RouterLink to="/about">关于我们</RouterLink>
      </div>
    </template>

    <script setup>
    // 确保 RouterLink 已被导入或全局可用
    import { RouterLink } from 'vue-router';
    // ... 其他脚本内容 ...
    </script>

    <!-- ... 样式 ... -->
    ```

**演练完成!**

现在，当你运行项目并访问课程页面时，会看到一个"关于我们"的链接。点击它，页面内容会平滑地切换到 `AboutView.vue` 的内容，并且浏览器地址栏的路径会变为 `/about`。

通过遵循这份文档，团队中的每一位成员都能以统一和规范的方式为项目贡献新的页面。

---

### **第三部分：处理复杂页面与组件化 (进阶)**

当一个页面（视图）中的某个区块逻辑变得复杂、动态效果增多时，如果把所有代码都堆在一个视图文件里，会使其变得臃肿且难以维护。此时，我们必须进行**组件化拆分**。

**核心思想：** 将复杂的页面看作一个乐高模型，**视图 (View)** 是整个模型，而 **组件 (Component)** 则是构成模型的、可复用的"积木块"。

*   **视图 (`src/views/`)**: 负责组合多个组件，构建完整的页面。
*   **组件 (`src/components/`)**: 负责单一、具体的功能，如用户头像、课程卡片、搜索框等。

#### **进阶流程：从复杂区块到独立组件**

##### **步骤 A: 识别与创建组件**

当页面中的一个 `<div>` 块功能内聚且复杂（例如：需要自己加载数据、有多个交互事件、有复杂的显示/隐藏逻辑），就应该将它提取成一个组件。

*   **存放位置**: 所有可复用的 UI 组件都必须存放在 [`src/components/`](mdc:vue-frontend/src/components) 目录下。
*   **命名规范**: 文件名使用大驼峰命名法 (PascalCase)，并清晰地描述其功能，例如 `UserInfoCard.vue`, `SearchBar.vue`。

##### **步骤 B: 在父组件 (页面) 中使用**

创建好组件后，在原来的页面（父组件）中像使用普通 HTML 标签一样使用它。

1.  在父组件的 `<script setup>` 区域，`import` 你刚刚创建的组件。
2.  在父组件的 `<template>` 区域，直接写入组件的标签，例如 `<UserInfoCard />`。

##### **步骤 C: 实现父子组件通信 (关键！)**

组件是独立的，那么页面（父）如何将数据传递给组件（子）？组件（子）又如何通知页面（父）发生了某个事件（比如用户点击了按钮）？这就需要"组件通信"。

*   **从父到子：使用 `props` 传递数据**
    *   **概念**: `props` 就像函数的参数。父组件通过 `props` 把动态数据"喂"给子组件。
    *   **子组件 (接收方)**: 在自己的 `<script setup>` 中使用 `defineProps` 来声明它能接收哪些数据。
    *   **父组件 (发送方)**: 在使用子组件的标签时，像 HTML 属性一样把数据传递过去，但属性名前要加一个冒号 `:`，表示这是一个动态数据。

*   **从子到父：使用 `emits` 发送事件**
    *   **概念**: `emits` 就像子组件在"喊话"。当子组件内部发生某件事（如点击），它会"喊"一个特定事件的名称，父组件可以"听"到这个事件并做出反应。
    *   **子组件 (发送方)**: 在自己的 `<script setup>` 中使用 `defineEmits` 声明它会喊哪些"口号"，然后用 `emit('口号', ...)` 来实际执行喊话。
    *   **父组件 (监听方)**: 在使用子组件的标签时，使用 `@` 符号来监听子组件的"喊话"，并指定一个函数来处理。

---

#### **进阶实例：将"我的"页面中的用户信息卡片拆分成组件**

假设我们的"我的"页面 (`MeView.vue`) 中，用户信息展示区变得非常复杂。

##### **演练步骤 A: 创建 `UserInfoCard.vue` 组件**

1.  **位置与命名**: 在 `src/components/` 目录下创建一个新文件，名为 `UserInfoCard.vue`。

2.  **编写内容**:

    ```vue
    <!-- 文件路径: src/components/UserInfoCard.vue -->
    <template>
      <div class="user-card">
        <img :src="avatarUrl" alt="用户头像" class="avatar">
        <h2 class="username">{{ username }}</h2>
        <p v-if="isVip" class="vip-status">VIP会员</p>
        <p v-else class="normal-status">普通用户</p>
        <!-- 当按钮被点击时，通过 emit 'renew-vip' 事件通知父组件 -->
        <button @click="emit('renew-vip')" class="renew-button">立即续费</button>
      </div>
    </template>

    <script setup>
    import { defineProps, defineEmits } from 'vue';

    // 1. 使用 defineProps 声明：这个组件需要从父组件接收三个数据
    const props = defineProps({
      username: String, // 需要一个字符串类型的用户名
      avatarUrl: String, // 需要一个字符串类型的头像URL
      isVip: Boolean     // 需要一个布尔类型的VIP状态
    });

    // 2. 使用 defineEmits 声明：这个组件可能会触发一个名为 'renew-vip' 的事件
    const emit = defineEmits(['renew-vip']);
    </script>

    <style scoped>
    .user-card { border: 1px solid #eee; padding: 1rem; text-align: center; }
    .avatar { width: 80px; height: 80px; border-radius: 50%; }
    .username { margin: 0.5rem 0; }
    .vip-status { color: gold; }
    .renew-button { margin-top: 1rem; padding: 0.5rem 1rem; background-color: #EA5960; color: white; border: none; }
    </style>
    ```

##### **演练步骤 B & C: 在 `MeView.vue` 中使用并与之通信**

现在，我们的 `MeView.vue` 页面可以变得非常简洁。

```vue
<!-- 文件路径: src/views/MeView.vue (假设已创建) -->
<template>
  <div class="me-page">
    <h1>我的个人中心</h1>
    
    <!-- 在模板中像HTML标签一样使用组件 -->
    <!-- 通过 : 传递 props, 通过 @ 监听 emits -->
    <UserInfoCard 
      :username="user.name"
      :avatar-url="user.avatar"
      :is-vip="user.isVip"
      @renew-vip="handleRenewVip" 
    />

    <!-- 页面的其他内容... -->
  </div>
</template>

<script setup>
import { reactive } from 'vue';
// 1. 导入刚刚创建的组件
import UserInfoCard from '../components/UserInfoCard.vue';

// 2. 准备要传递给子组件的数据 (这里用假数据模拟)
const user = reactive({
  name: '哈密瓜',
  avatar: 'https://www.shuimitao.online/wp-content/uploads/2024/05/cropped-shuimitao_online_logo.png',
  isVip: true
});

// 3. 定义一个函数来处理子组件 "喊" 出来的 renew-vip 事件
function handleRenewVip() {
  console.log("接收到来自子组件的续费请求！");
  // 在这里可以执行跳转到支付页面等逻辑
  alert("正在跳转到续费页面...");
}
</script>

<style scoped>
.me-page { padding: 1rem; }
</style>
```

**总结：** 通过遵循这个进阶流程，您的团队可以构建出层次清晰、高度可维护的前端应用。当遇到任何功能内聚的复杂区块时，第一反应就应该是："**我能把它抽成一个组件吗？**"


