/* detail.wxss */
.header {
  display: flex;
  flex-direction: column;
  align-items: flex-start;
  border-radius: 0 0 5px 5px;
}

.course-title {
  font-size: 24px;
  font-weight: bold;
}

.progress {
  font-size: 16px;
}
.body {
  padding: 10px;
}
.schedule-title {
  display: flex;
  align-items: center; /* 垂直居中对齐 */
  margin-top: 20px;
  font-size: 20px;
  font-weight: bold;
}

.title-decoration {
  width: 3px; /* 装饰条宽度 */
  height: 20px; /* 装饰条高度 */
  border-radius: 1px; /* 圆弧角度 */
  background-color: #f74655; /* 背景颜色 */
  margin-right: 10px; /* 与标题的间距 */
}

.schedule {
  display: flex;
  flex-wrap: wrap; /* 允许换行 */
  justify-content: space-between; /* 卡片之间的间距 */
  padding: 10px;
}
.card {
  width: calc(33.33% - 15px); /* 每个卡片占据约33.33%的宽度，减去间距 */
  margin-bottom: 15px; /* 卡片之间的间距 */
  border: 1px solid #ccc; /* 卡片边框 */
  border-radius: 5px; /* 圆角 */
  overflow: hidden; /* 隐藏超出部分 */
  box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1); /* 阴影效果 */
  background-color: #fff; /* 背景色 */
}

.card-title {
  font-size: 16px;
  font-weight: bold;
  padding: 10px;
  text-align: center; /* 标题居中 */
}

.card-image {
  width: 100%; /* 图片宽度100% */
  height: 150px; /* 固定高度 */
}

.card-description {
  font-size: 14px;
  padding: 10px;
  text-align: center; /* 描述居中 */
}