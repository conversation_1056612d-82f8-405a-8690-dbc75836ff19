/**index.wxss**/
page {
  height: 100%;
  background-color: #f8f8f8;
}

.container {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 40rpx;
  box-sizing: border-box;
  min-height: 100%;
}

.logo-container {
  margin-bottom: 40rpx;
  margin-top: 60rpx;
}

.logo {
  width: 160rpx;
  height: 160rpx;
  border-radius: 80rpx;
  border: 4rpx solid #07c160;
  box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.1);
}

.title {
  font-size: 40rpx;
  font-weight: bold;
  margin-bottom: 80rpx;
  color: #333;
}

.login-btn {
  width: 80%;
  height: 90rpx;
  line-height: 90rpx;
  margin-bottom: 40rpx;
  border-radius: 45rpx;
  background-color: #07c160 !important;
  color: white !important;
  font-size: 34rpx;
  font-weight: bold;
  box-shadow: 0 6rpx 20rpx rgba(7, 193, 96, 0.3);
}

.error-msg {
  color: #e64340;
  font-size: 28rpx;
  margin: 20rpx 0;
  padding: 20rpx;
  background: #fff6f6;
  border-radius: 8rpx;
  width: 80%;
  text-align: center;
}

.api-response {
  width: 90%;
  margin-top: 40rpx;
  border-radius: 16rpx;
  background-color: #fff;
  box-shadow: 0 2rpx 12rpx rgba(0, 0, 0, 0.1);
  padding: 30rpx;
}

.response-title {
  font-size: 32rpx;
  font-weight: bold;
  margin-bottom: 20rpx;
  color: #333;
}

.success-tag {
  background-color: #07c160;
  color: white;
  display: inline-block;
  padding: 6rpx 20rpx;
  border-radius: 30rpx;
  font-size: 24rpx;
  margin-bottom: 30rpx;
}

.error-tag {
  background-color: #e64340;
  color: white;
  display: inline-block;
  padding: 6rpx 20rpx;
  border-radius: 30rpx;
  font-size: 24rpx;
  margin-bottom: 20rpx;
}

.data-item {
  display: flex;
  justify-content: space-between;
  margin-bottom: 16rpx;
  padding: 16rpx 0;
  border-bottom: 1rpx solid #eee;
}

.label {
  color: #666;
  font-size: 28rpx;
}

.value {
  color: #333;
  font-size: 28rpx;
  font-weight: bold;
}

.course-title {
  font-size: 30rpx;
  font-weight: bold;
  margin: 30rpx 0 20rpx;
  color: #333;
}

.course-list {
  width: 100%;
}

.course-item {
  padding: 20rpx;
  margin-bottom: 20rpx;
  background-color: #f8f8f8;
  border-radius: 8rpx;
}

.course-header {
  display: flex;
  margin-bottom: 16rpx;
}

.course-image {
  width: 120rpx;
  height: 120rpx;
  border-radius: 8rpx;
  margin-right: 16rpx;
}

.course-info {
  flex: 1;
  display: flex;
  flex-direction: column;
  justify-content: center;
}

.course-name {
  font-size: 30rpx;
  font-weight: bold;
  color: #333;
  display: block;
  margin-bottom: 10rpx;
}

.course-difficulty {
  font-size: 24rpx;
  color: #666;
  display: block;
  margin-bottom: 10rpx;
}

.course-desc {
  font-size: 26rpx;
  color: #999;
  display: block;
  margin-bottom: 10rpx;
}

.course-id {
  font-size: 22rpx;
  color: #bbb;
  display: block;
  margin-top: 8rpx;
}

.error-message {
  font-size: 28rpx;
  color: #e64340;
  margin-bottom: 10rpx;
}

.error-detail {
  font-size: 26rpx;
  color: #999;
  white-space: pre-wrap;
  word-break: break-all;
}

.api-test-btn {
  width: 80%;
  height: 90rpx;
  line-height: 90rpx;
  margin-bottom: 30rpx;
  border-radius: 45rpx;
  background-color: #1E90FF !important;
  color: white !important;
  font-size: 34rpx;
  font-weight: bold;
  box-shadow: 0 6rpx 20rpx rgba(30, 144, 255, 0.3);
}
