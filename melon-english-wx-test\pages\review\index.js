// pages/review/index.js
Page({
  /**
   * 页面的初始数据
   */
  data: {
    reviewList: [
      {
        lesson: 42,
        courseName: "吃透新概念一册(上)",
      },
      {
        lesson: 41,
        courseName: "吃透新概念一册(上)",
      },
      {
        lesson: 40,
        courseName: "吃透新概念一册(上)",
      },
      {
        lesson: 39,
        courseName: "吃透新概念一册(上)",
      },
      {
        lesson: 29,
        courseName: "零基础发音课",
      },
    ],
  },

  /**
   * 生命周期函数--监听页面加载
   */
  onLoad: function (options) {},

  /**
   * 生命周期函数--监听页面初次渲染完成
   */
  onReady: function () {},

  /**
   * 生命周期函数--监听页面显示
   */
  onShow: function () {},

  /**
   * 生命周期函数--监听页面隐藏
   */
  onHide: function () {},

  /**
   * 生命周期函数--监听页面卸载
   */
  onUnload: function () {},

  /**
   * 页面相关事件处理函数--监听用户下拉动作
   */
  onPullDownRefresh: function () {},

  /**
   * 页面上拉触底事件的处理函数
   */
  onReachBottom: function () {},

  /**
   * 用户点击右上角分享
   */
  onShareAppMessage: function () {},
});
