---
description: 
globs: 
alwaysApply: true
---
# 代码注释标准

**目标:** 统一代码库中的注释风格，提高代码的可读性和可维护性。

**通用指南:** 请结合 @cursor_rules.mdc 中的通用规则执行以下步骤（例如，使用中文回复，使用反引号包裹标识符等）。

**!!! 严格执行:** AI 在编写或修改代码时，必须遵循以下注释标准添加注释。注释的目的是解释代码意图，而不是简单重复代码本身。

## 注释类型

主要使用两种类型的注释：块注释和行内注释。

### 1. 块注释 (Block Comments)

用于注释整个文件、类、函数、方法或复杂的代码块。

*   **位置:** 紧邻在被注释代码块的正上方。
*   **语法:** 使用目标语言的标准块注释语法 (例如 PHP: `/** ... */`, Python: `""" ... """`, JavaScript/TypeScript: `/** ... */`)。
*   **结构与内容 (针对文件、类、函数、方法):**
    *   **@功能概述:** (必需) 1-3 句话清晰描述该代码块的主要功能、目的和核心逻辑。
    *   **@输入参数 (@param):** (仅函数/方法适用) 对每个重要参数，按格式列出：`@param {类型} $参数名 - 描述`。说明参数用途、来源或约束。
    *   **@输出/返回值 (@return):** (仅函数/方法适用) 描述返回值的 `{类型}` 和含义。若无直接返回值，可描述其对系统的主要影响（如数据库更新、状态变更等）。
    *   **(可选) @执行流程:** (仅函数/方法适用) 简要列出该函数/方法内部关键的、高层级的处理步骤和分支逻辑。
*   **结构与内容 (针对函数/方法内部的复杂逻辑代码段):**
    *   当函数或方法内部包含特别复杂、包含多个清晰步骤的逻辑段时，除了必要的行内注释和详细的追踪日志外，可以考虑在该逻辑段的正上方添加块注释以增强可读性。
    *   **@分步说明:** (如果使用块注释) 清晰地、分步骤地列出该代码段执行的各个主要环节，并推荐使用**层级化序号** (例如 1., 1.1., 1.1.1., 2.) 进行组织。详细描述每个步骤的目的、关键逻辑判断或重要操作。
    *   **目的:** 帮助其他开发者快速理解复杂区段的内部工作流程。这些步骤号应与该代码段内部的追踪日志中的步骤号相对应（见下方"调试与追踪日志"部分）。
    *   **示例 (概念性，用于函数内部复杂代码段的块注释):**
        ```php
        // ... (位于函数内部) ...

        /**
         * @分步说明: 用户权限校验与数据预处理模块
         * 
         *   1.  权限等级检查
         *       1.1. 确认用户是否至少为 'editor' 角色。
         *       1.2. 若非 'editor'，则检查是否拥有特定 'manage_custom_data' capability。
         *   2.  输入数据清洗
         *       2.1. 对 $input_array 中的 'description' 字段执行 HTML 实体移除。
         *       2.2. 规范化 'date_field' 为 'YYYY-MM-DD' 格式。
         *   3.  敏感词过滤 (如果适用)
         *       3.1. 调用 sensitive_word_filter() 对清洗后的 'description' 进行检查。
         */
        // --- 复杂代码段开始 ---
        // 步骤 1.1: 角色检查 (对应日志中的步骤编号)
        error_log($log_prefix . '[内部流程 1.1] 开始角色检查...');
        if (user_can($user_id, 'editor')) {
            // ... 相关逻辑 ...
        } else {
            // 步骤 1.2: Capability 检查 (对应日志中的步骤编号)
            error_log($log_prefix . '[内部流程 1.2] 用户非editor, 检查 capability...');
            if (!user_can($user_id, 'manage_custom_data')) {
                // ... 权限不足处理 ...
            }
        }
        // 步骤 2.1: 数据清洗 - description (对应日志中的步骤编号)
        error_log($log_prefix . '[内部流程 2.1] 开始数据清洗 description...');
        // ...
        // --- 复杂代码段结束 ---
        ```
*   **格式:**
    *   块注释内部保持统一缩进。
    *   推荐使用 `@` 标签（如 `@param`, `@return`, `@分步说明`）来结构化信息。
    *   对于 PHP/JS/TS，推荐使用 JSDoc/PHPDoc 风格的星号对齐。

**块注释示例 (PHP):**
```php
/**
 * @功能概述: 处理用户登录逻辑，验证凭据并创建会话。
 *           如果验证成功，更新最后登录时间。
 *
 * @param {string} $username - 用户输入的用户名。
 * @param {string} $password - 用户输入的密码。
 * @param {bool}   $remember - 是否记住登录状态。
 *
 * @return {bool|WP_Error} 登录成功返回 true，失败返回 WP_Error 对象。
 */
function handle_user_login($username, $password, $remember) {
    // ... 函数实现 ...
}
```

### 2. 行内注释 (Inline Comments)

用于解释单行或小段代码。

*   **位置:** 通常位于代码行的末尾，或在需要解释的代码行的上方。
*   **语法:** 使用目标语言的单行注释语法 (例如 `// ...` 或 `# ...`)。
*   **使用场景:**
    *   解释复杂的表达式、算法或非显而易见的逻辑。
    *   说明特定选择或"魔法值"的原因。
    *   标记临时的解决方案、`TODO` 或 `FIXME`。
    *   解释关键变量的含义（如果变量名本身不够清晰）。
*   **风格:**
    *   使用中文，简明扼要。
    *   避免注释显而易见的代码。
    *   与代码保持同步更新。

**行内注释示例 (PHP):**
```php
// 检查用户是否有编辑权限
if (!current_user_can('edit_posts')) {
    wp_die('您没有权限执行此操作。'); // 中断执行并显示错误信息
}

$retry_count = 3; // 设置最大重试次数
while ($retry_count > 0) {
    // ... 重试逻辑 ...
    $retry_count--; // 递减重试计数器
}
```

## 通用原则

*   **清晰胜于冗余:** 优先编写清晰、自解释的代码，注释作为辅助。
*   **关注"为什么":** 注释不仅要说明代码"做什么"，更要解释"为什么"这么做（如果逻辑不明显）。
*   **保持更新:** 修改代码时，务必同步更新相关注释。
*   **禁止修改代码:** 此规则仅要求添加注释，不允许修改任何现有代码逻辑。

---

## 调试与追踪日志 (Debugging and Tracing Logs)

除了上述规范化的代码块注释和行内注释外，在开发和调试过程中，使用详细的追踪日志至关重要。这有助于理解代码的实际执行流程、诊断问题和监控关键操作。

*   **目标:**
    *   清晰展示代码执行的关键步骤和分支。
    *   记录重要的数据状态（输入、中间处理、输出）。
    *   在出现问题时，能够快速定位错误来源和上下文。
*   **主要工具 (PHP):** `error_log()` 函数。
*   **日志位置:** 通常记录到服务器配置的错误日志文件中 (例如 `debug.log`)。

### 日志结构化最佳实践

1.  **统一模块/处理器前缀 (Consistent Module/Handler Prefix):**
    *   为每个主要模块、类或处理器函数定义一个独特且一致的日志前缀。
    *   **示例:** `[PaymentVIPProcessorHandler]`, `[UserAuthentication]`, `[CourseDataImport]`
    *   **目的:** 便于在大量日志中筛选和识别特定模块的输出。

2.  **分步编号与清晰描述 (Step Numbering and Clear Descriptions):**
    *   对一个复杂流程中的关键执行步骤使用层级化编号。
    *   **重要：日志中的步骤编号应与代码注释中（无论是函数/方法级别的 `@执行流程`，还是内部复杂代码块的 `@分步说明` 或详细的行内注释）对应的步骤编号保持一致或清晰关联。** 这确保了通过阅读日志可以快速定位到代码中相应的解释和逻辑。
    *   **示例:**
        ```php
        error_log($log_prefix . '[步骤 1] 开始处理VIP激活请求。');
        // ...
        error_log($log_prefix . '[步骤 2] API Key 验证。');
        error_log($log_prefix . '[步骤 2.1] 获取接收到的API Key。');
        // ...
        error_log($log_prefix . '[步骤 2.5.1] API Key 验证失败：...');
        ```
    *   **目的:** 清晰展示执行路径，尤其在有多个分支或条件判断时，能快速看出代码走了哪个分支，以及在哪里中断。

3.  **记录关键数据状态 (Logging Key Data States):**
    *   **输入参数:** 记录从请求或函数参数中获取的核心数据及其类型。
        ```php
        error_log($log_prefix . "[步骤 1.1] 接收到的参数详情 - User ID: {$user_id}, Post ID: {$post_id}");
        $params = $request->get_json_params();
        error_log($log_prefix . "[步骤 1.2] 解析后的 JSON 参数: " . print_r($params, true));
        ```
    *   **重要变量:** 记录影响逻辑走向或重要的中间计算结果。
    *   **复杂数据结构:** 对于数组或对象，使用 `print_r($variable, true)` 或自定义的JSON序列化函数（如 `json_encode($variable, JSON_UNESCAPED_UNICODE)`）来记录，以保证可读性。
        ```php
        // 假设 $decoded_order_desc 是一个数组
        error_log($log_prefix . "[步骤 *******] 参数 'order_description' 解码成功 (JSON -> Array): " . print_r($decoded_order_desc, true));
        // 记录数组内部结构和类型
        foreach ($decoded_order_desc as $desc_key => $desc_value) {
            $desc_type = gettype($desc_value);
            $desc_log_value = (is_array($desc_value) || is_object($desc_value)) ? print_r($desc_value, true) : $desc_value;
            error_log($log_prefix . "[步骤 *******.1]   order_description 内参数名: [{$desc_key}], 类型: [{$desc_type}], 值: {$desc_log_value}");
        }
        ```
    *   **目的:** 验证数据在处理过程中的正确性，以及在出错时了解当时的数据状态。

4.  **上下文信息 (Contextual Information):**
    *   在日志中包含关键的上下文标识，如用户ID、订单号、会话ID等。
    *   **示例:** `error_log($log_prefix . "[步骤 3] 用户ID: {$user_id} 尝试更新配置。配置项: {$config_key}");`
    *   **目的:** 能够将单条日志与特定的用户会话或业务实体关联起来。

5.  **明确记录操作结果 (Logging Operation Outcomes):**
    *   **成功标记:** 清晰记录关键操作的成功完成。
        ```php
        error_log($log_prefix . "[步骤 *******] VIP服务处理成功：用户ID: {$user_id_to_activate} 的VIP已成功增加/更新 {$vip_duration_days} 天。");
        ```
    *   **失败/错误标记:** 记录操作失败、参数验证不通过或任何不符合预期的情况。
        ```php
        error_log($log_prefix . '[步骤 2.3.1] API Key 验证失败：请求中未提供 API Key。');
        error_log($log_prefix . "[步骤 5.1.2] 错误：提取的 wp_user_id (...) 不是一个有效的正整数。");
        ```
    *   **异常捕获:** 在 `try-catch` 块中，务必记录异常的详细信息，包括异常消息和堆栈跟踪。
        ```php
        try {
            // ... 业务逻辑 ...
        } catch (Exception $e) {
            error_log($log_prefix . "[API_EXCEPTION] :: " . __FUNCTION__ . " for User ID: {$user_id}. Exception: " . $e->getMessage() . "\nStack Trace:\n" . $e->getTraceAsString());
            // ... 返回错误响应 ...
        }
        ```
    *   **目的:** 快速识别操作是否成功，以及失败的具体原因和位置。

6.  **日志标记/级别 (Log Markers/Levels - Conceptual):**
    *   虽然 `error_log` 本身不直接支持日志级别，但可以在日志前缀或消息中使用明确的标记来区分日志类型。
    *   **示例:** `[DEBUG]`, `[INFO]`, `[WARN]`, `[ERROR]`, `[CRITICAL]`, `[API_REQUEST_START]`, `[API_VALIDATION_ERROR]`, `[API_SUCCESS_RESPONSE]`
    *   **目的:** 提高日志的可读性和可筛选性，尤其是在分析大量日志时。

### 示例：结合使用的日志片段

```php
function handle_complex_operation(WP_REST_Request $request) {
    $log_prefix = '[ComplexOperationHandler] ';
    $user_id = get_current_user_id();
    $params = $request->get_params();

    error_log($log_prefix . "[步骤 1][User:{$user_id}] 开始处理复杂操作。原始参数: " . print_r($params, true));

    // 1.1 参数提取与验证
    $item_id = $params['item_id'] ?? null;
    if (empty($item_id) || !is_numeric($item_id)) {
        error_log($log_prefix . "[步骤 1.1.1][User:{$user_id}][VALIDATION_ERROR] item_id 无效或缺失。收到的值: " . print_r($item_id, true));
        return new WP_Error('invalid_item_id', 'Item ID 无效。', ['status' => 400]);
    }
    $item_id = intval($item_id);
    error_log($log_prefix . "[步骤 1.1.2][User:{$user_id}] 参数 item_id 验证通过: {$item_id}");

    try {
        // ... (核心业务逻辑) ...
        error_log($log_prefix . "[步骤 2][User:{$user_id}][Item:{$item_id}] 核心逻辑A处理完成。");

        // ... (更多逻辑) ...
        $result_data = ['status' => 'completed', 'item_processed' => $item_id];
        error_log($log_prefix . "[步骤 3][User:{$user_id}][Item:{$item_id}][SUCCESS] 复杂操作成功完成。结果: " . json_encode($result_data));
        
        return new WP_REST_Response(['success' => true, 'data' => $result_data], 200);

    } catch (SpecificDomainException $sde) {
        error_log($log_prefix . "[步骤 X.Y][User:{$user_id}][Item:{$item_id}][DOMAIN_EXCEPTION] 特定领域异常: " . $sde->getMessage() . "\nStack Trace:\n" . $sde->getTraceAsString());
        return new WP_Error('domain_error', $sde->getMessage(), ['status' => 422]);
    } catch (Exception $e) {
        error_log($log_prefix . "[步骤 X.Y][User:{$user_id}][Item:{$item_id}][UNEXPECTED_EXCEPTION] 未预期异常: " . $e->getMessage() . "\nStack Trace:\n" . $e->getTraceAsString());
        return new WP_Error('internal_server_error', '服务器内部错误。', ['status' => 500]);
    }
}
```

### 注意事项
*   **避免在生产环境记录过多冗余信息:** 调试日志应在开发和测试阶段详细记录，但在生产环境中可能需要调整日志级别或内容，避免日志文件过大或包含敏感信息（除非已做脱敏处理）。
*   **日志安全:** 确保日志文件受到适当的保护，防止未授权访问。
*   **定期审查和清理:** 定期审查日志配置和内容，清理不再需要的调试信息。

请 AI 严格遵循以上规范添加注释和调试日志，以提高代码库的整体质量和可维护性。 