<!--index.wxml-->
<scroll-view class="scrollarea" scroll-y type="list">
  <view class="container">
    <!-- 用户信息区域 -->
    <view class="user-info">
      <view class="user-info-content">
        <image class="user-avatar" src="{{userInfo.avatarUrl}}" mode="aspectFill"></image>
        <view class="user-detail">
          <text class="user-name">{{userInfo.name}}</text>
          <text class="user-id">ID: {{userInfo.id}}</text>
          <block wx:if="{{userInfo.isVip}}">
            <text class="vip-expire">黑卡VIP期限：{{userInfo.vipExpire}}</text>
          </block>
          <block wx:else>
            <view class="vip-button" bindtap="goToPay">开通会员</view>
          </block>
        </view>
      </view>
    </view>

    <!-- 今日任务区域 -->
    <view class="task-section">
      <view class="task-container">
        <view class="task-header">
          <view class="left">
            <text>今日任务</text>
          </view>
          <text class="check-detail" bindtap="checkDetail">查看详情 ></text>
        </view>
        <view class="task-content">
          <text>学习1课（未完成）</text>
        </view>
      </view>
    </view>

    <!-- 学习历史区域 -->
    <view class="history-section">
      <view class="history-header">
        <image src="/assets/images/time.png" mode="aspectFit"></image>
        <text>学习历史</text>
      </view>
      <view class="book-list">
        <view class="book-item">
          <image src="/assets/images/course1.jpg" mode="aspectFit"></image>
          <text>吃透新概念一册(上)</text>
        </view>
        <view class="book-item">
          <image src="/assets/images/course1.jpg" mode="aspectFit"></image>
          <text>零基础发音课</text>
        </view>
        <view class="book-item">
          <image src="/assets/images/course1.jpg" mode="aspectFit"></image>
          <text>吃透新概念二册(下)</text>
        </view>
      </view>
    </view>

    <!-- 底部功能区域 -->
    <view class="function-section">
      <view class="function-item" bindtap="goToReview">
        <image src="/assets/images/review.png" mode="aspectFit"></image>
        <text>今日复习</text>
        <view class="red-dot"></view>
      </view>
      <view class="function-item" bindtap="goToSettings">
        <image src="/assets/images/setting.png" mode="aspectFit"></image>
        <text>设置</text>
      </view>
    </view>
  </view>
</scroll-view>