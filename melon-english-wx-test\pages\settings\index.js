import { request } from "../../utils/request";

Page({
  data: {
    isRemindOn: true,
  },

  onLoad() {
    // 获取复习提醒设置状态
    this.getRemindStatus();
  },

  // 获取复习提醒状态
  async getRemindStatus() {
    try {
      const res = await request.get("/get-remind-status");
      if (res.success) {
        this.setData({
          isRemindOn: res.isRemindOn,
        });
      }
    } catch (error) {
      console.error("获取提醒状态失败:", error);
    }
  },

  // 清空进度
  clearProgress() {
    wx.showModal({
      title: "提示",
      content: "确定要清空所有课程进度和复习计划吗？",
      async success(res) {
        if (res.confirm) {
          try {
            const result = await request.post("/clear-progress");
            if (result.success) {
              wx.showToast({
                title: "清空成功",
                icon: "success",
              });
            }
          } catch (error) {
            console.error("清空进度失败:", error);
          }
        }
      },
    });
  },

  // 切换复习提醒
  async onToggleRemind() {
    const newStatus = !this.data.isRemindOn;

    try {
      const res = await request.post("/update-remind-status", {
        isRemindOn: newStatus,
      });

      if (res.success) {
        this.setData({
          isRemindOn: newStatus,
        });
        wx.showToast({
          title: newStatus ? "已开启提醒" : "已关闭提醒",
          icon: "success",
        });
      }
    } catch (error) {
      console.error("更新提醒状态失败:", error);
    }
  },
});
