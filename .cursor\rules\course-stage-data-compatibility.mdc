---
description: 
globs: 
alwaysApply: true
---
# 课程阶段数据 (JSON) 兼容性处理规则

**目标:** 明确 WordPress 文章 (`post`) 中用于存储课程学习阶段 (Learning Stages) 的 JSON 数据的演变、处理逻辑和最终的标准化格式。

**背景:**

课程学习阶段数据定义了在线课程（例如通过微信小程序访问）中各个步骤的内容和类型。随着时间的推移，存储这些数据的 `post_meta` 字段和 JSON 结构发生了变化。`CourseDataHandler.php` 类负责处理这些不同版本的数据，并将其统一为标准格式。

**相关文件:**

*   `wp-content/themes/shuimitao.online/api/v1/models/CourseDataHandler.php`: 核心处理逻辑所在类。
*   `wp-content/themes/shuimitao.online/json/new_json.json`: 标准化后的 JSON 数据示例。

**数据来源与演变:**

`CourseDataHandler::processCourseStageData()` 方法按以下优先级尝试获取原始 JSON 数据：

1.  `course_stage_json_for_mini_program` (Meta Key): **目标格式**，存储已处理和标准化的 JSON 数据，供小程序直接使用。
2.  `course_learning_stage_json` (Meta Key): 较新的旧格式，通常与 `custom_post_template` 为 `single-20210604.php` 的文章关联。
3.  `course_learning_stage` (Meta Key): 最早的旧格式。

**处理逻辑 (`CourseDataHandler::processCourseStageData`)**

如果获取的数据不是 `course_stage_json_for_mini_program` 格式，`CourseDataHandler` 会执行以下主要处理步骤：

1.  **JSON 解码:**
    *   尝试直接解码 JSON 字符串。
    *   进行递归 `urldecode` 以处理可能存在的 URL 编码字符。
    *   包含错误处理和编码修复逻辑（如 `mb_convert_encoding`），以应对各种脏数据。
2.  **清理空值:** 移除值为空的字段（但保留值为 `'0'` 的字段）。
3.  **时间戳 (Timestamp) 处理:**
    *   确保每个阶段对象都有 `timestamp` 字段，没有则设为 `''`。
    *   将 `MM:SS.ms` 格式的时间戳转换为**毫秒 (milliseconds)** 的整数。
    *   如果格式无效或字段为空，时间戳暂时设为 `-1`。
    *   **回填无效时间戳:** 从后向前遍历，如果时间戳为 `-1`，则使用后一个有效时间戳减去 10ms 作为当前时间戳，确保时间戳大致递增。最终的时间戳是相对于课程音频开始时间的**毫秒数**。
4.  **冗余字段清理:** 删除旧格式中不再需要的字段，如 `review_go_num`, `purport`, `bindKnowledge`。
5.  **按 `stageType` 规范化:**
    *   **`begin`**: 如果不是数组的第一个元素，则将 `stageType` 强制改为 `transition`，清理 `headline`, `cat_name`, `brand_name` 等字段，并将 `showtext_2` 移到 `showtext_1`。第一个 `begin` 卡片保留 `showtext_1`, `showtext_2`, `cat_name`, `brand_name`, `img`。
    *   **`knowledgeHasImage` / `knowledge`**: 将旧的 `explain_1` 到 `explain_4` 字段合并为 HTML 段落 (`<p>`) 并存入 `rich_text` 字段。
    *   **`sentence`**: 只保留 `stageType`, `classifyType`, `timestamp`, `english_sentence`, `chinese_sentence`, `rich_text` 字段。
    *   **`word`**: 清理 `headline`；将 `explain_1` 到 `explain_4` 合并到 `rich_text`。
    *   **`wordchoice`**: 只保留 `stageType`, `classifyType`, `timestamp`, `headline`, `learn_word`, `word_explain`, `word_phonetic` 字段。
    *   **`lack`**: 如果 `english_sentence_has_dot` 或 `english_sentence_no_dot` 不存在，则根据 `english_sentence` 生成它们：
        *   `english_sentence_has_dot`: 英文句子，单词和标点符号之间用空格隔开。
        *   `english_sentence_no_dot`: 英文句子，移除所有标点符号（保留单引号），单词间用空格隔开。
        *   最终只保留 `stageType`, `chinese_sentence`, `classifyType`, `english_sentence`, `english_sentence_has_dot`, `english_sentence_no_dot`, `timestamp` 字段。
    *   **`spell`**: 只保留 `stageType`, `classifyType`, `headline`, `learn_word`, `word_explain`, `timestamp` 字段。
    *   **`voice`**: 只保留 `stageType`, `classifyType`, `question_stem_1`, `timestamp`, `headline` 字段。
    *   **`choice`**: 只保留 `stageType`, `classifyType`, `timestamp`, `headline`, `question_stem_1`, `question_stem_2`, `option_1`, `option_2`, `option_3`, `right_option` 字段。
6.  **图片 URL 处理:** 检查 `img` 字段，并尝试将其规范化为以当前站点 `site_url` 开头的完整 URL。
7.  **字符串清理:** 对 `english_sentence`, `english_sentence_has_dot`, `english_sentence_no_dot`, `word_phonetic`, `rich_text` 等字段应用 `stripslashes` 清理反斜杠。
8.  **重新编码:** 将处理后的 PHP 数组重新编码为 JSON 字符串 (使用 `JSON_UNESCAPED_UNICODE`)。
9.  **保存:** 将最终标准化的 JSON 字符串保存回 `course_stage_json_for_mini_program` meta 字段。

**标准化 JSON 格式 (`course_stage_json_for_mini_program`) 概述:**

*   是一个包含多个阶段对象的 JSON 数组。
*   每个对象代表课程的一个阶段（卡片）。
*   **`stageType`**: 区分卡片类型 (如 `begin`, `sentence`, `knowledge`, `word`, `choice`, `lack`, `spell`, `voice`, `transition`, `knowledgeHasImage`, `wordchoice`)。
*   **`classifyType`**: 通常为 `course` (内容卡片) 或 `exercise` (练习卡片)。
*   **`timestamp`**: 该阶段在课程音频中对应的**毫秒**时间点。
*   **其他字段**: 根据 `stageType` 的不同而变化，包含该阶段所需的具体内容（文本、图片 URL、问题、选项、单词信息等）。具体字段见 `new_json.json` 示例和上述规范化步骤。

**开发注意事项:**

*   当需要读取课程阶段数据时，应优先使用 `CourseDataHandler` 类，它会返回标准化的 `course_stage_json_for_mini_program` 格式的数据（在其 `course_learning_stage` 属性或 `toArray()` 方法的 `course_learning_stage` 键中）。
*   **避免直接读取旧的 meta key (`course_learning_stage_json`, `course_learning_stage`)**，除非明确知道需要处理原始或未标准化的数据。
*   任何涉及修改课程阶段数据结构或添加新 `stageType` 的开发，都需要更新 `CourseDataHandler::processCourseStageData()` 中的处理逻辑，并更新此规则文档。
*   修改 `CourseDataHandler` 时，务必考虑对现有数据的兼容性影响。
