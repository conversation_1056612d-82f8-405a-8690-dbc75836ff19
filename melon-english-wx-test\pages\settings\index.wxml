<view class="container">
  <!-- 课程进度与复习计划 -->
  <view class="settings-list">
    <view class="setting-item">
      <view class="setting-left">
        <text class="setting-title">课程进度与复习计划</text>
      </view>
      <view class="setting-right" bindtap="clearProgress">
        <text class="clear-text">清空</text>
      </view>
    </view>

    <!-- 开启/关闭复习提醒 -->
    <!-- <view class="setting-item">
      <view class="setting-left">
        <text class="setting-title">开启/关闭复习提醒</text>
      </view>
      <view class="setting-right" bindtap="onToggleRemind">
        <text class="status-text">{{isRemindOn ? '已开启' : '已关闭'}}</text>
        <image 
          class="toggle-icon" 
          src="/assets/images/{{isRemindOn ? 'toggle-on' : 'toggle-off'}}.png" 
          mode="aspectFit"
        ></image>
      </view>
    </view> -->
  </view>
</view> 