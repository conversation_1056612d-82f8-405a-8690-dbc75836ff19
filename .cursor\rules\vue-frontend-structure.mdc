---
description: 
globs: 
alwaysApply: false
---
### **`vue-frontend` 项目架构指南**

**文档目的:** 本文档是 `vue-frontend` 单页应用 (SPA) 的核心架构说明。它旨在为所有开发者提供关于技术选型、文件结构和推荐开发模式的清晰指引，确保代码的长期可维护性和可扩展性。

---

### **1. 核心理念与技术栈**

本项目采用**前后端分离 (Decoupled)** 架构。WordPress 仅作为 **Headless CMS**，通过 REST API ([shuimitao.online](mdc:wp-content/themes/shuimitao.online)) 提供数据。`vue-frontend` 是一个完全独立的客户端应用，负责所有用户界面的渲染和交互。

我们的核心技术栈包括：

*   **构建工具: [Vite](mdc:httpss:/vitejs.dev)**
    *   **角色**: 提供极速的本地开发服务器和高效的生产环境打包。配置文件是 [vite.config.js](mdc:vue-frontend/vite.config.js)。
*   **核心框架: [Vue 3](mdc:httpss:/vuejs.org) (Composition API)**
    *   **角色**: 驱动整个应用的数据和视图。我们主要使用 `<script setup>` 语法，它更简洁、高效。
    *   **入口**: [src/main.js](mdc:vue-frontend/src/main.js) (应用初始化) 和 [src/App.vue](mdc:vue-frontend/src/App.vue) (根组件)。
*   **路由管理: [Vue Router](mdc:httpss:/router.vuejs.org)**
    *   **角色**: 管理应用内所有页面的URL路径和页面间的跳转。
    *   **配置**: [src/router/index.js](mdc:vue-frontend/src/router/index.js)。
*   **状态管理: [Pinia](mdc:httpss:/pinia.vuejs.org)**
    *   **角色**: 集中管理需要在多个组件或页面间共享的"全局状态"（如用户信息、登录Token、购物车等）。
    *   **存放目录**: [src/stores/](mdc:vue-frontend/src/stores)。

---

### **2. 文件结构详解**

```
vue-frontend/
├── public/              # 静态资源，直接复制到构建结果根目录
├── src/                 # 核心源代码目录
│   ├── assets/          # 需要被 Vite 处理的静态资源 (CSS, images)
│   ├── components/      # 可复用的 UI 组件 (积木块)
│   ├── router/          # 路由配置
│   ├── stores/          # 全局状态管理 (Pinia)
│   ├── views/           # 页面级组件 (视图)
│   ├── App.vue          # 应用根组件，承载所有页面
│   └── main.js          # 应用主入口文件
├── .gitignore           # Git 忽略文件配置
├── index.html           # 应用主 HTML 模板
├── package.json         # 项目依赖与脚本配置
└── vite.config.js       # Vite 配置文件
```

*   **`src/assets/`**:
    *   存放 CSS 文件、图片、字体等。这些资源在构建时会被 Vite 优化和处理。
    *   全局样式应定义在 `main.css` 并于 `main.js` 中引入。

*   **`src/components/`**:
    *   **核心目录**，存放可复用的UI片段。**组件应该是独立的、只通过 `props` 和 `emits` 与外部通信**。
    *   **命名**: 大驼峰法 (PascalCase)，如 `BottomNav.vue`。
    *   **大型项目建议结构**:
        *   `components/common/`: 基础UI组件 (按钮、弹窗)。
        *   `components/layout/`: 布局组件 (页头、侧边栏)。
        *   `components/feature/`: 按业务功能划分 (如 `course/`, `user/`)。

*   **`src/router/`**:
    *   [index.js](mdc:vue-frontend/src/router/index.js) 是唯一的路由配置文件。
    *   **推荐**: 对非首屏页面使用**路由懒加载** `component: () => import('../views/MeView.vue')` 以优化性能。

*   **`src/stores/`**:
    *   每个文件代表一个独立的"状态仓库"。
    *   **命名**: 小驼峰法 + `Store` 后缀，如 `userStore.js`, `cartStore.js`。
    *   **示例**: [counter.js](mdc:vue-frontend/src/stores/counter.js) (这是一个演示文件)。

*   **`src/views/`**:
    *   存放页面级别的组件。它们组合 `components` 目录中的组件来构成完整页面。
    *   **命名**: 大驼峰法 + `View` 后缀，如 `CoursesView.vue`。

---

### **3. 推荐开发模式**

*   **组件化**: 遵循 [SOP 指南](mdc:.cursor/rules/vue-frontend-development-sop.mdc) 中定义的规则，优先将UI和逻辑拆分为小组件。
*   **API 请求**:
    *   **推荐**: 创建 `src/services/` 或 `src/api/` 目录来统一管理 API 请求。
    *   **`services/request.js`**: 配置一个通用的 `axios` 实例，处理 `baseURL`、请求拦截器 (附加Token) 和响应拦截器 (统一错误处理)。
    *   **`services/courseService.js`**: 导出具体的业务请求函数，如 `getAllCourses()`。组件内应调用这些函数，而不是直接使用 `axios`。
*   **环境变量**:
    *   在项目根目录创建 `.env` 文件来管理环境变量。
    *   `VITE_API_BASE_URL=http://your-domain.com/wp-json/`
    *   在代码中通过 `import.meta.env.VITE_API_BASE_URL` 访问。

