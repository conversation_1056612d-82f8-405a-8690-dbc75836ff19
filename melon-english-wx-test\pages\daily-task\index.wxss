.container {
  padding: 5px;
  background-color: #f5f5f5;
  min-height: 100vh;
}

.data-container {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin: 20rpx 4rpx;
  padding: 40rpx;
  background-color: #fff;
  border-radius: 10rpx;
  box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
  width: 100%;
  box-sizing: border-box;
}

.data-item {
  display: flex;
  flex-direction: column;
  align-items: center;
}

.data-number {
  font-size: 36rpx;
  font-weight: bold;
  margin-bottom: 5px;
  color: #333;
}

.data-label {
  font-size: 16px;
  color: #888;
}

.calendar-container {
  width: 100%;
  margin-top: 20rpx;
  margin-left: 4rpx;
  margin-right: 4rpx;
  padding: 20rpx;
  background-color: #fff;
  border-radius: 10px;
  box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
  box-sizing: border-box;
}

.calendar-title {
  font-size: 20px;
  font-weight: bold;
  margin-bottom: 10px;
  color: #333;
}

.calendar-tips {
  font-size: 12px;
  color: #888;
  margin-bottom: 10px;
}

.calendar-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 10px;
}

.calendar-year-month {
  font-size: 16px;
  color: #666;
}

.calendar-week {
  display: flex;
  overflow-x: scroll;
  width: 100%;
  padding-bottom: 10px; /* 增加底部内边距，防止滚动条遮挡内容 */
  box-sizing: border-box;
}

.calendar-week-item {
  width: 40px;
  height: 40px;
  display: flex;
  justify-content: center;
  align-items: center;
  border-radius: 50%;
  margin-right: 5px;
}

.calendar-day {
  font-size: 16px;
  color: #333;
}

.calendar-day.completed {
  color: #fff;
  background-color: #ffc107; /* Changed to yellow */
}

.calendar-legend {
  display: flex;
  justify-content: flex-end;
  align-items: center;
  margin-top: 10px;
}

.calendar-legend-item {
  display: flex;
  align-items: center;
  margin-right: 10px;
}

.calendar-legend-color {
  width: 12px;
  height: 12px;
  border-radius: 0; /* Make it a square */
  margin-right: 5px;
}

.calendar-icon {
  width: 20px;
  height: 20px;
  margin-right: 5px;
  vertical-align: middle; /* Align icon with text */
}

.calendar-header {
  display: flex;
  justify-content: space-between;
  align-items: baseline; /* Align items to the baseline */
}

.task-icon {
  width: 20px;
  height: 20px;
  margin-right: 5px;
  vertical-align: middle; /* Align icon with text */
}

.task-header {
  display: flex;
  align-items: baseline;
}

.calendar-legend {
  display: flex;
  justify-content: center; /* Center the items horizontally */
  align-items: center;
  margin-top: 5px; /* Reduce top margin */
  margin-bottom: 5px; /* Reduce bottom margin */
}

.calendar-legend-item {
  display: flex;
  align-items: center;
  justify-content: center;
  text-align: center; /* Ensure horizontal centering */
}

.calendar-legend-color {
  width: 12px;
  height: 12px;
  border-radius: 0; /* Make it a square */
  margin-right: 5px;
  background-color: #ffc107; /* Ensure yellow color */
}

.task-item {
  margin-bottom: 5px;
}

.remaining-task-count {
  font-size: 12px;
  color: #888;
  margin-left: 20px; /* Add left margin */
}

.task-header {
  display: flex;
  align-items: center;
}

.calendar-legend-item text {
  font-size: 12px; /* Same as day-of-week */
}

.calendar-legend-color.completed {
  background-color: #ffc107; /* Yellow for completed */
}

.calendar-swiper {
  height: 100px;
}

.calendar-week {
  display: flex;
  justify-content: space-around;
  align-items: center;
  width: 100%;
}

.calendar-week-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  width: 40px;
  height: 60px;
}

.calendar-day-of-week {
  font-size: 12px;
  color: #666;
  margin-bottom: 5px;
}

.calendar-day {
  font-size: 16px;
  color: #333;
}

.calendar-day.completed {
  color: #fff;
  background-color: #ffc107; /* Yellow for completed */
}

.task-container {
  margin: 20rpx 4rpx;
  padding: 20px;
  background-color: #fff;
  border-radius: 10px;
  box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
  width: 100%;
  box-sizing: border-box;
  align-items: baseline;
}

.task-title {
  font-size: 20px;
  font-weight: bold;
  margin-bottom: 10px;
  color: #333;
}

.task-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 10px;
  border-radius: 5px;
}

.task-link {
  color: blue;
}
