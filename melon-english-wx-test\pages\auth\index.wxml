<view class="container">
  <view class="logo">
    <!-- <image src="/images/logo.png" mode="aspectFit"></image> -->
  </view>
  <view class="title">欢迎使用 Melon English</view>
  
  <button 
    class="login-btn {{!isAgree ? 'login-btn-disabled' : ''}}" 
    type="primary" 
    open-type="getPhoneNumber" 
    bindgetphonenumber="getPhoneNumber"
    disabled="{{!isAgree}}"
  >
    用户一键登录
  </button>

  <view class="agreement">
    <checkbox-group bindchange="onAgreementChange">
      <checkbox value="agree" checked="{{isAgree}}" />
      <text class="agreement-text">我已阅读并同意</text>
      <text class="agreement-link" bindtap="goToAgreement">《用户协议》</text>
    </checkbox-group>
  </view>
</view>
