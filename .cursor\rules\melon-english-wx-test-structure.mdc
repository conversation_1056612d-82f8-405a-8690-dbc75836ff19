---
description: 
globs: 
alwaysApply: true
---
# melon-english-wx-test 小程序结构指南

本文档概述了 `melon-english-wx-test` 微信小程序测试项目的主要文件和目录结构。

## 核心文件

*   **[app.js](mdc:melon-english-wx-test/app.js)**: 小程序的全局逻辑入口文件。
*   **[app.json](mdc:melon-english-wx-test/app.json)**: 全局配置文件，定义页面路径、窗口表现、tabBar 等。
*   **[app.wxss](mdc:melon-english-wx-test/app.wxss)**: 全局公共样式表。
*   **[project.config.json](mdc:melon-english-wx-test/project.config.json)**: 微信开发者工具的项目配置文件。
*   **[sitemap.json](mdc:melon-english-wx-test/sitemap.json)**: 配置小程序及其页面是否允许被微信索引。

## 主要目录

*   **`/pages`**: 存放小程序的所有页面。每个页面通常包含四个文件：
    *   `.js`: 页面的逻辑处理。
    *   `.json`: 页面的配置。
    *   `.wxml`: 页面的结构 (类似 HTML)。
    *   `.wxss`: 页面的样式表。
    *   关键页面示例:
        *   **[pages/index/index.js](mdc:melon-english-wx-test/pages/index/index.js)**: 可能是首页。
        *   **[pages/auth/index.js](mdc:melon-english-wx-test/pages/auth/index.js)**: 用户授权/登录页面。
        *   **[pages/courses/index.js](mdc:melon-english-wx-test/pages/courses/index.js)**: 课程列表或展示页面。
        *   **[pages/me/index.js](mdc:melon-english-wx-test/pages/me/index.js)**: "我的" 或用户中心页面。
        *   **[pages/api-test/index.js](mdc:melon-english-wx-test/pages/api-test/index.js)**: 用于测试后端 API 的页面。
        *   **[pages/pay/index.js](mdc:melon-english-wx-test/pages/pay/index.js)**: 支付相关页面。
        *   **[pages/settings/index.js](mdc:melon-english-wx-test/pages/settings/index.js)**: 设置页面。
*   **`/utils`**: 存放工具函数模块。
    *   **[utils/request.js](mdc:melon-english-wx-test/utils/request.js)**: 封装的网络请求函数，用于调用后端 API。
    *   **[utils/util.js](mdc:melon-english-wx-test/utils/util.js)**: 可能包含日期格式化等通用工具函数。
*   **`/config`**: 存放配置文件。
    *   **[config/index.js](mdc:melon-english-wx-test/config/index.js)**: 可能包含 API 的基础 URL、密钥或其他全局配置。
*   **`/components`**: 存放可复用的自定义组件。
    *   例如 `navigation-bar` 自定义导航栏组件。
*   **`/assets`**: 存放静态资源，主要是图片。
*   **`/mock`**: 存放模拟数据 (Mock Data)，用于前端开发或测试。
*   **`/prompts`**: 存放与 AI (可能指 Cursor 或其他 AI 工具) 交互的提示或模板文件。

## 开发注意事项

*   测试后端 API 时，主要关注 **[pages/api-test/index.js](mdc:melon-english-wx-test/pages/api-test/index.js)** 页面的逻辑。该页面包含用于测试不同后端接口的函数，例如 `testLoginApi`, `testRefreshUserInfoApi`, `onChooseAvatar` (内部调用 `uploadFileToServer`)。
*   后端 API 的 URL 端点常量（如 `loginApiUrl`, `updateAvatarApiUrl`, `refreshUserInfoApiUrl`）定义在 **[pages/api-test/index.js](mdc:melon-english-wx-test/pages/api-test/index.js)** 文件的顶部。
*   用户登录后的数据（包括 `user_id`, `userData`, `avatarUrl`）存储在该页面的 `data` 对象中，并会尝试与本地缓存 (`wx.setStorageSync('userData')`) 同步。
*   所有对后端 API 的直接网络请求（如 `wx.request`, `wx.uploadFile`）目前直接在 **[pages/api-test/index.js](mdc:melon-english-wx-test/pages/api-test/index.js)** 中实现，而不是通过 `/utils` 下的封装函数。
*   API 的基础 URL 和相关配置可能在 **[config/index.js](mdc:melon-english-wx-test/config/index.js)** 中定义（需确认是否实际使用）。
*   理解页面路由和窗口表现需查看 **[app.json](mdc:melon-english-wx-test/app.json)**。



