---
description: 
globs: 
alwaysApply: true
---
# 项目文件结构与特定目录说明

本文档概述了当前 WordPress 项目的主要文件和目录结构，并特别说明了几个关键目录的用途，以帮助 AI 理解代码库和开发流程。

## 核心 WordPress 结构

*   **根目录 (`/`)**: 包含 WordPress 核心文件和主要目录。
    *   `wp-admin/`: WordPress 后台管理界面核心文件。
    *   `wp-content/`: 用户内容目录，是开发的主要工作区。
    *   `wp-includes/`: WordPress 核心库文件。
    *   `wp-config.php`: WordPress 配置文件。
    *   ... (其他 WordPress 核心文件)

## `wp-content/` 目录详解

*   **`plugins/`**: 标准插件目录。
    *   **用途**: 存放独立的 WordPress 插件，可能包括后台管理、功能增强或 **用于触发测试流程的后端逻辑测试插件**。
    *   **定位**: 这些插件通常在 WordPress 后台有自己的界面或设置，可视为一种后台 **前端**。

*   **`themes/`**: 主题目录。
    *   **`shuimitao.online/`**: 当前活动（或主要开发）的主题目录。此主题内部结构复杂，详细结构参见结构树json文件 [shuimitao_online_theme_tree.json](mdc:wp-content/themes/shuimitao.online/shuimitao_online_theme_tree.json)  与结构说明 [readme.md](mdc:wp-content/themes/shuimitao.online/readme.md) ，其包含多个逻辑区域：
        *   **`plugins/`**:
            *   **用途**: 存放服务于 `/wp-content/plugins/` 中特定插件（尤其是测试插件）的 **AJAX 处理程序**。
            *   **交互**: `plugins/functions/` 下的文件（如 `LearningReviewDebuggerAjax.php`）处理来自 `/wp-content/plugins/` 插件前端发起的特定 AJAX 请求，通常涉及复杂的数据操作或测试逻辑。
        *   **`admin/`**: 主题后台管理相关功能。
            *   **`pages/`**: 
                *   **用途**: 存放构成主题**后台管理界面**的页面文件（例如主题设置、课程管理等）。这些页面是 WordPress Admin 后台的另一种 **前端**。
            *   **`functions/`**: 
                *   **用途**: 存放服务于 `admin/pages/` 前端界面的 **AJAX 处理程序** (`ajax-handler.php`)。
                *   **交互**: 处理来自主题后台页面的 AJAX 请求，例如保存设置、获取管理数据等。
        *   **`api/`**: 
            *   **用途**: **生产环境 REST API** 目录。
            *   **定位**: 包含最终供**微信小程序**或其他外部应用调用的 API 接口。代码分为 `routes` (定义端点) 和 `handlers` (处理请求逻辑)。接口设计和文档遵循特定标准。
        *   **`api/v1/models/`**: 
            *   **用途**: 存放**数据模型和处理器类**，封装与特定数据类型（如用户 `User.php`, 课程 `CourseDataHandler.php`, 复习计划 `ReviewPlanHandler.php`）相关的复杂业务逻辑和数据交互。
            *   **定位**: 这些模型类是 `api/v1/handlers/` 的主要服务对象，有时也可能被 `admin/functions` 或 `plugins/functions` 调用。**这是查找核心数据处理逻辑的主要位置。**
        *   **`lib/`**: 
            *   **用途**: 存放**可复用的通用业务逻辑**、工具类或第三方库（如 `WXBizDataCrypt.php`）。
            *   **定位**: 实现代码复用的关键目录。与 `api/v1/models/` 不同，这里更侧重于**不直接绑定特定数据模型**的通用功能或与外部服务的集成。例如，微信数据解密逻辑。
        *   `functions.php`: 主题的核心功能文件。负责**初始化**、注册钩子、加载脚本/样式、包含其他必要文件（如 `api/bootstrap.php`、后台页面、AJAX 处理器等）。是整个主题的**入口点和协调器**。
        *   `style.css`: 主题样式表和信息。
        *   `templates/` 或 `template-parts/` (或其他模板文件如 `index.php`, `page.php`, `single.php`): 主题**前端显示**模板文件。
        *   `assets/`: 主题前端和后台使用的静态资源 (JS, CSS, Images)。
        *   ... (其他主题文件)
    *   ... (其他主题)

*   `uploads/`: 用户上传的媒体文件。
*   `mu-plugins/`: Must-Use Plugins。
*   ... (其他目录)



## AI 交互注意事项

*   **区分不同后端逻辑**: 
    *   需要处理 **主题后台页面** 的 AJAX 请求？ -> 查看 `themes/shuimitao.online/admin/functions/ajax-handler.php`。
    *   需要处理 **测试插件** (位于 `/wp-content/plugins/`) 的 AJAX 请求？ -> 查看 `themes/shuimitao.online/plugins/functions/`。
    *   需要处理 **面向小程序等前端应用** 的生产 API 请求？ -> 查看 `themes/shuimitao.online/api/v1/handlers/`。
*   **定位核心逻辑**: 
    *   与特定**数据模型**（用户、课程、复习计划等）相关的复杂操作？ -> 优先查找 `themes/shuimitao.online/api/v1/models/`。
    *   **通用的、可复用的**业务逻辑或工具函数？ -> 查找 `themes/shuimitao.online/lib/`。
*   **理解初始化与加载**: 主题的启动、钩子注册、主要组件加载？ -> 查看 `themes/shuimitao.online/functions.php`。
*   **API 开发规范**: 创建或修改 API 时，务必遵循 `@api-creation-standard.mdc` 和 `@api-documentation-standard.mdc`；理解 API 时参考 `api/v1/apidoc/`。
*   **代码注释**: 添加注释时，遵循 `@code-commenting-standard.mdc`。

## 使用说明 (查找代码)

*   **查找核心数据处理**: 先看 `api/v1/models/`，再看 `lib/`。
*   **理解 API**: 看 `api/v1/apidoc/` (文档) 和 `api/v1/handlers/` (实现)。
*   **理解后台管理**: 看 `admin/pages/` (界面) 和 `admin/functions/` (逻辑)。
*   **理解主题初始化**: 看 `functions.php`。

*   进行主题开发或 API 开发时，主要关注 `wp-content/themes/shuimitao.online/` 目录。
*   涉及用户数据处理和模型时，查看 `wp-content/themes/shuimitao.online/api/v1/models/`。
*   理解 API 设计意图时，参考 `wp-content/themes/shuimitao.online/api/v1/apidoc/`。
*   修改核心业务逻辑或添加钩子时，检查 `wp-content/themes/shuimitao.online/functions.php`。 