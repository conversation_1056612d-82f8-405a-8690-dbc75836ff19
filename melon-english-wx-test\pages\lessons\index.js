// pages/lessons/index.js
Page({

  /**
   * 页面的初始数据
   */
  data: {
    courseName: "新概念英语上册",
    progress: "90",
    courseName: 'Python编程入门',
    progress: 60,
    courseList: [
      {
        id: 1,
        index: 1,
        logoUrl: '/assets/images/review.png',
        status: '已学习'
      },
      {
        id: 2,
        index: 2,
        logoUrl: '/assets/images/review.png',
        status: '未学习'
      },
      {
        id: 3,
        index: 3,
        logoUrl: '/assets/images/review.png',
        status: '未学习'
      },
      {
        id: 4,
        index: 4,
        logoUrl: '/assets/images/review.png',
        status: '未学习'
      },
      {
        id: 5,
        index: 5,
        logoUrl: '/assets/images/review.png',
        status: '未学习'
      },
      {
        id: 6,
        index: 6,
        logoUrl: '/assets/images/review.png',
        status: '未学习'
      }
    ]
  },

  /**
   * 生命周期函数--监听页面加载
   */
  onLoad(options) {
    // 接收参数
    const id = options.id;
    console.log('Received ID:', id);
  },

  /**
   * 生命周期函数--监听页面初次渲染完成
   */
  onReady() {

  },

  /**
   * 生命周期函数--监听页面显示
   */
  onShow() {

  },

  /**
   * 生命周期函数--监听页面隐藏
   */
  onHide() {

  },

  /**
   * 生命周期函数--监听页面卸载
   */
  onUnload() {

  },

  /**
   * 页面相关事件处理函数--监听用户下拉动作
   */
  onPullDownRefresh() {

  },

  /**
   * 页面上拉触底事件的处理函数
   */
  onReachBottom() {

  },

  /**
   * 用户点击右上角分享
   */
  onShareAppMessage() {

  }
})