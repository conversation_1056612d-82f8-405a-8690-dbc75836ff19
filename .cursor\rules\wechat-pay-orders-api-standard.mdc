---
description: 
globs: 
alwaysApply: true
---
# 插件 `wechat-pay-orders` API 接口创建标准

**目标:** 确保在 `wechat-pay-orders` 插件中创建的所有新 API 接口遵循统一的结构、命名、注册和文档化方式。

**通用指南:** 请结合项目根目录 `.cursor/rules/cursor_rules.mdc` 中的通用规则执行以下步骤（例如，使用中文回复，使用反引号包裹标识符，通过 `edit_file` 工具进行修改等）。

**!!! 严格执行:** AI 必须严格遵循以下步骤和文件模板创建新的 API 接口。

## 创建步骤

1.  **明确接口定义:**
    *   **接口名称 (逻辑名):** 确定一个清晰的、小写加下划线的接口名称 (例如 `sync_order_data`)。
    *   **接口功能描述:** 简要描述接口用途 (例如"同步外部订单数据")。
    *   **API 访问路径:** 定义具体的URL路径，不包含命名空间 (例如 `/sync_order_data`)。
    *   **HTTP 请求方法:** 选择 `POST`, `GET`, `PUT`, `DELETE` 等。

2.  **创建路由文件:**
    *   **位置:** `wp-content/plugins/wechat-pay-orders/api/v1/routes/`
    *   **命名:** `[接口名称].php` (与逻辑名一致，例如 `sync_order_data.php`)
    *   **操作:** 使用 `edit_file` 工具，并基于下面的"路由文件模板"创建新文件。**必须严格参照文件模板进行创建，不允许有任何变化（除了占位符替换）。必须**替换模板中的所有占位符。
    *   **示例路径:** `wp-content/plugins/wechat-pay-orders/api/v1/routes/sync_order_data.php`

3.  **创建处理器文件:**
    *   **位置:** `wp-content/plugins/wechat-pay-orders/api/v1/handlers/`
    *   **命名:** `[接口名称].php` (与路由文件名保持一致)
    *   **操作:** 使用 `edit_file` 工具，并基于下面的"处理器文件模板"创建新文件。**必须严格参照文件模板进行创建，不允许有任何变化（除了占位符替换和后续业务逻辑填充）。必须**替换模板中的所有占位符，并实现具体的业务逻辑（初期可为简化版响应）。
    *   **示例路径:** `wp-content/plugins/wechat-pay-orders/api/v1/handlers/sync_order_data.php`

4.  **更新插件加载器 (`class-plugin-loader.php`):**
    *   **文件:** `wp-content/plugins/wechat-pay-orders/includes/class-plugin-loader.php`
    *   **操作:** 使用 `edit_file` 工具，在 `load_dependencies()` 方法内部，找到加载其他 API 文件的地方，添加对新创建的 **路由文件和处理器文件** 的 `require_once` 引用。
    *   **添加内容格式:**
        ```php
        // ... 其他 require_once 语句 ...

        // 加载 [接口功能描述] 接口
        if ( file_exists( WPO_PLUGIN_DIR . 'api/v1/routes/[接口名称].php' ) ) {
            require_once WPO_PLUGIN_DIR . 'api/v1/routes/[接口名称].php';
        }
        if ( file_exists( WPO_PLUGIN_DIR . 'api/v1/handlers/[接口名称].php' ) ) { // 新增加载处理器文件
            require_once WPO_PLUGIN_DIR . 'api/v1/handlers/[接口名称].php';
        }
        ```
    *   **注意:** 确保替换 `[接口名称]` 和 `[接口功能描述]`。`WPO_PLUGIN_DIR` 是插件中已定义的常量，代表插件根目录。

5.  **创建接口文档文件:**
    *   **位置:** `wp-content/plugins/wechat-pay-orders/api/v1/apidoc/`
    *   **命名:** `[接口名称].md` (与路由文件名保持一致，后缀为 `.md`)
    *   **操作:** 使用 `edit_file` 工具，创建一个**空白**的同名 Markdown 文件。**此步骤也必须严格执行。**
    *   **示例路径:** `wp-content/plugins/wechat-pay-orders/api/v1/apidoc/sync_order_data.md`

## 文件模板

### 1. 路由文件模板 (`routes/[接口名称].php`)

```php
<?php
/**
 * API 路由: [接口功能简要描述]
 *
 * 该文件定义了用于 [具体说明接口用途] 的 API 路由。
 *
 * @package WeChat_Pay_Orders\API\v1\Routes
 */

// 可选的命名空间声明，根据您插件的实际情况
namespace WeChatPayOrders\API\v1\Routes; 

defined( 'ABSPATH' ) || exit; // 防止直接访问文件

/**
 * 注册 [接口名称] API 路由
 *
 * 将 /wechat-pay-orders/v1/[接口路径] 端点映射到处理函数。
 */
function register_[接口名称]_route() { // 或者使用插件前缀，例如 wpo_register_[接口名称]_route
    register_rest_route(
        'wechat-pay-orders/v1',      // 插件的API命名空间
        '/[接口路径]',                // API 路径
        array(
            'methods'             => '[HTTP_METHOD]', 
            'callback'            => 'WeChatPayOrders\\API\\v1\\Handlers\\handle_[接口名称]_request', // 指向 Handlers 命名空间下的处理器函数
            'permission_callback' => function( \WP_REST_Request $request ) { // 或者指向 Handlers 命名空间下的权限检查函数, 或在此定义
                // 记录请求来源IP
                $ip_address = sanitize_text_field( $_SERVER['REMOTE_ADDR'] ?? 'unknown' );
                $log_prefix = '[API_PERMISSION_CALLBACK] ' . __FUNCTION__ . ' for ' . $request->get_route() . ' from IP: ' . $ip_address;
                //error_log($log_prefix . ' - 请求已接收。'); // 中文日志示例

                // TODO: 实现严格的权限检查逻辑。
                // 例如：检查 API Key, 用户登录状态或特定用户能力
                // if ( !current_user_can('manage_options') && $request->get_header('X-Custom-Auth') !== 'expected_key' ) {
                //     return new \WP_Error('rest_forbidden', '权限不足。', array('status' => 403));
                // }
                error_log($log_prefix . ' - 警告：正在跳过实际权限检查并返回 true。'); // 中文日志示例
                return true; // 警告：当前允许所有请求，请务必替换为实际的权限检查！
            },
        )
    );
}

// 将路由注册函数挂载到 rest_api_init 动作钩子上
// 注意：如果上面函数使用了命名空间，这里的回调也需要带上完整的命名空间
// 例如：add_action( 'rest_api_init', __NAMESPACE__ . '\\register_[接口名称]_route' );
add_action( 'rest_api_init', 'register_[接口名称]_route' ); // 如果函数在全局命名空间，或者确保函数名在WordPress钩子回调上下文中可访问

```

### 2. 处理器文件模板 (`handlers/[接口名称].php`)

```php
<?php
/**
 * API 处理器: [接口功能简要描述]
 *
 * 该文件包含处理 /wechat-pay-orders/v1/[接口路径] 请求的业务逻辑。
 *
 * @package WeChat_Pay_Orders\API\v1\Handlers
 */

// 确保与路由文件中的 callback 路径一致
namespace WeChatPayOrders\API\v1\Handlers; 

defined( 'ABSPATH' ) || exit; // 防止直接访问文件

// 可选：引入其他需要的类
// use WeChatPayOrders\Includes\OrderDB;

/**
 * 处理 [接口名称] API 请求
 *
 * @param \WP_REST_Request $request 完整的请求数据对象.
 * @return \WP_REST_Response|\WP_Error 成功时返回 WP_REST_Response 对象，失败时返回 WP_Error 对象。
 */
function handle_[接口名称]_request( \WP_REST_Request $request ) { // 函数名应与路由中 callback 指定的一致
    $log_prefix = '[API_HANDLER] ' . __NAMESPACE__ . '\\' . __FUNCTION__ . ' for ' . $request->get_route();
    //error_log($log_prefix . ' - 请求已接收。参数: ' . wp_json_encode($request->get_params())); // 中文日志示例

    // --- 初期响应：表示接口已成功调用，具体业务逻辑待实现 ---
    $response_data = array(
        'success' => true,
        'message' => '接口 "[接口功能简要描述]" 已成功调用。业务逻辑待后续实现。', // 此处本身是中文
        'data'    => array(
            'received_at' => current_time('mysql'),
            'note'        => '这是一个占位符响应，来自处理器文件。', // 中文 note 示例
            'params_received' => $request->get_params(),
        )
    );
    $response_status = 200;

    // --- 在这里逐步实现核心业务逻辑 ---
    // 1. 参数获取与验证: $params = $request->get_params();
    // 2. 业务处理...
    // 3. 异常捕获: try { ... } catch (Exception $e) { ... return new WP_Error(...); }

    //error_log($log_prefix . ' - 响应状态 ' . $response_status . '. 响应数据: ' . wp_json_encode($response_data)); // 中文日志示例
    return new \WP_REST_Response( $response_data, $response_status );
}



```

**重要提示:** (部分内容与之前一致，但针对分离结构进行了调整)

*   **占位符替换:** 务必替换所有 `[]` 包裹的占位符。
*   **命名空间:** 请特别注意路由和处理器文件中的PHP命名空间声明 (`namespace ...;`) 以及函数调用时的命名空间路径（例如 `WeChatPayOrders\\API\\v1\\Handlers\\handle_...`）。确保它们匹配，以便PHP能够正确找到函数。如果您的插件未使用严格的PSR-4命名空间，函数可能在全局命名空间下，此时回调路径会简单些。**请参考您插件中现有 `payment_notify.php` 路由和处理器文件的命名空间用法。**
*   **`PluginLoader` 更新:** `class-plugin-loader.php` 的 `load_dependencies()` 方法中现在需要同时 `require_once` 新的路由文件和新的处理器文件。
*   **错误处理与日志:** 遵循现有规范。
*   **安全性 (`permission_callback`):** 依然至关重要。
*   **参数校验 (`args`):** 可以在路由文件的 `register_rest_route` 中定义。
*   **逐步实现与文档同步:** 保持不变。

---
*文件名: `wechat-pay-orders-api-standard.mdc`*









