.container {
  background-color: #f5f5f5;
  min-height: 100vh;
  padding: 20rpx;
}

.review-list {
  display: flex;
  flex-direction: column;
  width: 100%;
}

.review-item {
  background-color: #fff;
  border-radius: 10rpx;
  margin-bottom: 20rpx;
  padding: 20rpx 10rpx;
  /* display: flex; */
  justify-content: space-between;
  align-items: center;
}

.item-container {
  display: flex;
  justify-content: space-between;
  align-items: center;
  width: 100%;
}

.item-left {
  display: flex;
  flex-direction: row; /* 修改为 row */
  align-items: center; /* 垂直居中 */
}

.need-review {
  font-size: 24rpx;
  color: #fff;
  background-color: #ff4444;
  padding: 5rpx 10rpx;
  border-radius: 5rpx;
  margin-right: 5rpx; /* 增加右边距 */
  margin-bottom: 0; /* 移除 margin-bottom */
}

.lesson {
  font-size: 32rpx;
  color: #333;
  margin-bottom: 0; /* 移除 margin-bottom */
  margin-left: 20rpx;
}

.course-name-container {
  width: 100%;
}

.course-name {
  font-size: 24rpx;
  color: #999;
}

.item-right {
  display: flex;
  align-items: center;
}

.arrow {
  font-size: 36rpx;
  color: #ccc;
}
