<view class="container">
  <view class="data-container">
    <view class="data-item">
      <text class="data-number">{{continuousLearningDays}}</text>
      <text class="data-label">连续学习天数</text>
    </view>
    <view class="data-item">
      <text class="data-number">{{totalLearningDays}}</text>
      <text class="data-label">累计学习天数</text>
    </view>
  </view>

  <view class="calendar-container">
    <view class="calendar-header">
      <image class="calendar-icon" src="../../assets/images/calendar.png" />
      <text class="calendar-title">学习日历</text>
      <text class="calendar-year-month">{{calendarData.year}}年{{calendarData.month}}月</text>
    </view>
    <text class="calendar-tips">Tips: 完成学习任务 增加一天“学习天数”</text>
  
    <swiper class="calendar-swiper" bindchange="onSwiperChange">
      <swiper-item>
        <view class="calendar-week">
          <view class="calendar-week-item" wx:for="{{weekDays}}" wx:key="date">
            <text class="calendar-day-of-week">{{item.dayOfWeek}}</text>
            <text class="calendar-day {{item.completed ? 'completed' : ''}}">{{item.date}}</text>
          </view>
        </view>
      </swiper-item>
    </swiper>

    <view class="calendar-legend">
      <view class="calendar-legend-item">
        <view class="calendar-legend-color completed"></view>
        <text>已完成</text>
      </view>
    </view>

  </view>

  <view class="task-container">
    <view class="task-header">
      <image class="task-icon" src="../../assets/images/task.png" />
      <text class="task-title">今日剩余任务</text>
    </view>
    <view class="task-item">
      <text>{{remainingTasks}}</text>
      <text class="task-link">去学习 >></text>
    </view>
    <view>
      <text class="remaining-task-count">剩余{{remainingTaskCount}}课</text>
    </view>
  </view>
</view>