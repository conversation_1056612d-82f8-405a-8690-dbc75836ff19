/* pages/courses/index.wxss */
.container {
  padding: 0;
}

/* 课程信息 */
.course-info {
  background-color: #f44336;
  color: #fff;
  padding: 20px;
  border-radius: 0 0 20px 20px;
}

.course-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 10px;
}

.back-icon {
  width: 20px;
  height: 20px;
}

.more-icon {
  width: 20px;
  height: 20px;
}

.course-title {
  font-size: 18px;
}

.course-status {
  font-size: 14px;
  margin-bottom: 10px;
  text-align: center; /* 居中显示 */
}

.course-button {
  background-color: transparent; /* 背景色跟父级一样 */
  color: #fff; /* 字体是白色的 */
  border: 1px solid #fff; /* 边框颜色是白色 */
  border-radius: 20px;
  padding: 5px 15px;
  font-size: 14px;
}

/* 今日任务 */
.today-task {
  padding: 20px;
}

.task-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.task-title {
  font-size: 16px;
  color: #333;
}

.task-detail {
  font-size: 14px;
  color: #666;
  margin-top: 10rpx;
  margin-left: 30rpx;
}

.more-link {
  color: #1976d2;
}

/* 课程表 */
.course-list {
  padding: 20px;
}

.course-list-title {
  font-size: 16px;
  color: #333;
  margin-bottom: 10px;
}

/* pages/courses/index.wxss */
.container {
  padding: 0;
}

/* 课程信息 */
.course-info {
  background-color: #f44336;
  color: #fff;
  padding: 20px;
  border-radius: 0 0 20px 20px;
  width: calc(100% - 20rpx); /* 调整宽度和边距 */
  margin: 0 10rpx;
}

.course-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 10px;
}

.back-icon {
  width: 20px;
  height: 20px;
}

.more-icon {
  width: 20px;
  height: 20px;
}

.course-title {
  font-size: 18px;
}

.course-status {
  font-size: 14px;
  margin-bottom: 10px;
}

.course-button {
  /* background-color: #fff;
  color: #f44336; */
  border-radius: 20px;
  padding: 5px 15px;
  font-size: 14px;
}

/* 今日任务 */
.task-section {
  /* margin-top: -125rpx; */
  background: #fff;
  width: calc(100% - 20rpx); /* 调整宽度和边距 */
  margin: 0 10rpx;
}

.task-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 30rpx;
  margin-bottom: 0;
  border-bottom: 1rpx solid #f5f5f5;
}

.task-header .right {
  display: flex;
  align-items: center;
}

.task-header .left {
  display: flex;
  align-items: center;
}

.task-header .left text {
  font-size: 30rpx;
  font-weight: 500;
  position: relative;
  padding-left: 20rpx;
}

.task-header .left text::before {
  content: "";
  position: absolute;
  left: 0;
  top: 50%;
  transform: translateY(-50%);
  width: 6rpx;
  height: 30rpx;
  background: #ff6b6b;
  border-radius: 3rpx;
}

.task-content {
  padding: 30rpx;
  background: #fff;
  font-size: 28rpx;
  color: #666;
  width: 100%;
  box-sizing: border-box;
}

/* 课程表 */
.course-list {
  padding: 20px;
  width: calc(100% - 20rpx); /* 调整宽度和边距 */
  margin: 0 10rpx;
}

.course-list-title {
  font-size: 16px;
  color: #333;
  margin-bottom: 10px;
}

.course-grid {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  grid-gap: 10px;
}

.course-item {
  background-color: #eee;
  border-radius: 10px;
  padding: 15px;
  text-align: center;
}

.course-item-content {
  display: flex;
  flex-direction: column;
  align-items: center;
}

.course-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
}

.course-number {
  font-size: 16px;
  color: #666;
  margin-bottom: 5px;
}

.course-icon {
  width: 20px;
  height: 20px;
  margin-bottom: 5px;
}

.course-status-text {
  font-size: 12px;
  color: #999;
  margin-top: 5px;
}

/* 不需要单独设置 statusText 的颜色，因为 course-item 已经设置了颜色 */

.course-grid .course-item {
  border: 1px solid #ddd;
  background-color: #eee; /* 默认灰色 */
}

.course-grid .course-item.not-available,
.course-grid .course-item.not-studied {
  background-color: #eee !important;
}

.course-grid .course-item.need-review {
  background-color: #ffebee !important;
}

.course-grid .course-item.waiting-review {
  background-color: #e3f2fd !important;
}

.course-grid .course-item.completed {
  background-color: #e8f5e9 !important;
  position: relative;
}

.course-completed-desc {
  position: absolute;
  top: 2px;
  right: 2px;
  font-size: 8px;
  color: #388e3c;
  background-color: rgba(232, 245, 233, 0.8);
  padding: 1px 3px;
  border-radius: 10px 0 10px 0; /* 模拟树叶形状 */
  z-index: 1; /* 确保显示在最上层 */
}
