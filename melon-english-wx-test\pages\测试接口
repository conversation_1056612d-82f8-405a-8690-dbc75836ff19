## 接口说明

### **获取用户信息接口 (get_course_list.php)**

#### **1. 接口基本信息**
- **路由路径:** `/wp-json/shuimitao/v1/get_course_list`
- **请求方法:** `POST`
- **请求参数:**
  - `code`: 微信小程序通过 `wx.login()` 获取的临时登录凭证 code。
  - `key`: API Key，用于验证请求的合法性。

#### **2. 处理流程**
1. **参数校验**
   - 检查 `code` 和 `key` 是否为空。
   - 如果缺少必要参数，返回错误信息：
     ```json
     {
         "success": false,
         "message": "缺少必要参数"
     }
     ```

2. **API Key 鉴权**
   - 从 WordPress 选项中获取 `shuimitao_api_key`。
   - 校验客户端传入的 `key` 是否与存储的 `shuimitao_api_key` 匹配。
   - 如果鉴权失败，返回错误信息：
     ```json
     {
         "success": false,
         "message": "API Key 鉴权失败"
     }
     ```

3. **获取微信小程序配置**
   - 从 WordPress 选项中获取小程序的 `appid` 和 `appsecret`。

4. **通过 code 换取 session_key 和 openid**
   - 先查询数据库中是否有这个code对应的session_key和openid，如果有，直接使用，如果没有，向微信服务器发起请求，调用 `jscode2session` 接口，传入 `appid`、`appsecret` 和 `code
   - 如果请求失败，返回错误信息：
     ```json
     {
         "success": false,
         "message": "请求微信服务器失败"
     }
     ```
   - 如果微信返回错误码，返回错误信息：
     ```json
     {
         "success": false,
         "message": "获取用户信息失败",
         "error": "微信返回的错误信息"
     }
     ```

5. **用户注册状态检查**
   - 通过 `unionid` 检查用户是否已注册。
   - 如果用户未注册，返回错误信息：
     ```json
     {
         "success": false,
         "message": "用户未注册"
     }
     ```

6. **获取课程列表**

- **成功返回:**
  ```json
  {
    "success": true,
    "continuous_learning_days": "连续学习天数",
    "total_learning_days": "累计学习天数",
    "total_course_hours": "总课时",
    "completed_course_hours": "已完成课时",
    "check_in_days": "打卡状态",
    "course_items": [
      {
        "course_id": "课程ID",
        "course_name": "课程名称",
        "course_difficulty": "课程难度",
        "course_desc": "课程描述",
        "course_img": "课程显示的图片地址"
      },
      {
        "course_id": "课程ID",
        "course_name": "课程名称",
        "course_difficulty": "课程难度",
        "course_desc": "课程描述",
        "course_img": "课程显示的图片地址"
    ]
  }
  ```

- **失败返回:**
  ```json
  {
      "success": false,
      "message": "错误信息",
      "error": "错误详情（可选）"
  }
  ```

