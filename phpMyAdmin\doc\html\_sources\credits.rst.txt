.. _credits:

Credits
=======

Credits, in chronological order
+++++++++++++++++++++++++++++++

* <PERSON> <to<PERSON>s\_at\_ratschiller.com>

  * creator of the phpMyAdmin project

  * maintainer from 1998 to summer 2000

* <PERSON> <marc\_at\_infomarc.info>

  * multi-language version in December 1998

  * various fixes and improvements

  * first version of the :term:`SQL` analyser (most of it)

  * maintainer from 2001 to 2015

* <PERSON> <om\_at\_omnis.ch>

  * started SourceForge phpMyAdmin project in March 2001

  * sync'ed different existing CVS trees with new features and bugfixes

  * multi-language improvements, dynamic language selection

  * many bugfixes and improvements

* <PERSON>ï<PERSON>aux <lolo\_at\_phpheaven.net>

  * rewrote and optimized JavaScript, DHTML and DOM stuff

  * rewrote the scripts so they fit the :term:`PEAR` coding standards and
    generate XHTML1.0 and CSS2 compliant codes

  * improved the language detection system

  * many bugfixes and improvements

* <PERSON> <robbat2\_at\_users.sourceforge.net>

  * database maintenance controls

  * table type code

  * Host authentication :term:`IP` Allow/Deny

  * DB-based configuration (Not completed)

  * :term:`SQL` parser and pretty-printer

  * :term:`SQL` validator

  * many bugfixes and improvements

* Armel Fauveau <armel.fauveau\_at\_globalis-ms.com>

  * bookmarks feature

  * multiple dump feature

  * gzip dump feature

  * zip dump feature

* Geert Lund <glund\_at\_silversoft.dk>

  * various fixes

  * moderator of the phpMyAdmin former users forum at phpwizard.net

* Korakot Chaovavanich <korakot\_at\_iname.com>

  * "insert as new row" feature

* Pete Kelly <webmaster\_at\_trafficg.com>

  * rewrote and fix dump code

  * bugfixes

* Steve Alberty <alberty\_at\_neptunlabs.de>

  * rewrote dump code for PHP4

  * mySQL table statistics

  * bugfixes

* Benjamin Gandon <gandon\_at\_isia.cma.fr>

  * main author of the version *******

  * bugfixes

* Alexander M. Turek <me\_at\_derrabus.de>

  * MySQL 4.0 / 4.1 / 5.0 compatibility

  * abstract database interface (PMA\_DBI) with MySQLi support

  * privileges administration

  * :term:`XML` exports

  * various features and fixes

  * German language file updates

* Mike Beck <mike.beck\_at\_web.de>

  * automatic joins in QBE

  * links column in printview

  * Relation view

* Michal Čihař <michal\_at\_cihar.com>

  * enhanced index creation/display feature

  * feature to use a different charset for HTML than for MySQL

  * improvements of export feature

  * various features and fixes

  * Czech language file updates

  * created current website for phpMyAdmin

* Christophe Gesché from the "MySQL Form Generator for PHPMyAdmin"
  (https://sourceforge.net/projects/phpmysqlformgen/)

  * suggested the patch for multiple table printviews

* Garvin Hicking <me\_at\_supergarv.de>

  * built the patch for vertical display of table rows

  * built the Javascript based Query window + :term:`SQL` history

  * Improvement of column/db comments

  * (MIME)-Transformations for columns

  * Use custom alias names for Databases in left frame

  * hierarchical/nested table display

  * :term:`PDF`-scratchboard for WYSIWYG-
    distribution of :term:`PDF` relations

  * new icon sets

  * vertical display of column properties page

  * some bugfixes, features, support, German language additions

* Yukihiro Kawada <kawada\_at\_den.fujifilm.co.jp>

  * japanese kanji encoding conversion feature

* Piotr Roszatycki <d3xter\_at\_users.sourceforge.net> and Dan Wilson

  * the Cookie authentication mode

* Axel Sander <n8falke\_at\_users.sourceforge.net>

  * table relation-links feature

* Maxime Delorme <delorme.maxime\_at\_free.fr>

  * :term:`PDF` schema output, thanks also to
    Olivier Plathey for the "FPDF" library (see <http://www.fpdf.org/>), Steven
    Wittens for the "UFPDF" library and
    Nicola Asuni for the "TCPDF" library (see <https://tcpdf.org/>).

* Olof Edlund <olof.edlund\_at\_upright.se>

  * :term:`SQL` validator server

* Ivan R. Lanin <ivanlanin\_at\_users.sourceforge.net>

  * phpMyAdmin logo (until June 2004)

* Mike Cochrane <mike\_at\_graftonhall.co.nz>

  * blowfish library from the Horde project (withdrawn in release 4.0)

* Marcel Tschopp <ne0x\_at\_users.sourceforge.net>

  * mysqli support

  * many bugfixes and improvements

* Nicola Asuni (Tecnick.com)

  * TCPDF library (<https://tcpdf.org>)

* Michael Keck <mkkeck\_at\_users.sourceforge.net>

  * redesign for 2.6.0

  * phpMyAdmin sailboat logo (June 2004)

* Mathias Landhäußer

  * Representation at conferences

* Sebastian Mendel <cybot\_tm\_at\_users.sourceforge.net>

  * interface improvements

  * various bugfixes

* Ivan A Kirillov

  * new relations Designer

* Raj Kissu Rajandran (Google Summer of Code 2008)

  * BLOBstreaming support (withdrawn in release 4.0)

* Piotr Przybylski (Google Summer of Code 2008, 2010 and 2011)

  * improved setup script

  * user preferences

  * Drizzle support

* Derek Schaefer (Google Summer of Code 2009)

  * Improved the import system

* Alexander Rutkowski (Google Summer of Code 2009)

  * Tracking mechanism

* Zahra Naeem (Google Summer of Code 2009)

  * Synchronization feature (removed in release 4.0)

* Tomáš Srnka (Google Summer of Code 2009)

  * Replication support

* Muhammad Adnan (Google Summer of Code 2010)

  * Relation schema export to multiple formats

* Lori Lee (Google Summer of Code 2010)

  * User interface improvements

  * ENUM/SET editor

  * Simplified interface for export/import

* Ninad Pundalik (Google Summer of Code 2010)

  * AJAXifying the interface

* Martynas Mickevičius (Google Summer of Code 2010)

  * Charts

* Barrie Leslie

  * BLOBstreaming support with PBMS PHP extension (withdrawn in release
    4.0)

* Ankit Gupta (Google Summer of Code 2010)

  * Visual query builder

* Madhura Jayaratne (Google Summer of Code 2011)

  * OpenGIS support

* Ammar Yasir (Google Summer of Code 2011)

  * Zoom search

* Aris Feryanto (Google Summer of Code 2011)

  * Browse-mode improvements

* Thilanka Kaushalya (Google Summer of Code 2011)

  * AJAXification

* Tyron Madlener (Google Summer of Code 2011)

  * Query statistics and charts for the status page

* Zarubin Stas (Google Summer of Code 2011)

  * Automated testing

* Rouslan Placella (Google Summer of Code 2011 and 2012)

  * Improved support for Stored Routines, Triggers and Events

  * Italian translation updates

  * Removal of frames, new navigation

* Dieter Adriaenssens

  * Various bugfixes

  * Dutch translation updates

* Alex Marin (Google Summer of Code 2012)

  * New plugins and properties system

* Thilina Buddika Abeyrathna (Google Summer of Code 2012)

  * Refactoring

* Atul Pratap Singh  (Google Summer of Code 2012)

  * Refactoring

* Chanaka Indrajith (Google Summer of Code 2012)

  * Refactoring

* Yasitha Pandithawatta (Google Summer of Code 2012)

  * Automated testing

* Jim Wigginton (phpseclib.sourceforge.net)

  * phpseclib

* Bin Zu (Google Summer of Code 2013)

  * Refactoring

* Supun Nakandala (Google Summer of Code 2013)

  * Refactoring

* Mohamed Ashraf (Google Summer of Code 2013)

  * AJAX error reporting

* Adam Kang (Google Summer of Code 2013)

  * Automated testing

* Ayush Chaudhary (Google Summer of Code 2013)

  * Automated testing

* Kasun Chathuranga (Google Summer of Code 2013)

  * Interface improvements

* Hugues Peccatte

  * Load/save query by example (database search bookmarks)

* Smita Kumari (Google Summer of Code 2014)

  * Central list of columns

  * Improve table structure (normalization)

* Ashutosh Dhundhara (Google Summer of Code 2014)

  * Interface improvements

* Dhananjay Nakrani (Google Summer of Code 2014)

  * PHP error reporting

* Edward Cheng (Google Summer of Code 2014)

  * SQL Query Console

* Kankanamge Bimal Yashodha (Google Summer of Code 2014)

  * Refactoring: Designer/schema integration

* Chirayu Chiripal (Google Summer of Code 2014)

  * Custom field handlers (Input based MIME transformations)

  * Export with table/column name changes

* Dan Ungureanu (Google Summer of Code 2015)

  * New parser and analyzer

* Nisarg Jhaveri (Google Summer of Code 2015)

  * Page-related settings

  * SQL debugging integration to the Console

  * Other UI improvements

* Deven Bansod (Google Summer of Code 2015)

  * Print view using CSS

  * Other UI improvements and new features

* Deven Bansod (Google Summer of Code 2017)

  * Improvements to the Error Reporting Server

  * Improved Selenium testing

* Manish Bisht (Google Summer of Code 2017)

  * Mobile user interface

  * Remove inline JavaScript code

  * Other UI improvements

* Raghuram Vadapalli (Google Summer of Code 2017)

  * Multi-table query interface

  * Allow Designer to work with tables from other databases

  * Other UI improvements

* Maurício Meneghini Fauth

  * Major improvements and upgrades to the JavaScript core
  * Modernize JavaScript library functionality
  * Modernize templating and introduce Twig

* William Desportes

  * Coding style improvements based on PHPStan
  * Improve links to external MySQL and MariaDB documentation
  * Numerous other bug fixes

* Emanuel Bronshtein

  * Comprehensive security assessment and suggestions

* Lakshya Arora (Google Summer of Code 2018)

  * Various improvements including:

    * Integrate user preferences with local storage
    * Use a modal login after session expiration
    * Add support for CHECK CONSTRAINTS
    * and more!

* Saksham Gupta (Google Summer of Code 2018)

  * Automated theme generator tool

* Leonardo Strozzi (Google Summer of Code 2018)

  * Refactoring Twig templates and other internal code improvements

* Piyush Vijay (Google Summer of Code 2018)

  * Modernize the JavaScript code including introducing Webpack, Babel, and Yarn as well as eslint and Jsdoc

And also to the following people who have contributed minor changes,
enhancements, bugfixes or support for a new language since version
2.1.0:

Bora Alioglu, Ricardo ?, Sven-Erik Andersen, Alessandro Astarita,
Péter Bakondy, Borges Botelho, Olivier Bussier, Neil Darlow, Mats
Engstrom, Ian Davidson, Laurent Dhima, Kristof Hamann, Thomas Kläger,
Lubos Klokner, Martin Marconcini, Girish Nair, David Nordenberg,
Andreas Pauley, Bernard M. Piller, Laurent Haas, "Sakamoto", Yuval
Sarna, www.securereality.com.au, Alexis Soulard, Alvar Soome, Siu Sun,
Peter Svec, Michael Tacelosky, Rachim Tamsjadi, Kositer Uros, Luís V.,
Martijn W. van der Lee, Algis Vainauskas, Daniel Villanueva, Vinay,
Ignacio Vazquez-Abrams, Chee Wai, Jakub Wilk, Thomas Michael
Winningham, Vilius Zigmantas, "Manuzhai".

Translators
+++++++++++

Following people have contributed to translation of phpMyAdmin:

* Albanian

    * Arben Çokaj <acokaj\_at\_shkoder.net>

* Arabic

    * Ahmed Saleh Abd El-Raouf Ismae <a.saleh.ismael\_at\_gmail.com>
    * Ahmed Saad <egbrave\_at\_hotmail.com>
    * hassan mokhtari <persiste1\_at\_gmail.com>

* Armenian

    * Andrey Aleksanyants <aaleksanyants\_at\_yahoo.com>

* Azerbaijani

    * Mircəlal <01youknowme\_at\_gmail.com>
    * Huseyn <huseyn_esgerov\_at\_mail.ru>
    * Sevdimali İsa <sevdimaliisayev\_at\_mail.ru>
    * Jafar <sharifov\_at\_programmer.net>

* Belarusian

    * Viktar Palstsiuk <vipals\_at\_gmail.com>

* Bulgarian

    * Boyan Kehayov <bkehayov\_at\_gmail.com>
    * Valter Georgiev <blagynchy\_at\_gmail.com>
    * Valentin Mladenov <hudsonvsm\_at\_gmail.com>
    * P <plamen_mbx\_at\_yahoo.com>
    * krasimir <vip\_at\_krasio-valia.com>

* Catalan

    * josep constanti <jconstanti\_at\_yahoo.es>
    * Xavier Navarro <xvnavarro\_at\_gmail.com>

* Chinese (China)

    * Vincent Lau <3092849\_at\_qq.com>
    * Zheng Dan <clanboy\_at\_163.com>
    * disorderman <disorderman\_at\_qq.com>
    * Rex Lee <duguying2008\_at\_gmail.com>
    *  <fundawang\_at\_gmail.com>
    * popcorner <memoword\_at\_163.com>
    * Yizhou Qiang <qyz.yswy\_at\_hotmail.com>
    * zz <tczzjin\_at\_gmail.com>
    * Terry Weng <wengshiyu\_at\_gmail.com>
    * whh <whhlcj\_at\_126.com>

* Chinese (Taiwan)

    * Albert Song <albb0920\_at\_gmail.com>
    * Chien Wei Lin <cwlin0416\_at\_gmail.com>
    * Peter Dave Hello <xs910203\_at\_gmail.com>

* Colognian

    * Purodha <publi\_at\_web.de>

* Czech

    * Aleš Hakl <ales\_at\_hakl.net>
    * Dalibor Straka <dalibor.straka3\_at\_gmail.com>
    * Martin Vidner <martin\_at\_vidner.net>
    * Ondra Šimeček <ondrasek.simecek\_at\_gmail.com>
    * Jan Palider <palider\_at\_seznam.cz>
    * Petr Kateřiňák <petr.katerinak\_at\_gmail.com>

* Danish

    * Aputsiaĸ Niels Janussen <aj\_at\_isit.gl>
    * Dennis Jakobsen <dennis.jakobsen\_at\_gmail.com>
    * Jonas <jonas.den.smarte\_at\_gmail.com>
    * Claus Svalekjaer <just.my.smtp.server\_at\_gmail.com>

* Dutch

    * A. Voogt <a.voogt\_at\_hccnet.nl>
    * dingo thirteen <dingo13\_at\_gmail.com>
    * Robin van der Vliet <info\_at\_robinvandervliet.nl>
    * Dieter Adriaenssens <ruleant\_at\_users.sourceforge.net>
    * Niko Strijbol <strijbol.niko\_at\_gmail.com>

* English (United Kingdom)

    * Dries Verschuere <dries.verschuere\_at\_outlook.com>
    * Francisco Rocha <j.francisco.o.rocha\_at\_zoho.com>
    * Marc Delisle <marc\_at\_infomarc.info>
    * Marek Tomaštík <tomastik.m\_at\_gmail.com>

* Esperanto

    * Eliovir <eliovir\_at\_gmail.com>
    * Robin van der Vliet <info\_at\_robinvandervliet.nl>

* Estonian

    * Kristjan Räts <kristjanrats\_at\_gmail.com>

* Finnish

    * Juha Remes <jremes\_at\_outlook.com>
    * Lari Oesch <lari\_at\_oesch.me>

* French

    * Marc Delisle <marc\_at\_infomarc.info>

* Frisian

    * Robin van der Vliet <info\_at\_robinvandervliet.nl>

* Galician

    * Xosé Calvo <xosecalvo\_at\_gmail.com>

* German

    * Julian Ladisch <github.com-t3if\_at\_ladisch.de>
    * Jan Erik Zassenhaus <jan.zassenhaus\_at\_jgerman.de>
    * Lasse Goericke <lasse\_at\_mydom.de>
    * Matthias Bluthardt <matthias\_at\_bluthardt.org>
    * Michael Koch <michael.koch\_at\_enough.de>
    * Ann + J.M. <phpMyAdmin\_at\_ZweiSteinSoft.de>
    *  <pma\_at\_sebastianmendel.de>
    * Phillip Rohmberger <rohmberger\_at\_hotmail.de>
    * Hauke Henningsen <sqrt\_at\_entless.org>

* Greek

    * Παναγιώτης Παπάζογλου <papaz_p\_at\_yahoo.com>

* Hebrew

    * Moshe Harush <mmh15\_at\_windowslive.com>
    * Yaron Shahrabani <sh.yaron\_at\_gmail.com>
    * Eyal Visoker <visokereyal\_at\_gmail.com>

* Hindi

    * Atul Pratap Singh <atulpratapsingh05\_at\_gmail.com>
    * Yogeshwar <charanyogeshwar\_at\_gmail.com>
    * Deven Bansod <devenbansod.bits\_at\_gmail.com>
    * Kushagra Pandey <kushagra4296\_at\_gmail.com>
    * Nisarg Jhaveri <nisargjhaveri\_at\_gmail.com>
    * Roohan Kazi <roohan_cena\_at\_yahoo.co.in>
    * Yugal Pantola <yug.scorpio\_at\_gmail.com>

* Hungarian

    * Akos Eros <erosakos02\_at\_gmail.com>
    * Dániel Tóth <leedermeister\_at\_gmail.com>
    * Szász Attila <undernetangel\_at\_gmail.com>
    * Balázs Úr <urbalazs\_at\_gmail.com>

* Indonesian

    * Deky Arifianto <Deky40\_at\_gmail.com>
    * Andika Triwidada <andika\_at\_gmail.com>
    * Dadan Setia <da2n_s\_at\_yahoo.co.id>
    * Dadan Setia <dadan.setia\_at\_gmail.com>
    * Yohanes Edwin <edwin\_at\_yohanesedwin.com>
    * Fadhiil Rachman <fadhiilrachman\_at\_gmail.com>
    * Benny <tarzq28\_at\_gmail.com>
    * Tommy Surbakti <tommy\_at\_surbakti.net>
    * Zufar Fathi Suhardi <zufar.bogor\_at\_gmail.com>

* Interlingua

    * Giovanni Sora <g.sora\_at\_tiscali.it>

* Italian

    * Francesco Saverio Giacobazzi <francesco.giacobazzi\_at\_ferrania.it>
    * Marco Pozzato <ironpotts\_at\_gmail.com>
    * Stefano Martinelli <stefano.ste.martinelli\_at\_gmail.com>

* Japanese

    * k725 <alexalex.kobayashi\_at\_gmail.com>
    * Hiroshi Chiyokawa <hiroshi.chiyokawa\_at\_gmail.com>
    * Masahiko HISAKAWA <orzkun\_at\_ageage.jp>
    * worldwideskier <worldwideskier\_at\_yahoo.co.jp>

* Kannada

    * Robin van der Vliet <info\_at\_robinvandervliet.nl>
    * Shameem Ahmed A Mulla <shameem.sam\_at\_gmail.com>

* Korean

    * Bumsoo Kim <bskim45\_at\_gmail.com>
    * Kyeong Su Shin <cdac1234\_at\_gmail.com>
    * Dongyoung Kim <dckyoung\_at\_gmail.com>
    * Myung-han Yu <greatymh\_at\_gmail.com>
    * JongDeok <human.zion\_at\_gmail.com>
    * Yong Kim <kim\_at\_nhn.com>
    * 이경준 <kyungjun2\_at\_gmail.com>
    * Seongki Shin <skshin\_at\_gmail.com>
    * Yoon Bum-Jong <virusyoon\_at\_gmail.com>
    * Koo Youngmin <youngminz.kr\_at\_gmail.com>

* Kurdish Sorani

    * Alan Hilal <alan.hilal94\_at\_gmail.com>
    * Aso Naderi <aso.naderi\_at\_gmail.com>
    * muhammad <esy_vb\_at\_yahoo.com>
    * Zrng Abdulla <zhyarabdulla94\_at\_gmail.com>

* Latvian

    * Latvian TV <dnighttv\_at\_gmail.com>
    * Edgars Neimanis <edgarsneims5092\_at\_inbox.lv>
    * Ukko <perkontevs\_at\_gmail.com>

* Limburgish

    * Robin van der Vliet <info\_at\_robinvandervliet.nl>

* Lithuanian

    * Vytautas Motuzas <v.motuzas\_at\_gmail.com>

* Malay

    * Amir Hamzah <amir.overlord666\_at\_gmail.com>
    * diprofinfiniti <anonynuine-999\_at\_yahoo.com>

* Nepali

    * Nabin Ghimire <nnabinn\_at\_hotmail.com>

* Norwegian Bokmål

    * Børge Holm-Wennberg <borge947\_at\_gmail.com>
    * Tor Stokkan <danorse\_at\_gmail.com>
    * Espen Frøyshov <efroys\_at\_gmail.com>
    * Kurt Eilertsen <kurt\_at\_kheds.com>
    * Christoffer Haugom <ph3n1x.nobody\_at\_gmail.com>
    * Sebastian <sebastian\_at\_sgundersen.com>
    * Tomas <tomas\_at\_tomasruud.com>

* Persian

    * ashkan shirian <ashkan.shirian\_at\_gmail.com>
    * HM <goodlinuxuser\_at\_chmail.ir>

* Polish

    * Andrzej <andrzej\_at\_kynu.pl>
    * Przemo <info\_at\_opsbielany.waw.pl>
    * Krystian Biesaga <krystian4842\_at\_gmail.com>
    * Maciej Gryniuk <maciejka45\_at\_gmail.com>
    * Michał VonFlynee <vonflynee\_at\_gmail.com>

* Portuguese

    * Alexandre Badalo <alexandre.badalo\_at\_sapo.pt>
    * João Rodrigues <geral\_at\_jonilive.com>
    * Pedro Ribeiro <p.m42.ribeiro\_at\_gmail.com>
    * Sandro Amaral <sandro123iv\_at\_gmail.com>

* Portuguese (Brazil)

    * Alex Rohleder <alexrohleder96\_at\_outlook.com>
    * bruno mendax <brunomendax\_at\_gmail.com>
    * Danilo GUia <danilo.eng\_at\_globomail.com>
    * Douglas Rafael Morais Kollar <douglas.kollar\_at\_pg.df.gov.br>
    * Douglas Eccker <douglaseccker\_at\_hotmail.com>
    * Ed Jr <edjacobjunior\_at\_gmail.com>
    * Guilherme Souza Silva <g.szsilva\_at\_gmail.com>
    * Guilherme Seibt <gui\_at\_webseibt.net>
    * Helder Santana <helder.bs.santana\_at\_gmail.com>
    * Junior Zancan <jrzancan\_at\_hotmail.com>
    * Luis <luis.eduardo.braschi\_at\_outlook.com>
    * Marcos Algeri <malgeri\_at\_gmail.com>
    * Marc Delisle <marc\_at\_infomarc.info>
    * Renato Rodrigues de Lima Júnio <renatomdd\_at\_yahoo.com.br>
    * Thiago Casotti <thiago.casotti\_at\_uol.com.br>
    * Victor Laureano <victor.laureano\_at\_gmail.com>
    * Vinícius Araújo <vinipitta\_at\_gmail.com>
    * Washington Bruno Rodrigues Cav <washingtonbruno\_at\_msn.com>
    * Yan Gabriel <yansilvagabriel\_at\_gmail.com>

* Punjabi

    * Robin van der Vliet <info\_at\_robinvandervliet.nl>

* Romanian

    * Alex <amihaita\_at\_yahoo.com>
    * Costel Cocerhan <costa1988sv\_at\_gmail.com>
    * Ion Adrian-Ionut <john\_at\_panevo.ro>
    * Raul Molnar <molnar.raul\_at\_wservices.eu>
    * Deleted User <noreply\_at\_weblate.org>
    * Stefan Murariu <stefan.murariu\_at\_yahoo.com>

* Russian

    * Andrey Aleksanyants <aaleksanyants\_at\_yahoo.com>
    *  <ddrmoscow\_at\_gmail.com>
    * Robin van der Vliet <info\_at\_robinvandervliet.nl>
    * Хомутов Иван Сергеевич <khomutov.ivan\_at\_mail.ru>
    * Alexey Rubinov <orion1979\_at\_yandex.ru>
    * Олег Карпов <salvadoporjc\_at\_gmail.com>
    * Egorov Artyom <unlucky\_at\_inbox.ru>

* Serbian

    * Smart Kid <kidsmart33\_at\_gmail.com>

* Sinhala

    * Madhura Jayaratne <madhura.cj\_at\_gmail.com>

* Slovak

    * Martin Lacina <martin\_at\_whistler.sk>
    * Patrik Kollmann <parkourpotex\_at\_gmail.com>
    * Jozef Pistej <pistej2\_at\_gmail.com>

* Slovenian

    * Domen <mitenem\_at\_outlook.com>

* Spanish

    * Luis García Sevillano <floss.dev\_at\_gmail.com>
    * Franco <fulanodetal.github1\_at\_openaliasbox.org>
    * Luis Ruiz <luisan00\_at\_hotmail.com>
    * Macofe <macofe.languagetool\_at\_gmail.com>
    * Matías Bellone <matiasbellone+weblate\_at\_gmail.com>
    * Rodrigo A. <ra4\_at\_openmailbox.org>
    * FAMMA TV NOTICIAS MEDIOS DE CO <revistafammatvmusic.oficial\_at\_gmail.com>
    * Ronnie Simon <ronniesimonf\_at\_gmail.com>

* Swedish

    * Anders Jonsson <anders.jonsson\_at\_norsjovallen.se>

* Tamil

    * கணேஷ் குமார் <GANESHTHEONE\_at\_gmail.com>
    * Achchuthan Yogarajah <achch1990\_at\_gmail.com>
    * Rifthy Ahmed <rifthy456\_at\_gmail.com>

* Thai

    *  <nontawat39\_at\_gmail.com>
    * Somthanat W. <somthanat\_at\_gmail.com>

* Turkish

    * Burak Yavuz <hitowerdigit\_at\_hotmail.com>

* Ukrainian

    * Сергій Педько <nitrotoll\_at\_gmail.com>
    * Igor <vmta\_at\_yahoo.com>
    * Vitaliy Perekupka <vperekupka\_at\_gmail.com>

* Vietnamese

    * Bao Phan <baophan94\_at\_icloud.com>
    * Xuan Hung <mr.hungdx\_at\_gmail.com>
    * Bao trinh minh <trinhminhbao\_at\_gmail.com>

* West Flemish

    * Robin van der Vliet <info\_at\_robinvandervliet.nl>

Documentation translators
+++++++++++++++++++++++++

Following people have contributed to translation of phpMyAdmin documentation:

* Albanian

    * Arben Çokaj <acokaj\_at\_shkoder.net>

* Arabic

    * Ahmed El Azzabi <ahmedtek1993\_at\_gmail.com>
    * Omar Essam <omar_2412\_at\_live.com>

* Armenian

    * Andrey Aleksanyants <aaleksanyants\_at\_yahoo.com>

* Azerbaijani

    * Mircəlal <01youknowme\_at\_gmail.com>
    * Sevdimali İsa <sevdimaliisayev\_at\_mail.ru>

* Catalan

    * josep constanti <jconstanti\_at\_yahoo.es>
    * Joan Montané <joan\_at\_montane.cat>
    * Xavier Navarro <xvnavarro\_at\_gmail.com>

* Chinese (China)

    * Vincent Lau <3092849\_at\_qq.com>
    * 罗攀登 <6375lpd\_at\_gmail.com>
    * disorderman <disorderman\_at\_qq.com>
    * ITXiaoPang <djh1017555\_at\_126.com>
    * tunnel213 <tunnel213\_at\_aliyun.com>
    * Terry Weng <wengshiyu\_at\_gmail.com>
    * whh <whhlcj\_at\_126.com>

* Chinese (Taiwan)

    * Chien Wei Lin <cwlin0416\_at\_gmail.com>
    * Peter Dave Hello <xs910203\_at\_gmail.com>

* Czech

    * Aleš Hakl <ales\_at\_hakl.net>
    * Michal Čihař <michal\_at\_cihar.com>
    * Jan Palider <palider\_at\_seznam.cz>
    * Petr Kateřiňák <petr.katerinak\_at\_gmail.com>

* Danish

    * Aputsiaĸ Niels Janussen <aj\_at\_isit.gl>
    * Claus Svalekjaer <just.my.smtp.server\_at\_gmail.com>

* Dutch

    * A. Voogt <a.voogt\_at\_hccnet.nl>
    * dingo thirteen <dingo13\_at\_gmail.com>
    * Dries Verschuere <dries.verschuere\_at\_outlook.com>
    * Robin van der Vliet <info\_at\_robinvandervliet.nl>
    * Stefan Koolen <nast3zz\_at\_gmail.com>
    * Ray Borggreve <ray\_at\_datahuis.net>
    * Dieter Adriaenssens <ruleant\_at\_users.sourceforge.net>
    * Tom Hofman <tom.hofman\_at\_gmail.com>

* Estonian

    * Kristjan Räts <kristjanrats\_at\_gmail.com>

* Finnish

    * Juha <jremes\_at\_outlook.com>

* French

    * Cédric Corazza <cedric.corazza\_at\_wanadoo.fr>
    * Étienne Gilli <etienne.gilli\_at\_gmail.com>
    * Marc Delisle <marc\_at\_infomarc.info>
    * Donavan_Martin <mart.donavan\_at\_hotmail.com>

* Frisian

    * Robin van der Vliet <info\_at\_robinvandervliet.nl>

* Galician

    * Xosé Calvo <xosecalvo\_at\_gmail.com>

* German

    * Daniel <d.gnauk89\_at\_googlemail.com>
    * JH M <janhenrikm\_at\_yahoo.de>
    * Lasse Goericke <lasse\_at\_mydom.de>
    * Michael Koch <michael.koch\_at\_enough.de>
    * Ann + J.M. <phpMyAdmin\_at\_ZweiSteinSoft.de>
    * Niemand Jedermann <predatorix\_at\_web.de>
    * Phillip Rohmberger <rohmberger\_at\_hotmail.de>
    * Hauke Henningsen <sqrt\_at\_entless.org>

* Greek

    * Παναγιώτης Παπάζογλου <papaz_p\_at\_yahoo.com>

* Hungarian

    * Balázs Úr <urbalazs\_at\_gmail.com>

* Italian

    * Francesco Saverio Giacobazzi <francesco.giacobazzi\_at\_ferrania.it>
    * Marco Pozzato <ironpotts\_at\_gmail.com>
    * Stefano Martinelli <stefano.ste.martinelli\_at\_gmail.com>
    * TWS <tablettws\_at\_gmail.com>

* Japanese

    * Eshin Kunishima <ek\_at\_luna.miko.im>
    * Hiroshi Chiyokawa <hiroshi.chiyokawa\_at\_gmail.com>

* Lithuanian

    * Jur Kis <atvejis\_at\_gmail.com>
    * Dovydas <dovy.buz\_at\_gmail.com>

* Norwegian Bokmål

    * Tor Stokkan <danorse\_at\_gmail.com>
    * Kurt Eilertsen <kurt\_at\_kheds.com>

* Portuguese (Brazil)

    * Alexandre Moretti <alemoretti2010\_at\_hotmail.com>
    * Douglas Rafael Morais Kollar <douglas.kollar\_at\_pg.df.gov.br>
    * Guilherme Seibt <gui\_at\_webseibt.net>
    * Helder Santana <helder.bs.santana\_at\_gmail.com>
    * Michal Čihař <michal\_at\_cihar.com>
    * Michel Souza <michel.ekio\_at\_gmail.com>
    * Danilo Azevedo <mrdaniloazevedo\_at\_gmail.com>
    * Thiago Casotti <thiago.casotti\_at\_uol.com.br>
    * Vinícius Araújo <vinipitta\_at\_gmail.com>
    * Yan Gabriel <yansilvagabriel\_at\_gmail.com>

* Slovak

    * Martin Lacina <martin\_at\_whistler.sk>
    * Michal Čihař <michal\_at\_cihar.com>
    * Jozef Pistej <pistej2\_at\_gmail.com>

* Slovenian

    * Domen <mitenem\_at\_outlook.com>

* Spanish

    * Luis García Sevillano <floss.dev\_at\_gmail.com>
    * Franco <fulanodetal.github1\_at\_openaliasbox.org>
    * Matías Bellone <matiasbellone+weblate\_at\_gmail.com>
    * Ronnie Simon <ronniesimonf\_at\_gmail.com>

* Turkish

    * Burak Yavuz <hitowerdigit\_at\_hotmail.com>

Original Credits of Version 2.1.0
+++++++++++++++++++++++++++++++++

This work is based on Peter Kuppelwieser's MySQL-Webadmin. It was his
idea to create a web-based interface to MySQL using PHP3. Although I
have not used any of his source-code, there are some concepts I've
borrowed from him. phpMyAdmin was created because Peter told me he
wasn't going to further develop his (great) tool.

Thanks go to

* Amalesh Kempf <ak-lsml\_at\_living-source.com> who contributed the
  code for the check when dropping a table or database. He also
  suggested that you should be able to specify the primary key on
  tbl\_create.php3. To version 1.1.1 he contributed the ldi\_\*.php3-set
  (Import text-files) as well as a bug-report. Plus many smaller
  improvements.
* Jan Legenhausen <jan\_at\_nrw.net>: He made many of the changes that
  were introduced in 1.3.0 (including quite significant ones like the
  authentication). For 1.4.1 he enhanced the table-dump feature. Plus
  bug-fixes and help.
* Marc Delisle <DelislMa\_at\_CollegeSherbrooke.qc.ca> made phpMyAdmin
  language-independent by outsourcing the strings to a separate file. He
  also contributed the French translation.
* Alexandr Bravo <abravo\_at\_hq.admiral.ru> who contributed
  tbl\_select.php3, a feature to display only some columns from a table.
* Chris Jackson <chrisj\_at\_ctel.net> added support for MySQL functions
  in tbl\_change.php3. He also added the "Query by Example" feature in
  2.0.
* Dave Walton <walton\_at\_nordicdms.com> added support for multiple
  servers and is a regular contributor for bug-fixes.
* Gabriel Ash <ga244\_at\_is8.nyu.edu> contributed the random access
  features for 2.0.6.

The following people have contributed minor changes, enhancements,
bugfixes or support for a new language:

Jim Kraai, Jordi Bruguera, Miquel Obrador, Geert Lund, Thomas
Kleemann, Alexander Leidinger, Kiko Albiol, Daniel C. Chao, Pavel
Piankov, Sascha Kettler, Joe Pruett, Renato Lins, Mark Kronsbein,
Jannis Hermanns, G. Wieggers.

And thanks to everyone else who sent me email with suggestions, bug-
reports and or just some feedback.
