/* index.wxss */
.container {
  min-height: 100vh;
  background: #f5f5f5;
}

/* 页面容器 */

/* 用户信息区域 */
.user-info {
  background: linear-gradient(to right, #ff6b6b, #ff8e8e);
  padding: 69rpx;
  color: #fff;
  width: 100%;
}

.user-info-content {
  display: flex;
  align-items: center;
  margin-left: 30rpx;
}

.user-avatar {
  width: 100rpx;
  height: 100rpx;
  border-radius: 50%;
  margin-right: 20rpx;
}

.user-detail {
  display: flex;
  flex-direction: column;
}

.user-name {
  font-size: 32rpx;
  margin-bottom: 8rpx;
}

.user-id {
  font-size: 24rpx;
  margin-bottom: 4rpx;
  opacity: 0.9;
}

.vip-expire {
  font-size: 24rpx;
  opacity: 0.9;
}

/* 开通VIP按钮 */
.vip-button {
  background: linear-gradient(90deg, #ff7f50, #ff4500);
  color: #fff;
  text-align: center;
  font-size: 14px;
  font-weight: bold;
  box-shadow: 0 4px 8px rgba(255, 69, 0, 0.3);
}

/* 功能入口区域 */
.function-list {
  width: 100%;
  background-color: #fff;
  border-top: 1px solid #eee;
}

.section {
  background: #fff;
  margin-top: 20rpx;
  padding: 30rpx;
}

.section-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20rpx;
  font-size: 30rpx;
  font-weight: bold;
}

.check-detail {
  color: #999;
  font-size: 26rpx;
}

.task-item {
  color: #666;
  font-size: 28rpx;
}

.section-icon {
  width: 40rpx;
  height: 40rpx;
  margin-right: 10rpx;
}

.book-list {
  display: flex;
  justify-content: space-between;
  padding: 30rpx;
}

.book-item {
  width: 30%;
  text-align: center;
}

.book-item image {
  width: 100%;
  height: 200rpx;
  margin-bottom: 10rpx;
}

.book-item text {
  font-size: 24rpx;
  color: #333;
  display: block;
  line-height: 1.4;
}

.bottom-section {
  background: #fff;
  margin-top: 20rpx;
}

.function-item {
  display: flex;
  align-items: center;
  padding: 30rpx;
  border-bottom: 1rpx solid #f5f5f5;
  position: relative;
}

.function-item image {
  width: 36rpx;
  height: 36rpx;
  margin-right: 20rpx;
}

.function-item text {
  font-size: 30rpx;
  color: #333;
}

.red-dot {
  /* position: absolute;
  top: 30rpx;
  right: 80rpx; */
  margin-top: -30rpx;
  margin-left: 2rpx;
  width: 10rpx;
  height: 10rpx;
  background-color: #ff4444;
  border-radius: 50%;
}

/* 内容区域统一样式 */
.content-section {
  background: #fff;
  padding: 30rpx;
}

/* 今日任务区域 */
.task-section {
  /* margin-top: -125rpx; */
  background: #fff;
  width: 100%;
}

.task-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 30rpx;
  margin-bottom: 0; /* 移除底部间距 */
  border-bottom: 1rpx solid #f5f5f5;
}

.task-header .left {
  display: flex;
  align-items: center;
}

.task-header .left text {
  font-size: 30rpx;
  font-weight: 500;
  position: relative;
  padding-left: 20rpx;
}

.task-header .left text::before {
  content: "";
  position: absolute;
  left: 0;
  top: 50%;
  transform: translateY(-50%);
  width: 6rpx;
  height: 30rpx;
  background: #ff6b6b;
  border-radius: 3rpx;
}

.task-content {
  padding: 30rpx;
  background: #fff;
  font-size: 28rpx;
  color: #666;
  width: 100%;
  box-sizing: border-box;
}

/* 学习历史区域 */
.history-section {
  /* margin-top: -220rpx; */
  background: #fff;
  width: 100%;
}

.history-header {
  display: flex;
  align-items: center;
  padding: 30rpx;
  border-bottom: 1rpx solid #f5f5f5;
}

.history-header image {
  width: 36rpx;
  height: 36rpx;
  margin-right: 10rpx;
}

.history-header text {
  font-size: 30rpx;
  font-weight: 500;
}

/* 底部功能区域 */
.function-section {
  margin-top: 20rpx;
  background: #fff;
  width: 100%;
}
