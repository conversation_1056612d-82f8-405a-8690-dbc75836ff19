// api-test/index.js
const app = getApp()
// 接口域名和路径
const baseUrl = 'https://shuimitao.online'
const apiUrl = baseUrl + '/wp-json/shuimitao/v1'
const getUserApiUrl = apiUrl + '/get_user'
const loginApiUrl = apiUrl + '/login'
// *** 新增：定义头像上传接口地址 ***
const updateAvatarApiUrl = apiUrl + '/update_user_avatar'
// *** 新增：定义刷新用户信息接口地址 ***
const refreshUserInfoApiUrl = apiUrl + '/refresh_user_info'
// 新增：定义微信支付接口地址
const wechatPayApiUrl = baseUrl + '/wp-json/wechat-pay-orders/v1/payment'
// 新增：定义获取VIP套餐信息接口地址
const getVipMessagesApiUrl = baseUrl + '/wp-json/shuimitao/v1/get_vip_messages'



Page({
  data: {
    loading: false, // 加载状态
    apiResponse: null, // 保存接口返回结果
    jsonString: '', // JSON字符串形式，用于显示
    errorMsg: '', // 错误信息
    code: '', // 存储临时登录凭证code
    apiKey: '0AQruhBYmKa^D%xX3#ocpDfz$$dxx%l*CorGNX5tCTr1SpYfmN9qXR0Sq#)m2xgW', // API Key
    wechat_pay_apiKey:"G!KqqN3Xs3g?$wf2,q0NCXe]B.L*zrOt@<mXYFSF:ljF/2}xr<*BO+h4Yvg,u:d/",
    copied: false, // 标记是否已复制数据
    selectedApi: 'login', // 默认选择 login API
    encryptedData: '', // 手机号加密数据
    iv: '', // 加密向量
    userInfoResult: '', // 这个似乎不再需要，因为 get_user 接口可能不存在
    loginResult: '', // 存储登录接口的字符串结果
    pronounceWord: 'good job', // 默认发音单词
    user_id: null, // 初始化为 null，登录后填充
    userData: null, // 新增：用于存储从 API 获取的用户数据对象
    avatarBase64: '', // 保存头像的base64数据
    avatarUrl: '', // 初始化为空，登录后或选择头像后填充
    uploadResult: null, // 存储上传结果
    vipPackages: null, // 新增：用于存储获取到的VIP套餐信息
  },

  onLoad() {
    console.log('[API测试] 页面加载完成');
    
    // 保存API Key到缓存（如果需要持久化）
    wx.setStorageSync('shuimitao_api_key', this.data.apiKey);

    // 尝试从缓存加载用户数据
    const storedUserData = wx.getStorageSync('userData');
    if (storedUserData) {
      console.log('从缓存加载用户数据:', storedUserData);
      this.setData({
        userData: storedUserData,
        user_id: storedUserData.user_id, // 确保 user_id 也从缓存加载
        avatarUrl: storedUserData.user_avatar || '' // 加载头像，提供默认值
      });
    } else {
      console.log('缓存中无用户数据');
    }

    // 新增：尝试从缓存加载VIP套餐信息
    const storedVipPackages = wx.getStorageSync('vipPackagesData');
    if (storedVipPackages) {
      console.log('从缓存加载VIP套餐数据:', storedVipPackages);
      this.setData({
        vipPackages: storedVipPackages
      });
    } else {
      console.log('缓存中无VIP套餐数据');
    }
  },
  
  // 选择API
  onSelectApi: function(e) {
    const api = e.currentTarget.dataset.api;
    // 定义API映射关系，确保data.selectedApi的值与界面保持一致
    const apiMapping = {
      // 'get_user': 'get_user', // 移除或注释掉不存在的 API
      'login': 'login',
      'pronounce': 'pronounce',
      // 'choose_avatar': 'choose_avatar' // 更新为 update_user_avatar 保持一致
      'update_user_avatar': 'update_user_avatar',
      // *** 新增：刷新用户信息映射 ***
      'refresh_user_info': 'refresh_user_info'
    };
    
    console.log('选择API:', api);
    
    this.setData({
      selectedApi: apiMapping[api] || api,
      errorMsg: ''
    });
    this.resetState(false); // 重置部分状态，不清空用户信息
  },
  
  // 开始测试获取用户信息接口 (注释掉或移除，因为后端似乎没有这个接口)
  /*
  testUserInfoApi() {
    // ... 原有逻辑 ...
  },
  */
  
  // 开始测试登录接口
  testLoginApi() {
    // 重置数据
    this.setData({
      loading: true,
      code: '',
      encryptedData: '',
      iv: '',
      loginResult: '',
      apiResponse: null, // 清空上次的响应
      jsonString: '',
      errorMsg: ''
    });

    // 检查API Key
    if (!this.data.apiKey) {
      this.setData({
        errorMsg: 'API Key不能为空',
        loading: false
      });
      return;
    }

    // 调用微信登录接口获取code
    wx.login({
      success: (res) => {
        if (res.code) {
          // 保存code
          this.setData({
            code: res.code,
            loading: false
          });
          // 提示用户下一步
          this.handleError('已获取临时登录code，请点击"获取手机号"按钮继续');
        } else {
          this.setData({
            errorMsg: '获取Code失败: ' + (res.errMsg || '未知错误'),
            loading: false
          });
        }
      },
      fail: (err) => {
        this.setData({
          errorMsg: '登录失败: ' + (err.errMsg || '未知错误'),
          loading: false
        });
      }
    });
  },
  
  // 获取手机号
  getPhoneNumber(e) {
    console.log('获取手机号事件触发:', e);
    // console.log('事件详情:', JSON.stringify(e.detail)); // 避免打印过多信息
    
    // 检查是否已获取code
    if (!this.data.code) {
      this.setData({
        errorMsg: '请先点击"测试登录接口"获取登录Code'
      });
      return;
    }
    
    // 设置加载状态
    this.setData({
      loading: true,
      errorMsg: ''
    });
    
    if (e.detail.errMsg === 'getPhoneNumber:ok') {
      if (e.detail.encryptedData && e.detail.iv) {
        console.log('获取到手机号加密数据');
        this.setData({
          encryptedData: e.detail.encryptedData,
          iv: e.detail.iv
        });
        this._requestLogin(this.data.code, e.detail.encryptedData, e.detail.iv);
      } else {
        this.setData({
          errorMsg: '获取手机号数据格式异常',
          loading: false
        });
      }
    } else {
      this.setData({
        errorMsg: '用户拒绝授权获取手机号: ' + e.detail.errMsg,
        loading: false
      });
    }
  },
  
  // 发起登录请求
  _requestLogin(code, encryptedData, iv) {
    const apiUrl = loginApiUrl;
    
    if (!code || !encryptedData || !iv || !this.data.apiKey) {
      // ... 参数校验逻辑保持不变 ...
      this.handleError('缺少必要参数: ...'); // 简化错误消息
      return;
    }
    
    wx.login({ // 重新获取 code
      success: (loginRes) => {
        if (!loginRes.code) {
          this.handleError('重新获取code失败: ' + (loginRes.errMsg || '未知错误'));
          return;
        }
        
        console.log('登录前重新获取新code成功:', loginRes.code);
        
        const requestData = {
          key: this.data.apiKey,
          code: loginRes.code, // 使用最新的 code
          encryptedData: encryptedData,
          iv: iv
        };
        
        console.log('登录请求参数:', JSON.stringify(requestData));
        console.log('登录请求URL:', apiUrl);
        
        wx.request({
          url: apiUrl,
          method: 'POST',
          header: {
            'content-type': 'application/x-www-form-urlencoded'
          },
          data: requestData,
          success: (res) => {
            console.log('登录接口返回:', res);
            
            if (res.statusCode !== 200 || !res.data) {
              this.handleError(`请求失败: HTTP ${res.statusCode} 或响应为空`);
              return;
            }
            
            // 先展示原始响应
            this.displayApiResponse(res.data);
            
            // 检查业务逻辑是否成功
            if (res.data.success && res.data.data) { // 确保 res.data.data 存在
              console.log('登录成功，用户数据:', res.data.data);

              // *** 新增：存储用户数据到本地缓存 ***
              try {
                wx.setStorageSync('userData', res.data.data);
                console.log('用户数据已存入本地缓存');
              } catch (e) {
                console.error('存储用户数据到缓存失败:', e);
                // 这里可以选择是否提示用户
              }

              // *** 新增：更新页面 data 中的用户数据 ***
              this.setData({
                userData: res.data.data, 
                user_id: res.data.data.user_id, // 更新 user_id
                avatarUrl: res.data.data.user_avatar || '', // 更新头像
                loginResult: JSON.stringify(res.data, null, 2), // 保持 loginResult 更新
                loading: false
              });
              console.log('用户数据已更新到页面 data');

            } else {
              console.log('登录失败或返回数据结构错误:', res.data.message);
              this.handleError('登录失败: ' + (res.data.message || '返回数据结构错误'));
              this.setData({ // 失败时也要停止 loading
                 loginResult: JSON.stringify(res.data, null, 2),
                 loading: false
              });
            }
          },
          fail: (err) => {
            console.error('登录请求失败:', err);
            this.setData({
              errorMsg: '请求登录接口失败: ' + (err.errMsg || '未知错误'),
              loading: false
            });
          }
        });
      },
      fail: (err) => {
        this.handleError('重新获取code失败: ' + (err.errMsg || '未知错误'));
      }
    });
  },
  
  // 调用获取用户信息接口 (注释掉或移除)
  /*
  getUserInfo(code) {
    // ...
  },
  */
  
  // 重置状态 (修改：可选地保留用户信息)
  resetState(clearUserInfo = true) { // 添加参数控制是否清除用户信息
    console.log('重置状态，是否清除用户信息:', clearUserInfo);
    const newState = {
      loading: false,
      apiResponse: null,
      jsonString: '',
      errorMsg: '',
      code: '',
      encryptedData: '',
      iv: '',
      copied: false,
      loginResult: '', 
      uploadResult: null, // 重置上传结果
      avatarBase64: '' // 重置 base64 头像
    };
    if (clearUserInfo) { // 如果需要清除用户信息
      newState.userData = null;
      newState.user_id = null;
      newState.avatarUrl = ''; // 重置头像 URL
      // 清除本地缓存
      // wx.removeStorageSync('userData');
      // console.log('已清除本地用户数据缓存');
      // 暂时不主动清除缓存，让 onLoad 可以加载
    }
    this.setData(newState);
  },
  
  // 展示API返回结果
  displayApiResponse(response) {
    try {
      const jsonString = JSON.stringify(response, null, 2);
      this.setData({
        apiResponse: response,
        jsonString: jsonString,
        // errorMsg: '', // 不在这里清除错误，可能 login 失败时需要显示
        // loading: false // 不在这里设置 loading，由调用者处理
      });
    } catch (e) {
      this.handleError('解析返回数据失败: ' + e.message);
      // 即使解析失败，也显示原始数据（如果 response 是字符串的话）
      if (typeof response === 'string') {
         this.setData({ jsonString: response });
      }
    }
  },
  
  // 复制API返回数据
  copyApiResponse() {
    if (!this.data.jsonString) return;
    
    wx.setClipboardData({
      data: this.data.jsonString,
      success: () => {
        this.setData({ copied: true });
        setTimeout(() => { this.setData({ copied: false }); }, 1500);
      }
    });
  },
  
  // 错误处理
  handleError(msg) {
    console.error('错误:', msg);
    this.setData({
      errorMsg: msg,
      loading: false
    });
  },
  
  // 分享功能
  onShareAppMessage() {
    return {
      title: '水蜜桃英语API测试工具',
      path: '/pages/api-test/index'
    };
  },
  
  // 播放发音
  playPronunciation: function() {
    const word = this.data.pronounceWord || 'hello';
    const audioUrl = `https://dict.youdao.com/dictvoice?audio=${encodeURIComponent(word)}`;
    
    this.setData({ loading: true, errorMsg: '' });
    console.log('播放发音:', word, '链接:', audioUrl);
    
    const innerAudioContext = wx.createInnerAudioContext();
    innerAudioContext.autoplay = true; // 设置自动播放
    innerAudioContext.src = audioUrl;
    
    innerAudioContext.onPlay(() => {
      console.log('开始播放');
    });
    innerAudioContext.onEnded(() => {
      console.log('播放完成');
      this.setData({ loading: false });
      innerAudioContext.destroy(); // 销毁实例
    });
    innerAudioContext.onError((res) => {
      console.error('音频播放出错:', res);
      this.handleError('发音播放失败: ' + res.errMsg);
      innerAudioContext.destroy(); // 销毁实例
    });
  },
  
  // 更新要发音的单词
  updatePronounceWord: function(e) {
    this.setData({ pronounceWord: e.detail.value });
  },

  // 获取用户头像回调
  onChooseAvatar: function(e) {
    console.log('获取头像事件触发:', e);
    const { avatarUrl } = e.detail; // 这是临时路径
    console.log('获取到的头像临时路径:', avatarUrl);

    if (!this.data.user_id) {
      this.handleError('请先登录获取 user_id');
      return;
    }
    
    // 更新显示的头像为临时路径，并开始上传
    this.setData({ avatarUrl: avatarUrl, loading: true, errorMsg: '' });
    
    // 调用修改后的 uploadFileToServer 函数
    this.uploadFileToServer(avatarUrl)
      .then(uploadResponse => { // uploadFileToServer 现在直接返回 wx.uploadFile 的响应
        console.log('头像上传成功 (wx.uploadFile 响应):', uploadResponse);
        
        // 解析后端返回的数据
        try {
          const backendData = JSON.parse(uploadResponse.data);
          console.log('解析后的后端响应:', backendData);

          // *** 修改这里的判断逻辑 ***
          // 仅检查后端是否返回了 success: true，因为当前后端只做校验
          if (backendData.success === true) {
             // 校验成功，但后端未实际处理文件和返回 URL
             console.log('[校验成功]', backendData.message); // 打印后端返回的消息
             this.setData({
               // avatarUrl:  // 不更新 avatarUrl，因为还没有最终 URL
               uploadResult: backendData, // 保存校验结果供查看
               loading: false
             });
             // 可以给用户一个提示，表明文件已发送，但处理状态未知或等待处理
             wx.showToast({ title: '头像发送成功', icon: 'success' }); 
             // 注意：此时不应该更新本地存储的 user_avatar

           } else {
             // 后端返回业务失败 (例如 API Key 错误、用户不存在等)
             console.error('[校验失败]', backendData.message);
             throw new Error(backendData.message || '上传接口校验失败'); // 使用后端返回的错误消息
           }
        } catch (parseError) {
           // JSON 解析错误
           console.error('解析上传响应失败:', parseError, '原始数据:', uploadResponse.data);
           throw new Error('解析服务器响应失败'); // 抛出错误，由 catch 处理
        }
      })
      .catch(error => {
        console.error('处理头像过程中出错:', error);
        this.handleError('头像处理失败: ' + (error.message || '未知错误'));
        const oldUserData = wx.getStorageSync('userData');
        this.setData({ avatarUrl: oldUserData ? oldUserData.user_avatar : '' });
      });
  },

  // *** 修改后的函数：直接上传文件到后端接口 ***
  uploadFileToServer(filePath) {
    // 1. 打印文件路径（调试用）
    console.log('[上传头像] 准备上传文件，临时路径:', filePath);

    // 2. 检查 user_id 和 apiKey 是否存在
    if (!this.data.user_id || !this.data.apiKey) {
      console.error('[上传头像] 缺少 user_id 或 apiKey，无法上传');
      // 返回一个 rejected Promise，以便调用链可以捕获错误
      return Promise.reject(new Error('缺少用户信息或 API Key'));
    }

    // 3. 返回一个新的 Promise，封装 wx.uploadFile 的异步操作
    return new Promise((resolve, reject) => {
      // 4. 调用 wx.uploadFile
      wx.uploadFile({
        // 接口 URL：使用之前定义的 updateAvatarApiUrl
        url: updateAvatarApiUrl,
        // 文件路径：传入的临时文件路径
        filePath: filePath,
        // 文件对应的 key：后端接收文件时使用的字段名，通常是 'file' 或 'avatar'
        // ！！！重要：需要与后端接收文件的字段名一致！！！
        name: 'avatar_file', // 假设后端用 'avatar_file' 接收，您需要根据后端修改
        // HTTP 请求 Header，可以为空或按需添加
        // header: { 'Authorization': 'Bearer YOUR_TOKEN' },
        // 附加的表单数据：包含 user_id 和 key
        formData: {
          'user_id': this.data.user_id, // 传递用户 ID
          'key': this.data.apiKey       // 传递 API Key
          // 注意：这里不再传递 avatar_data URL
        },
        // 上传成功回调
        success: (res) => {
          console.log('[上传头像] wx.uploadFile 调用成功，原始响应:', res);
          // statusCode 为 HTTP 状态码，data 为服务器返回的数据（通常是字符串）
          if (res.statusCode === 200) {
            // 成功接收到服务器响应（HTTP 200）
            // 将服务器的完整响应 resolve 出去，由调用者 (onChooseAvatar) 处理
            resolve(res);
          } else {
            // 服务器返回了非 200 的 HTTP 状态码
            console.error('[上传头像] 服务器返回错误状态码:', res.statusCode);
            reject(new Error(`服务器错误: ${res.statusCode}`));
          }
        },
        // 上传失败回调
        fail: (err) => {
          console.error('[上传头像] wx.uploadFile 调用失败:', err);
          reject(new Error('文件上传网络请求失败: ' + (err.errMsg || '未知网络错误')));
        }
      });
    });
  },

  // 调用 WordPress 更新头像接口 (这个函数现在不再直接调用，因为上传和更新合并了)
  /*
  updateAvatarOnWordPress(avatarUrl) {
    // ... (代码保持不变，但调用处已修改) ...
  },
  */

  // 准备获取头像
  getUserAvatar: function() {
    if (!this.data.user_id) {
      this.handleError('请先登录');
      return;
    }
    // 提示用户点击按钮
    this.setData({ errorMsg: '请点击下方的 "选择头像" 按钮' }); 
    console.log('提示用户点击按钮获取头像');
  },

  // *** 新增：开始测试刷新用户信息接口 ***
  testRefreshUserInfoApi() {
    console.log('[API测试] 开始测试刷新用户信息接口');
    // 1. 重置状态并显示加载中
    this.setData({
      loading: true,
      apiResponse: null,
      jsonString: '',
      errorMsg: ''
    });

    // 2. 检查必要的参数：user_id 和 apiKey
    if (!this.data.user_id || !this.data.apiKey) {
      this.handleError('请先登录以获取 user_id 和 API Key');
      return;
    }

    // 3. 准备请求数据
    const requestData = {
      key: this.data.apiKey,
      user_id: this.data.user_id
    };

    console.log('刷新用户信息请求参数:', JSON.stringify(requestData));
    console.log('刷新用户信息请求URL:', refreshUserInfoApiUrl);

    // 4. 发起 wx.request 请求
    wx.request({
      url: refreshUserInfoApiUrl,
      method: 'POST', // 假设使用 POST 方法
      header: {
        'content-type': 'application/x-www-form-urlencoded'
      },
      data: requestData,
      success: (res) => {
        console.log('刷新用户信息接口返回:', res);
        
        if (res.statusCode !== 200 || !res.data) {
          this.handleError(`请求失败: HTTP ${res.statusCode} 或响应为空`);
          return;
        }
        
        // 显示返回结果
        this.displayApiResponse(res.data);
        
        // 检查业务逻辑是否成功
        if (res.data.success && res.data.data) {
          console.log('刷新用户信息成功，新数据:', res.data.data);
          //可选：用新数据更新本地缓存和页面 data (如果需要同步)
          try {
            wx.setStorageSync('userData', res.data.data);
            this.setData({ userData: res.data.data });
            console.log('用户数据已更新');
          } catch (e) {
            console.error('更新用户数据缓存失败:', e);
          }
           wx.showToast({ title: '用户信息已刷新', icon: 'success', duration: 1500 });
        } else {
          console.log('刷新用户信息失败:', res.data.message);
          this.handleError('刷新失败: ' + (res.data.message || '返回数据结构错误'));
        }
      },
      fail: (err) => {
        console.error('刷新用户信息请求失败:', err);
        this.handleError('请求刷新接口失败: ' + (err.errMsg || '未知错误'));
      },
      complete: () => {
        // 停止加载状态
        this.setData({ loading: false });
      }
    });
  },

  // *** 修改：测试微信支付接口，现在接收事件对象e ***
  testWechatPayApi(e) { // 添加事件对象e作为参数
    console.log('[API测试] 开始测试微信支付接口');

    // 从按钮的data-package中获取选中的套餐详情
    const packageDetail = e.currentTarget.dataset.package;
    if (!packageDetail || typeof packageDetail !== 'object') {
      this.handleError('发起支付失败：无法获取套餐详情或套餐详情格式不正确。');
      return;
    }
    console.log('选中的支付套餐详情:', packageDetail);

    // 从页面数据中获取其他必要信息
    const user_id = this.data.user_id;
    const userData = this.data.userData || {}; 
    const union_id = userData.union_id || ''; 
    const openid_to_wechat_mini_program = userData.openid_to_wechat_mini_program || ''; 
    const key = this.data.wechat_pay_apiKey; 

    // 动态获取价格和描述
    const priceInYuan = packageDetail.price; // 从套餐详情中获取价格（元）
    let priceInCents;
    if (typeof priceInYuan === 'number' && !isNaN(priceInYuan)) {
      priceInCents = Math.round(priceInYuan * 100); // 转换为分，并四舍五入
    } else {
      console.error('套餐价格无效:', priceInYuan, packageDetail);
      this.handleError('套餐价格无效，无法发起支付。');
      return;
    }

    // 将整个套餐对象序列化为JSON字符串作为描述
    let descriptionJsonString = '';
    try {
      descriptionJsonString = JSON.stringify(packageDetail);
    } catch (jsonError) {
      console.error('套餐对象序列化为JSON失败:', jsonError, packageDetail);
      this.handleError('处理套餐信息失败，无法发起支付。');
      return;
    }

    // 打印将要发送的参数，用于调试
    console.log('支付接口 user_id:', user_id);
    console.log('支付接口 union_id:', union_id);
    console.log('支付接口 openid_to_wechat_mini_program:', openid_to_wechat_mini_program);
    console.log('支付接口 price (分): ', priceInCents); // 显示以分为单位的价格
    console.log('支付接口 description (JSON):', descriptionJsonString);
    console.log('支付接口 key:', key);

    // 前置检查：确保关键用户信息存在
    if (!user_id || !openid_to_wechat_mini_program || !key) {
      this.handleError('发起支付失败：缺少必要的用户信息或API Key。请确保已登录且插件API Key已配置。');
      return;
    }

    // 设置加载状态并清除旧数据
    this.setData({
      loading: true,
      apiResponse: null,
      jsonString: '',
      errorMsg: ''
    });

    // 发起获取预支付参数的请求
    wx.request({
      url: wechatPayApiUrl, // 后端接口地址，用于获取支付参数
      method: 'POST',
      header: { 'content-type': 'application/json' }, // 告知后端请求体是JSON格式
      data: { 
        user_id: user_id,
        union_id: union_id, 
        openid_to_wechat_mini_program: openid_to_wechat_mini_program, 
        price: priceInCents, // 使用以分为单位的价格
        description: descriptionJsonString, // 使用套餐JSON字符串作为描述
        key: key 
      },
      success: (res) => {
        console.log('后端获取支付参数接口返回:', res); // 打印后端完整响应
        this.displayApiResponse(res.data); // 首先展示后端返回的原始数据，方便调试

        // 检查HTTP状态码和业务逻辑是否成功
        if (res.statusCode === 200 && res.data && res.data.success === true && res.data.data) {
          const paymentParams = res.data.data; // 后端返回的支付参数在 res.data.data 中
          console.log('成功获取到预支付参数:', paymentParams);

          // 验证必要支付参数是否存在
          if (paymentParams.timeStamp && paymentParams.nonceStr && paymentParams.package && paymentParams.signType && paymentParams.paySign) {
            // 调用微信支付API
            wx.requestPayment({
              timeStamp: paymentParams.timeStamp,
              nonceStr: paymentParams.nonceStr,
              package: paymentParams.package,
              signType: paymentParams.signType,
              paySign: paymentParams.paySign,
              success: (payRes) => {
                console.log('微信支付API调用成功 (客户端回调):', payRes);

                // 支付成功后的提示和操作
                wx.showModal({
                  title: '支付成功',
                  content: '恭喜您，支付成功！点击"确定"返回个人中心。',
                  showCancel: false, // 只显示确定按钮
                  confirmText: '确定',
                  success: function (modalRes) {
                    if (modalRes.confirm) {
                      console.log('用户点击了"确定"，准备跳转到个人中心页');
                      // 跳转到个人中心页 (假设是 TabBar 页面，路径为 /pages/me/index)
                      // 如果不是 TabBar 页面，请使用 wx.navigateTo({ url: '/path/to/your/page' })
                      wx.switchTab({
                        url: '/pages/me/index',
                        fail: (switchTabErr) => {
                          console.error('跳转到个人中心页 (switchTab) 失败:', switchTabErr);
                          // 如果 switchTab 失败 (可能不是 TabBar 页面或路径错误)，尝试 navigateTo
                          wx.navigateTo({
                            url: '/pages/me/index', // 再次尝试，或者您需要提供正确的非 TabBar 页面路径
                            fail: (navigateToErr) => {
                              console.error('跳转到个人中心页 (navigateTo) 也失败:', navigateToErr);
                              // 可以在这里给用户一个备用提示，例如：
                              wx.showToast({
                                title: '操作成功，请手动返回个人中心查看。',
                                icon: 'none',
                                duration: 2500
                              });
                            }
                          });
                        }
                      });
                    }
                  }
                });

              },
              fail: (payErr) => {
                console.error('微信支付API调用失败:', payErr);
                let errMsg = '支付操作失败';
                if (payErr.errMsg === 'requestPayment:fail cancel') {
                  errMsg = '用户取消了支付';
                }
                this.handleError(errMsg);
              },
              complete: () => {
                console.log('微信支付流程结束 (requestPayment complete)');
              }
            });
          } else {
            console.error('后端返回的支付参数不完整:', paymentParams);
            this.handleError('支付准备失败：服务器返回的支付参数不完整。');
          }
        } else {
          // HTTP状态码200，但业务逻辑失败 (res.data.success为false或缺少data字段)
          const error_message = res.data && res.data.message ? res.data.message : '获取预支付参数失败，请检查API响应。';
          console.error('获取预支付参数业务逻辑失败:', error_message, '完整响应:', res.data);
          this.handleError('支付准备失败: ' + error_message);
        }
      },
      fail: (err) => {
        // wx.request 本身调用失败 (例如网络问题)
        console.error('请求获取支付参数接口失败:', err);
        this.handleError('请求支付接口网络错误: ' + (err.errMsg || '未知网络错误'));
      },
      complete: () => {
        // wx.request 调用完成 (无论成功或失败)
        this.setData({ loading: false });
        console.log('获取支付参数接口请求完成 (wx.request complete)');
      }
    });
  },

  // *** 新增：测试获取VIP套餐信息接口 ***
  testGetVipMessagesApi() {
    console.log('[API测试] 开始测试获取VIP套餐信息接口');
    // 1. 重置状态并显示加载中
    this.setData({
      loading: true,
      apiResponse: null,
      jsonString: '',
      errorMsg: '',
      vipPackages: null // 清空旧的套餐信息，准备获取新的
    });

    console.log('请求URL:', getVipMessagesApiUrl);

    // 2. 发起 wx.request GET 请求
    wx.request({
      url: getVipMessagesApiUrl,
      method: 'GET',
      success: (res) => {
        console.log('获取VIP套餐接口原始返回:', res); // 打印完整响应
        
        // 首先在界面上展示原始返回数据，方便调试
        this.displayApiResponse(res.data);

        if (res.statusCode === 200 && res.data) {
          // 假设后端直接在 res.data 中返回套餐数组或对象，或者在 res.data.data 中
          // 根据实际 get_vip_messages.php 的返回结构调整
          // 我们先假设数据直接在 res.data (如果shuimitao_handle_get_vip_messages直接 new WP_REST_Response( $data ))
          // 或者在 res.data.data (如果shuimitao_handle_get_vip_messages返回 {success:true, data: $data})
          // 从 get_vip_messages.php 的代码看，它没有封装 success 和 data，而是直接返回数据
          // 所以我们直接使用 res.data
          
          const packagesData = res.data; // 直接获取响应数据
          console.log('成功获取到VIP套餐数据:', packagesData);

          this.setData({
            vipPackages: packagesData // 更新页面data中的套餐信息
          });

          // 保存到本地缓存
          try {
            wx.setStorageSync('vipPackagesData', packagesData);
            console.log('VIP套餐数据已存入本地缓存 - prepare to read from cache');

            // 从本地缓存中读取 vipPackagesData 并打印
            const cachedVipPackages = wx.getStorageSync('vipPackagesData');
            console.log('从本地缓存读取的 vipPackagesData:', cachedVipPackages);
            console.log('本地缓存 vipPackagesData 的数据类型:', typeof cachedVipPackages);

          } catch (e) {
            console.error('存储或读取VIP套餐数据到缓存失败:', e);
          }
          
          // 可以在这里根据 packagesData 的具体内容做进一步处理或验证
          // 例如，检查是否是数组，是否有数据等
          if (Array.isArray(packagesData) && packagesData.length > 0) {
            wx.showToast({ title: 'VIP套餐已获取', icon: 'success', duration: 1500 });
          } else if (typeof packagesData === 'object' && packagesData !== null && Object.keys(packagesData).length > 0) {
            wx.showToast({ title: 'VIP套餐配置已获取', icon: 'success', duration: 1500 });
          } else {
            console.warn('获取到的VIP套餐数据为空或格式不符合预期:', packagesData);
            // 虽然请求成功，但数据可能为空，按需处理
            // this.handleError('获取成功，但套餐数据为空'); 
          }

        } else {
          // HTTP状态码不是200或res.data为空
          const error_message = (res.data && res.data.message) ? res.data.message : `请求失败: HTTP ${res.statusCode} 或响应为空`;
          console.error('获取VIP套餐信息失败:', error_message, '完整响应:', res.data);
          this.handleError('获取VIP套餐失败: ' + error_message);
        }
      },
      fail: (err) => {
        console.error('请求获取VIP套餐接口失败 (网络层面):', err);
        this.handleError('请求套餐接口网络错误: ' + (err.errMsg || '未知网络错误'));
      },
      complete: () => {
        // 无论成功或失败，都停止加载状态
        this.setData({ loading: false });
        console.log('获取VIP套餐接口请求完成 (wx.request complete)');
      }
    });
  },
}); 