// 环境配置
const ENV = {
  dev: {
    host: "https://shuimitao.online",
    baseUrl: "https://shuimitao.online/wp-json/shuimitao/v1",
  },
  prod: {
    host: "https://shuimitao.online",
    baseUrl: "https://shuimitao.online/wp-json/shuimitao/v1",
  },
};

// 当前环境
const currentEnv = "dev";

export const config = {
  // 基础配置
  host: ENV[currentEnv].host,
  baseUrl: ENV[currentEnv].baseUrl,

  // 小程序配置
  appid: "wxcf50e1fae973f94f",
  secret: "6469a90930533cd12edc29900b31d310",
  apiKey: "0AQruhBYmKa^D%xX3#ocpDfz$$dxx%l*CorGNX5tCTr1SpYfmN9qXR0Sq#)m2xgW",

  // 超时时间
  timeout: 15000,

  // 是否使用 mock 数据
  useMockData: true,
};
