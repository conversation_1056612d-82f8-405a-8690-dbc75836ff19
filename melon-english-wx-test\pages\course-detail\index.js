Page({
  data: {
    courseId: null,
    course: null,
  },

  onLoad(options) {
    const courseId = options.id;
    this.setData({
      courseId: courseId,
    });

    // 获取课程详情
    this.fetchCourseDetail(courseId);
  },

  async fetchCourseDetail(courseId) {
    // TODO: 从后台接口获取课程详情
    // const response = await request.get(`/courses/${courseId}`);
    // if (response.success) {
    //   this.setData({
    //     course: response.data,
    //   });
    // }

    // 模拟数据
    const course = {
      id: courseId,
      title: '课程详情',
      level: '难度: 小学/初中',
      description: '课程详情描述',
    };
    this.setData({
      course: course,
    });
  },
});