/* pages/pay/index.wxss */
.container {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 20rpx;
}

.header {
  width: 100%;
  text-align: center;
  margin-bottom: 20rpx;
}

.title {
  font-size: 36rpx;
  font-weight: bold;
}

.promotion {
  width: 100%;
  height: 400rpx;
  display: flex;
  flex-direction: column;
  justify-content: center; /* 垂直居中 */
  align-items: center; /* 水平居中 */
  text-align: center;
  margin-bottom: 40rpx;
  background-color: #f0f0f0; /* 恢复灰色背景 */
  border-radius: 10rpx; /* 添加圆角 */
}

.slogan {
  font-size: 28rpx;
}

.discount {
  font-size: 24rpx;
  color: #f00;
}

.options {
  width: 100%;
  display: flex;
  flex-direction: column;
  margin-bottom: 40rpx;
}

.promotion {
  width: 100%; /* 与 .options 保持一致 */
  text-align: center;
  margin-bottom: 40rpx;
  background-color: #f0f0f0; /* 恢复灰色背景 */
  border-radius: 10rpx;
}

.option {
  display: flex;
  align-items: center;
  padding: 20rpx;
  border: 1rpx solid #ccc;
  border-radius: 10rpx;
  margin-bottom: 20rpx;
}

.option-content {
  display: flex;
  flex-direction: column;
  margin-left: 20rpx;
}

.days-price {
  display: flex;
  align-items: center;
}

.days {
  font-size: 32rpx;
  font-weight: bold;
  margin-bottom: 0; /* 移除 days 的 margin-bottom */
  margin-right: 20rpx; /* 添加 days 的 margin-right */
}

.price {
  font-size: 24rpx;
}

.select {
  font-size: 24rpx;
  color: #999;
}

.option.active {
  border-color: #f00;
}

.left {
  display: flex;
  flex-direction: column;
}

.days {
  font-size: 32rpx;
  font-weight: bold;
  margin-bottom: 10rpx;
}

.price {
  font-size: 24rpx;
}

.select {
  font-size: 24rpx;
  color: #999;
}

.button {
  width: 100%;
  background-color: #007bff;
  color: #fff;
  text-align: center;
  padding: 20rpx;
  border-radius: 10rpx;
  font-size: 32rpx;
}
