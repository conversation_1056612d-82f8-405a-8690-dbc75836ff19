import { config } from "../../config/index"; // 导入配置文件，包含API密钥等配置信息
import { request } from "../../utils/request"; // 导入请求工具函数，用于发送网络请求

// 定义页面对象
Page({
  // 页面的初始数据
  data: {
    loading: false, // 是否显示加载状态，初始为false
    isAgree: false, // 用户是否同意协议，初始为false
  },

  // 处理协议同意状态变化的函数
  // 参数：e (对象) - 事件对象，包含用户的选择信息
  // 用途：当用户勾选或取消勾选协议同意框时更新状态
  onAgreementChange(e) {
    this.setData({
      isAgree: e.detail.value.includes("agree"), // 检查用户是否勾选了"agree"选项
    });
  },

  // 跳转到用户协议页面的函数
  // 参数：无
  // 用途：当用户点击查看协议链接时，导航到协议详情页
  goToAgreement() {
    wx.navigateTo({
      url: "/pages/agreement/index", // 协议页面的路径
    });
  },

  // 获取用户手机号并完成登录的函数
  // 参数：e (对象) - 事件对象，包含加密的手机号信息
  // 用途：处理获取手机号按钮的点击事件，完成用户登录流程
  async getPhoneNumber(e) {
    if (e.detail.errMsg !== "getPhoneNumber:ok") {
      wx.showToast({
        title: "获取手机号失败", // 提示获取手机号失败
        icon: "none", // 不显示图标
      });
      return;
    }

    this.setData({ loading: true }); // 设置加载状态为true，显示加载中

    try {
      // 获取微信登录code
      const loginRes = await this._getLoginCode();

      // 发送登录请求到服务器
      const response = await request.post("/login", {
        code: loginRes.code, // 微信登录code
        encryptedData: e.detail.encryptedData, // 加密的手机号数据
        iv: e.detail.iv, // 加密算法的初始向量
        key: config.apiKey, // API密钥，用于服务器验证
      });

      if (response.success) {
        // 登录成功，保存用户信息
        this._saveUserInfo(response);

        // 跳转到首页
        this._navigateToIndex();
      } else {
        wx.showToast({
          title: response.message || "登录失败", // 显示服务器返回的错误信息或默认错误信息
          icon: "none", // 不显示图标
        });
      }
    } catch (error) {
      console.error("登录失败:", error); // 在控制台输出错误信息
      wx.showToast({
        title: "登录失败", // 提示用户登录失败
        icon: "none", // 不显示图标
      });
    } finally {
      this.setData({ loading: false }); // 无论成功失败，都结束加载状态
    }
  },

  // 获取微信登录code的辅助函数
  // 参数：无
  // 用途：调用微信登录接口获取临时登录凭证code
  // 返回：Promise对象，成功时返回登录结果，失败时返回错误
  _getLoginCode() {
    return new Promise((resolve, reject) => {
      wx.login({
        success: (res) => resolve(res), // 登录成功时解析Promise
        fail: (error) => reject(error), // 登录失败时拒绝Promise
      });
    });
  },

  // 保存用户信息的辅助函数
  // 参数：data (对象) - 服务器返回的用户数据
  // 用途：将用户信息保存到本地存储中，供应用其他部分使用
  _saveUserInfo(data) {
    // 保存用户唯一标识作为token
    wx.setStorageSync("token", data.union_id);

    // 保存额外的用户信息
    wx.setStorageSync("user_info_extra", {
      user_id: data.user_id, // 用户ID
      phone: data.telephone_number, // 用户手机号
    });

    console.log("保存的token:", wx.getStorageSync("token")); // 输出保存的token用于调试
  },

  // 跳转到首页的辅助函数
  // 参数：无
  // 用途：登录成功后导航到应用首页
  _navigateToIndex() {
    wx.switchTab({
      url: "/pages/index/index", // 首页的路径
      fail: (error) => {
        console.error("跳转失败:", error); // 输出跳转失败信息
        // 如果 switchTab 失败（可能首页不是 tabBar 页面），尝试使用 redirectTo
        wx.redirectTo({
          url: "/pages/index/index",
        });
      },
    });
  },
});
