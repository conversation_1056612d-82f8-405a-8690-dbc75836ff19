.container {
  padding: 30rpx;
  box-sizing: border-box;
  display: flex;
  flex-direction: column;
  gap: 30rpx;
}

.title {
  font-size: 36rpx;
  font-weight: bold;
  text-align: center;
  margin-bottom: 20rpx;
}

/* API卡片样式 */
.api-card {
  background-color: #f6f8fa;
  padding: 20rpx;
  border-radius: 12rpx;
  display: flex;
  flex-direction: column;
  gap: 20rpx;
}

.card-title {
  font-size: 32rpx;
  font-weight: bold;
  margin-bottom: 10rpx;
}

.api-path {
  font-family: monospace;
  background-color: #eaeaea;
  padding: 8rpx 16rpx;
  border-radius: 6rpx;
  font-size: 26rpx;
  color: #333;
}

/* 测试按钮样式 */
.test-btn {
  background-color: #007aff;
  color: white;
  border-radius: 8rpx;
  font-weight: normal;
  margin-top: 20rpx;
}

/* 警告图标 */
.warning-icon {
  color: #ff6b00;
  margin-right: 8rpx;
  font-size: 32rpx;
}

/* API 说明区域样式 */
.api-note {
  background-color: #f0f7ff;
  padding: 20rpx;
  border-radius: 12rpx;
  border-left: 8rpx solid #007aff;
}

.note-title {
  font-weight: bold;
  margin-bottom: 16rpx;
}

.note-content {
  font-size: 28rpx;
  color: #333;
  line-height: 1.5;
}

.note-tips {
  margin-top: 10rpx;
  color: #ff6b00;
  font-size: 26rpx;
}

/* API Key输入框 */
.input-group {
  display: flex;
  flex-direction: column;
  gap: 10rpx;
}

.input-label {
  font-size: 28rpx;
  font-weight: bold;
}

.api-key-input {
  background-color: white;
  padding: 16rpx;
  border-radius: 8rpx;
  border: 1px solid #ddd;
  font-size: 28rpx;
}

/* 错误消息样式 */
.error-container {
  background-color: rgba(230, 67, 64, 0.1);
  padding: 20rpx;
  border-radius: 8rpx;
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 16rpx;
}

.error-msg {
  color: #e64340;
  font-size: 28rpx;
  text-align: center;
  display: flex;
  align-items: center;
}

.retry-btn {
  background-color: #ff6b00;
  color: white;
  padding: 0 30rpx;
}

/* API 响应区域样式 */
.api-response {
  background-color: #f6f8fa;
  padding: 20rpx;
  border-radius: 12rpx;
  flex: 1;
  display: flex;
  flex-direction: column;
}

.api-response.loading {
  opacity: 0.7;
}

.response-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20rpx;
}

.response-title {
  font-weight: bold;
}

.copy-btn {
  margin: 0;
  padding: 0 20rpx;
  background-color: #f0f0f0;
  color: #333;
  font-weight: normal;
}

.copy-btn[disabled] {
  background-color: #aaa;
  color: #eee;
}

.status-tags {
  margin-bottom: 20rpx;
}

.success-tag {
  display: inline-block;
  background-color: #07c160;
  color: white;
  padding: 4rpx 16rpx;
  border-radius: 6rpx;
  font-size: 24rpx;
}

.error-tag {
  display: inline-block;
  background-color: #e64340;
  color: white;
  padding: 4rpx 16rpx;
  border-radius: 6rpx;
  font-size: 24rpx;
}

/* 加载中状态 */
.loading-text {
  text-align: center;
  color: #999;
  font-size: 28rpx;
  margin: 60rpx 0;
}

/* 空白状态 */
.empty-state {
  text-align: center;
  color: #999;
  font-size: 28rpx;
  margin: 60rpx 0;
}

/* JSON数据显示区域 */
.json-scroll {
  flex: 1;
  height: 600rpx;
}

.json-content {
  white-space: pre-wrap;
  font-family: monospace;
  font-size: 24rpx;
  line-height: 1.4;
  word-break: break-all;
}

/* 调试信息区域 */
.debug-info {
  margin-top: 20rpx;
  font-size: 24rpx;
  color: #999;
  text-align: center;
  padding: 10rpx;
  border-top: 1rpx dashed #ddd;
}

/* API Key状态样式 */
.api-status {
  background-color: #f0f9e8;
  padding: 16rpx;
  border-radius: 8rpx;
  margin-top: 10rpx;
}

.status-info {
  display: flex;
  align-items: center;
}

.status-label {
  font-size: 28rpx;
  font-weight: bold;
  margin-right: 16rpx;
}

.status-value {
  font-size: 28rpx;
  color: #07c160;
}

/* API选项卡样式 */
.api-tabs {
  display: flex;
  margin-bottom: 20rpx;
  background-color: white;
  border-radius: 8rpx;
  overflow: hidden;
  box-shadow: 0 2rpx 6rpx rgba(0, 0, 0, 0.1);
}

.tab {
  flex: 1;
  text-align: center;
  padding: 20rpx 0;
  font-size: 28rpx;
  color: #333;
  position: relative;
}

.tab.active {
  color: #007aff;
  font-weight: bold;
}

.tab.active::after {
  content: '';
  position: absolute;
  bottom: 0;
  left: 25%;
  width: 50%;
  height: 6rpx;
  background-color: #007aff;
  border-radius: 3rpx;
}

/* 测试步骤样式 */
.step {
  margin-left: 20rpx;
  font-size: 26rpx;
  color: #333;
  margin-top: 8rpx;
  position: relative;
}

.step::before {
  content: '●';
  font-size: 22rpx;
  color: #007aff;
  margin-right: 8rpx;
}

/* 手机号按钮样式 */
.phone-btn {
  background: linear-gradient(135deg, #07c160, #1aad19);
  color: white;
  border-radius: 45rpx;
  font-weight: 500;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-top: 16rpx;
}

.phone-btn[disabled] {
  background: linear-gradient(135deg, #aaa, #999);
  color: #ddd;
}

.phone-btn:active {
  opacity: 0.8;
}

.btn-icon {
  margin-right: 10rpx;
  font-size: 32rpx;
}

/* 按钮组样式 */
.button-group {
  display: flex;
  flex-direction: column;
  gap: 16rpx;
}

/* 手机号状态样式 */
.phone-status {
  margin-top: 16rpx;
  background-color: #f0f9e8;
  padding: 16rpx;
  border-radius: 8rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 28rpx;
  color: #07c160;
}

.success-icon {
  margin-right: 10rpx;
  font-size: 32rpx;
  font-weight: bold;
}

/* 警告提示样式 */
.note-warning {
  color: #fa5151;
  font-size: 26rpx;
  margin-top: 10rpx;
  font-weight: bold;
}

/* 调试面板样式 */
.debug-panel {
  margin-top: 20rpx;
  background-color: #f5f5f5;
  padding: 16rpx;
  border-radius: 8rpx;
  border: 1px solid #ddd;
}

.debug-item {
  display: flex;
  margin-bottom: 10rpx;
}

.debug-label {
  font-size: 26rpx;
  color: #666;
  width: 200rpx;
}

.debug-value {
  font-size: 26rpx;
  color: #07c160;
  font-weight: bold;
}

.debug-value.empty {
  color: #fa5151;
}

.pronounce-btn {
  background: linear-gradient(135deg, #4d94ff, #0066cc);
  color: white;
  border-radius: 45rpx;
  font-weight: 500;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-top: 16rpx;
}

.pronounce-btn[disabled] {
  background: linear-gradient(135deg, #aaa, #999);
  color: #ddd;
}

.pronounce-btn:active {
  opacity: 0.8;
}

/* 单词输入框样式 */
.word-input {
  background-color: white;
  padding: 16rpx;
  border-radius: 8rpx;
  border: 1px solid #ddd;
  font-size: 28rpx;
  margin-bottom: 16rpx;
}

/* 发音状态样式 */
.pronounce-status {
  margin-top: 16rpx;
  background-color: #f0f9ff;
  padding: 16rpx;
  border-radius: 8rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 28rpx;
  color: #4d94ff;
}

.avatar-btn {
  background: linear-gradient(135deg, #9c27b0, #673ab7);
  color: white;
  border-radius: 45rpx;
  font-weight: 500;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-top: 16rpx;
}

.avatar-btn[disabled] {
  background: linear-gradient(135deg, #aaa, #999);
  color: #ddd;
}

.avatar-btn:active {
  opacity: 0.8;
}

/* 头像展示区域样式 */
.avatar-display {
  margin: 20rpx 0;
  display: flex;
  flex-direction: column;
  align-items: center;
  background-color: #f5f5f5;
  padding: 20rpx;
  border-radius: 12rpx;
  border: 1px dashed #9c27b0;
}

.avatar-title {
  font-size: 28rpx;
  font-weight: bold;
  margin-bottom: 16rpx;
  color: #673ab7;
}

.avatar-image {
  width: 200rpx;
  height: 200rpx;
  border-radius: 50%;
  border: 4rpx solid #9c27b0;
  background-color: white;
  margin-bottom: 16rpx;
}

.avatar-info {
  font-size: 24rpx;
  color: #666;
  text-align: center;
  margin-bottom: 16rpx;
}

/* URL展示区域 */
.url-display {
  width: 100%;
  background-color: #f0f0f0;
  border-radius: 8rpx;
  padding: 12rpx;
  margin-top: 10rpx;
}

.url-label {
  font-size: 24rpx;
  font-weight: bold;
  color: #333;
  margin-bottom: 8rpx;
}

.url-value {
  font-size: 22rpx;
  color: #666;
  word-break: break-all;
  font-family: monospace;
  background-color: #e8e8e8;
  padding: 8rpx;
  border-radius: 4rpx;
}

/* Base64数据展示区域 */
.base64-display {
  margin: 20rpx 0;
  display: flex;
  flex-direction: column;
  align-items: center;
  background-color: #f0f7ff;
  padding: 20rpx;
  border-radius: 12rpx;
  border: 1px dashed #4d94ff;
}

.base64-title {
  font-size: 28rpx;
  font-weight: bold;
  margin-bottom: 16rpx;
  color: #4d94ff;
}

.avatar-image-small {
  width: 100rpx;
  height: 100rpx;
  border-radius: 50%;
  border: 4rpx solid #4d94ff;
  background-color: white;
  margin-bottom: 16rpx;
}

.base64-info {
  font-size: 24rpx;
  color: #666;
  text-align: center;
}

/* 上传结果区域 */
.upload-result {
  margin: 20rpx 0;
  display: flex;
  flex-direction: column;
  background-color: #f0f9e8;
  padding: 20rpx;
  border-radius: 12rpx;
  border: 1px dashed #07c160;
}

.result-title {
  font-size: 28rpx;
  font-weight: bold;
  margin-bottom: 16rpx;
  color: #07c160;
}

.result-success {
  display: flex;
  align-items: center;
  justify-content: center;
  margin-bottom: 16rpx;
  color: #07c160;
  font-weight: bold;
  font-size: 28rpx;
}

.success-icon {
  margin-right: 10rpx;
  font-size: 32rpx;
}

.result-details {
  background-color: #fff;
  border-radius: 8rpx;
  padding: 12rpx;
}

.result-item {
  margin-bottom: 10rpx;
}

.result-label {
  font-size: 24rpx;
  font-weight: bold;
  color: #333;
  margin-bottom: 4rpx;
}

.result-value {
  font-size: 22rpx;
  color: #666;
  word-break: break-all;
  font-family: monospace;
  background-color: #f5f5f5;
  padding: 8rpx;
  border-radius: 4rpx;
}
