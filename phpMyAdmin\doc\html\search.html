
<!DOCTYPE html>

<html lang="en">
  <head>
    <meta charset="utf-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>Search &#8212; phpMyAdmin 5.2.2 documentation</title>
    <link rel="stylesheet" type="text/css" href="_static/pygments.css" />
    <link rel="stylesheet" type="text/css" href="_static/classic.css" />
    
    
    <script data-url_root="./" id="documentation_options" src="_static/documentation_options.js"></script>
    <script src="_static/jquery.js"></script>
    <script src="_static/underscore.js"></script>
    <script src="_static/_sphinx_javascript_frameworks_compat.js"></script>
    <script src="_static/doctools.js"></script>
    <script src="_static/sphinx_highlight.js"></script>
    
    <script src="_static/searchtools.js"></script>
    <script src="_static/language_data.js"></script>
    <link rel="index" title="Index" href="genindex.html" />
    <link rel="search" title="Search" href="#" />
    <link rel="copyright" title="Copyright" href="copyright.html" />
  <script src="searchindex.js" defer></script>
   

  </head><body>
    <div class="related" role="navigation" aria-label="related navigation">
      <h3>Navigation</h3>
      <ul>
        <li class="right" style="margin-right: 10px">
          <a href="genindex.html" title="General Index"
             accesskey="I">index</a></li>
        <li class="nav-item nav-item-0"><a href="index.html">phpMyAdmin 5.2.2 documentation</a> &#187;</li>
        <li class="nav-item nav-item-this"><a href="">Search</a></li> 
      </ul>
    </div>  

    <div class="document">
      <div class="documentwrapper">
        <div class="bodywrapper">
          <div class="body" role="main">
            
  <h1 id="search-documentation">Search</h1>
  
  <noscript>
  <div class="admonition warning">
  <p>
    Please activate JavaScript to enable the search
    functionality.
  </p>
  </div>
  </noscript>
  
  
  <p>
    Searching for multiple words only shows matches that contain
    all words.
  </p>
  
  
  <form action="" method="get">
    <input type="text" name="q" aria-labelledby="search-documentation" value="" autocomplete="off" autocorrect="off" autocapitalize="off" spellcheck="false"/>
    <input type="submit" value="search" />
    <span id="search-progress" style="padding-left: 10px"></span>
  </form>
  
  
  
  <div id="search-results">
  
  </div>
  

            <div class="clearer"></div>
          </div>
        </div>
      </div>
      <div class="sphinxsidebar" role="navigation" aria-label="main navigation">
        <div class="sphinxsidebarwrapper">
        </div>
      </div>
      <div class="clearer"></div>
    </div>
    <div class="related" role="navigation" aria-label="related navigation">
      <h3>Navigation</h3>
      <ul>
        <li class="right" style="margin-right: 10px">
          <a href="genindex.html" title="General Index"
             >index</a></li>
        <li class="nav-item nav-item-0"><a href="index.html">phpMyAdmin 5.2.2 documentation</a> &#187;</li>
        <li class="nav-item nav-item-this"><a href="">Search</a></li> 
      </ul>
    </div>
    <div class="footer" role="contentinfo">
        &#169; <a href="copyright.html">Copyright</a> 2012 - 2024, The phpMyAdmin devel team.
      Created using <a href="https://www.sphinx-doc.org/">Sphinx</a> 5.3.0.
    </div>
  </body>
</html>