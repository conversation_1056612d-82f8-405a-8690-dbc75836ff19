{"config": {"filename": "melon-english-wx-test-tree.json", "baseDirectory": "C:/Users/<USER>/Desktop/代码库/wordpress/new_site/app/public/melon-english-wx-test", "projectRoot": "C:/Users/<USER>/Desktop/代码库/wordpress/new_site/app/public", "lastUpdated": "2025-04-30T03:12:48.356Z"}, "fileTree": {"path": "C:\\Users\\<USER>\\Desktop\\代码库\\wordpress\\new_site\\app\\public\\melon-english-wx-test", "name": "melon-english-wx-test", "isDirectory": true, "children": [{"path": "C:\\Users\\<USER>\\Desktop\\代码库\\wordpress\\new_site\\app\\public\\melon-english-wx-test\\.cloudbase", "name": ".cloudbase", "isDirectory": true, "children": [{"path": "C:\\Users\\<USER>\\Desktop\\代码库\\wordpress\\new_site\\app\\public\\melon-english-wx-test\\.cloudbase\\container", "name": "container", "isDirectory": true, "children": [{"path": "C:\\Users\\<USER>\\Desktop\\代码库\\wordpress\\new_site\\app\\public\\melon-english-wx-test\\.cloudbase\\container\\debug.json", "name": "debug.json", "isDirectory": false, "importance": 1, "dependencies": [], "packageDependencies": [], "dependents": []}]}]}, {"path": "C:\\Users\\<USER>\\Desktop\\代码库\\wordpress\\new_site\\app\\public\\melon-english-wx-test\\.eslintrc.js", "name": ".eslintrc.js", "isDirectory": false, "importance": 2, "dependencies": [], "packageDependencies": [], "dependents": []}, {"path": "C:\\Users\\<USER>\\Desktop\\代码库\\wordpress\\new_site\\app\\public\\melon-english-wx-test\\app.js", "name": "app.js", "isDirectory": false, "importance": 4, "dependencies": [], "packageDependencies": [], "dependents": []}, {"path": "C:\\Users\\<USER>\\Desktop\\代码库\\wordpress\\new_site\\app\\public\\melon-english-wx-test\\app.json", "name": "app.json", "isDirectory": false, "importance": 3, "dependencies": [], "packageDependencies": [], "dependents": []}, {"path": "C:\\Users\\<USER>\\Desktop\\代码库\\wordpress\\new_site\\app\\public\\melon-english-wx-test\\app.miniapp.json", "name": "app.miniapp.json", "isDirectory": false, "importance": 1, "dependencies": [], "packageDependencies": [], "dependents": []}, {"path": "C:\\Users\\<USER>\\Desktop\\代码库\\wordpress\\new_site\\app\\public\\melon-english-wx-test\\app.wxss", "name": "app.wxss", "isDirectory": false, "importance": 2, "dependencies": [], "packageDependencies": [], "dependents": []}, {"path": "C:\\Users\\<USER>\\Desktop\\代码库\\wordpress\\new_site\\app\\public\\melon-english-wx-test\\assets", "name": "assets", "isDirectory": true, "children": [{"path": "C:\\Users\\<USER>\\Desktop\\代码库\\wordpress\\new_site\\app\\public\\melon-english-wx-test\\assets\\images", "name": "images", "isDirectory": true, "children": [{"path": "C:\\Users\\<USER>\\Desktop\\代码库\\wordpress\\new_site\\app\\public\\melon-english-wx-test\\assets\\images\\avatar.jpg", "name": "avatar.jpg", "isDirectory": false, "importance": 0, "dependencies": [], "packageDependencies": [], "dependents": []}, {"path": "C:\\Users\\<USER>\\Desktop\\代码库\\wordpress\\new_site\\app\\public\\melon-english-wx-test\\assets\\images\\calendar.png", "name": "calendar.png", "isDirectory": false, "importance": 0, "dependencies": [], "packageDependencies": [], "dependents": []}, {"path": "C:\\Users\\<USER>\\Desktop\\代码库\\wordpress\\new_site\\app\\public\\melon-english-wx-test\\assets\\images\\course-active.jpg", "name": "course-active.jpg", "isDirectory": false, "importance": 0, "dependencies": [], "packageDependencies": [], "dependents": []}, {"path": "C:\\Users\\<USER>\\Desktop\\代码库\\wordpress\\new_site\\app\\public\\melon-english-wx-test\\assets\\images\\course-active.png", "name": "course-active.png", "isDirectory": false, "importance": 0, "dependencies": [], "packageDependencies": [], "dependents": []}, {"path": "C:\\Users\\<USER>\\Desktop\\代码库\\wordpress\\new_site\\app\\public\\melon-english-wx-test\\assets\\images\\course.jpg", "name": "course.jpg", "isDirectory": false, "importance": 0, "dependencies": [], "packageDependencies": [], "dependents": []}, {"path": "C:\\Users\\<USER>\\Desktop\\代码库\\wordpress\\new_site\\app\\public\\melon-english-wx-test\\assets\\images\\course.png", "name": "course.png", "isDirectory": false, "importance": 0, "dependencies": [], "packageDependencies": [], "dependents": []}, {"path": "C:\\Users\\<USER>\\Desktop\\代码库\\wordpress\\new_site\\app\\public\\melon-english-wx-test\\assets\\images\\course1.jpg", "name": "course1.jpg", "isDirectory": false, "importance": 0, "dependencies": [], "packageDependencies": [], "dependents": []}, {"path": "C:\\Users\\<USER>\\Desktop\\代码库\\wordpress\\new_site\\app\\public\\melon-english-wx-test\\assets\\images\\course_hour.png", "name": "course_hour.png", "isDirectory": false, "importance": 0, "dependencies": [], "packageDependencies": [], "dependents": []}, {"path": "C:\\Users\\<USER>\\Desktop\\代码库\\wordpress\\new_site\\app\\public\\melon-english-wx-test\\assets\\images\\learn_history.png", "name": "learn_history.png", "isDirectory": false, "importance": 0, "dependencies": [], "packageDependencies": [], "dependents": []}, {"path": "C:\\Users\\<USER>\\Desktop\\代码库\\wordpress\\new_site\\app\\public\\melon-english-wx-test\\assets\\images\\me-active.png", "name": "me-active.png", "isDirectory": false, "importance": 0, "dependencies": [], "packageDependencies": [], "dependents": []}, {"path": "C:\\Users\\<USER>\\Desktop\\代码库\\wordpress\\new_site\\app\\public\\melon-english-wx-test\\assets\\images\\me.png", "name": "me.png", "isDirectory": false, "importance": 0, "dependencies": [], "packageDependencies": [], "dependents": []}, {"path": "C:\\Users\\<USER>\\Desktop\\代码库\\wordpress\\new_site\\app\\public\\melon-english-wx-test\\assets\\images\\my_with_point.png", "name": "my_with_point.png", "isDirectory": false, "importance": 0, "dependencies": [], "packageDependencies": [], "dependents": []}, {"path": "C:\\Users\\<USER>\\Desktop\\代码库\\wordpress\\new_site\\app\\public\\melon-english-wx-test\\assets\\images\\review.png", "name": "review.png", "isDirectory": false, "importance": 0, "dependencies": [], "packageDependencies": [], "dependents": []}, {"path": "C:\\Users\\<USER>\\Desktop\\代码库\\wordpress\\new_site\\app\\public\\melon-english-wx-test\\assets\\images\\setting.png", "name": "setting.png", "isDirectory": false, "importance": 0, "dependencies": [], "packageDependencies": [], "dependents": []}, {"path": "C:\\Users\\<USER>\\Desktop\\代码库\\wordpress\\new_site\\app\\public\\melon-english-wx-test\\assets\\images\\Settings.png", "name": "Settings.png", "isDirectory": false, "importance": 0, "dependencies": [], "packageDependencies": [], "dependents": []}, {"path": "C:\\Users\\<USER>\\Desktop\\代码库\\wordpress\\new_site\\app\\public\\melon-english-wx-test\\assets\\images\\task.png", "name": "task.png", "isDirectory": false, "importance": 0, "dependencies": [], "packageDependencies": [], "dependents": []}, {"path": "C:\\Users\\<USER>\\Desktop\\代码库\\wordpress\\new_site\\app\\public\\melon-english-wx-test\\assets\\images\\time.png", "name": "time.png", "isDirectory": false, "importance": 0, "dependencies": [], "packageDependencies": [], "dependents": []}]}]}, {"path": "C:\\Users\\<USER>\\Desktop\\代码库\\wordpress\\new_site\\app\\public\\melon-english-wx-test\\components", "name": "components", "isDirectory": true, "children": [{"path": "C:\\Users\\<USER>\\Desktop\\代码库\\wordpress\\new_site\\app\\public\\melon-english-wx-test\\components\\navigation-bar", "name": "navigation-bar", "isDirectory": true, "children": [{"path": "C:\\Users\\<USER>\\Desktop\\代码库\\wordpress\\new_site\\app\\public\\melon-english-wx-test\\components\\navigation-bar\\navigation-bar.js", "name": "navigation-bar.js", "isDirectory": false, "importance": 2, "dependencies": [], "packageDependencies": [], "dependents": []}, {"path": "C:\\Users\\<USER>\\Desktop\\代码库\\wordpress\\new_site\\app\\public\\melon-english-wx-test\\components\\navigation-bar\\navigation-bar.json", "name": "navigation-bar.json", "isDirectory": false, "importance": 1, "dependencies": [], "packageDependencies": [], "dependents": []}, {"path": "C:\\Users\\<USER>\\Desktop\\代码库\\wordpress\\new_site\\app\\public\\melon-english-wx-test\\components\\navigation-bar\\navigation-bar.wxml", "name": "navigation-bar.wxml", "isDirectory": false, "importance": 0, "dependencies": [], "packageDependencies": [], "dependents": []}, {"path": "C:\\Users\\<USER>\\Desktop\\代码库\\wordpress\\new_site\\app\\public\\melon-english-wx-test\\components\\navigation-bar\\navigation-bar.wxss", "name": "navigation-bar.wxss", "isDirectory": false, "importance": 0, "dependencies": [], "packageDependencies": [], "dependents": []}]}]}, {"path": "C:\\Users\\<USER>\\Desktop\\代码库\\wordpress\\new_site\\app\\public\\melon-english-wx-test\\config", "name": "config", "isDirectory": true, "children": [{"path": "C:\\Users\\<USER>\\Desktop\\代码库\\wordpress\\new_site\\app\\public\\melon-english-wx-test\\config\\index.js", "name": "index.js", "isDirectory": false, "importance": 6, "dependencies": [], "packageDependencies": [], "dependents": ["C:\\Users\\<USER>\\Desktop\\代码库\\wordpress\\new_site\\app\\public\\melon-english-wx-test\\pages\\auth\\index.js", "C:\\Users\\<USER>\\Desktop\\代码库\\wordpress\\new_site\\app\\public\\melon-english-wx-test\\utils\\request.js"]}]}, {"path": "C:\\Users\\<USER>\\Desktop\\代码库\\wordpress\\new_site\\app\\public\\melon-english-wx-test\\i18n", "name": "i18n", "isDirectory": true, "children": [{"path": "C:\\Users\\<USER>\\Desktop\\代码库\\wordpress\\new_site\\app\\public\\melon-english-wx-test\\i18n\\base.json", "name": "base.json", "isDirectory": false, "importance": 1, "dependencies": [], "packageDependencies": [], "dependents": []}]}, {"path": "C:\\Users\\<USER>\\Desktop\\代码库\\wordpress\\new_site\\app\\public\\melon-english-wx-test\\mock", "name": "mock", "isDirectory": true, "children": [{"path": "C:\\Users\\<USER>\\Desktop\\代码库\\wordpress\\new_site\\app\\public\\melon-english-wx-test\\mock\\dailyTask.js", "name": "dailyTask.js", "isDirectory": false, "importance": 3, "dependencies": [], "packageDependencies": [], "dependents": ["C:\\Users\\<USER>\\Desktop\\代码库\\wordpress\\new_site\\app\\public\\melon-english-wx-test\\pages\\daily-task\\index.js"]}, {"path": "C:\\Users\\<USER>\\Desktop\\代码库\\wordpress\\new_site\\app\\public\\melon-english-wx-test\\mock\\index.js", "name": "index.js", "isDirectory": false, "importance": 4, "dependencies": [], "packageDependencies": [], "dependents": []}]}, {"path": "C:\\Users\\<USER>\\Desktop\\代码库\\wordpress\\new_site\\app\\public\\melon-english-wx-test\\pages", "name": "pages", "isDirectory": true, "children": [{"path": "C:\\Users\\<USER>\\Desktop\\代码库\\wordpress\\new_site\\app\\public\\melon-english-wx-test\\pages\\agreement", "name": "agreement", "isDirectory": true, "children": [{"path": "C:\\Users\\<USER>\\Desktop\\代码库\\wordpress\\new_site\\app\\public\\melon-english-wx-test\\pages\\agreement\\index.js", "name": "index.js", "isDirectory": false, "importance": 4, "dependencies": [], "packageDependencies": [], "dependents": []}, {"path": "C:\\Users\\<USER>\\Desktop\\代码库\\wordpress\\new_site\\app\\public\\melon-english-wx-test\\pages\\agreement\\index.json", "name": "index.json", "isDirectory": false, "importance": 3, "dependencies": [], "packageDependencies": [], "dependents": []}, {"path": "C:\\Users\\<USER>\\Desktop\\代码库\\wordpress\\new_site\\app\\public\\melon-english-wx-test\\pages\\agreement\\index.ttml", "name": "index.ttml", "isDirectory": false, "importance": 2, "dependencies": [], "packageDependencies": [], "dependents": []}, {"path": "C:\\Users\\<USER>\\Desktop\\代码库\\wordpress\\new_site\\app\\public\\melon-english-wx-test\\pages\\agreement\\index.ttss", "name": "index.ttss", "isDirectory": false, "importance": 2, "dependencies": [], "packageDependencies": [], "dependents": []}, {"path": "C:\\Users\\<USER>\\Desktop\\代码库\\wordpress\\new_site\\app\\public\\melon-english-wx-test\\pages\\agreement\\index.wxml", "name": "index.wxml", "isDirectory": false, "importance": 2, "dependencies": [], "packageDependencies": [], "dependents": []}, {"path": "C:\\Users\\<USER>\\Desktop\\代码库\\wordpress\\new_site\\app\\public\\melon-english-wx-test\\pages\\agreement\\index.wxss", "name": "index.wxss", "isDirectory": false, "importance": 2, "dependencies": [], "packageDependencies": [], "dependents": []}]}, {"path": "C:\\Users\\<USER>\\Desktop\\代码库\\wordpress\\new_site\\app\\public\\melon-english-wx-test\\pages\\api-test", "name": "api-test", "isDirectory": true, "children": [{"path": "C:\\Users\\<USER>\\Desktop\\代码库\\wordpress\\new_site\\app\\public\\melon-english-wx-test\\pages\\api-test\\get_user_api", "name": "get_user_api", "isDirectory": false, "importance": 0, "dependencies": [], "packageDependencies": [], "dependents": []}, {"path": "C:\\Users\\<USER>\\Desktop\\代码库\\wordpress\\new_site\\app\\public\\melon-english-wx-test\\pages\\api-test\\index.js", "name": "index.js", "isDirectory": false, "importance": 4, "dependencies": [], "packageDependencies": [], "dependents": []}, {"path": "C:\\Users\\<USER>\\Desktop\\代码库\\wordpress\\new_site\\app\\public\\melon-english-wx-test\\pages\\api-test\\index.json", "name": "index.json", "isDirectory": false, "importance": 3, "dependencies": [], "packageDependencies": [], "dependents": []}, {"path": "C:\\Users\\<USER>\\Desktop\\代码库\\wordpress\\new_site\\app\\public\\melon-english-wx-test\\pages\\api-test\\index.wxml", "name": "index.wxml", "isDirectory": false, "importance": 2, "dependencies": [], "packageDependencies": [], "dependents": []}, {"path": "C:\\Users\\<USER>\\Desktop\\代码库\\wordpress\\new_site\\app\\public\\melon-english-wx-test\\pages\\api-test\\index.wxss", "name": "index.wxss", "isDirectory": false, "importance": 2, "dependencies": [], "packageDependencies": [], "dependents": []}, {"path": "C:\\Users\\<USER>\\Desktop\\代码库\\wordpress\\new_site\\app\\public\\melon-english-wx-test\\pages\\api-test\\index.wxss.bak", "name": "index.wxss.bak", "isDirectory": false, "importance": 0, "dependencies": [], "packageDependencies": [], "dependents": []}, {"path": "C:\\Users\\<USER>\\Desktop\\代码库\\wordpress\\new_site\\app\\public\\melon-english-wx-test\\pages\\api-test\\login.php", "name": "login.php", "isDirectory": false, "importance": 2, "dependencies": [], "packageDependencies": [], "dependents": []}, {"path": "C:\\Users\\<USER>\\Desktop\\代码库\\wordpress\\new_site\\app\\public\\melon-english-wx-test\\pages\\api-test\\login_api", "name": "login_api", "isDirectory": false, "importance": 0, "dependencies": [], "packageDependencies": [], "dependents": []}]}, {"path": "C:\\Users\\<USER>\\Desktop\\代码库\\wordpress\\new_site\\app\\public\\melon-english-wx-test\\pages\\auth", "name": "auth", "isDirectory": true, "children": [{"path": "C:\\Users\\<USER>\\Desktop\\代码库\\wordpress\\new_site\\app\\public\\melon-english-wx-test\\pages\\auth\\index.js", "name": "index.js", "isDirectory": false, "importance": 6, "dependencies": ["C:\\Users\\<USER>\\Desktop\\代码库\\wordpress\\new_site\\app\\public\\melon-english-wx-test\\config\\index.js", "C:\\Users\\<USER>\\Desktop\\代码库\\wordpress\\new_site\\app\\public\\melon-english-wx-test\\utils\\request.js"], "packageDependencies": [], "dependents": []}, {"path": "C:\\Users\\<USER>\\Desktop\\代码库\\wordpress\\new_site\\app\\public\\melon-english-wx-test\\pages\\auth\\index.json", "name": "index.json", "isDirectory": false, "importance": 3, "dependencies": [], "packageDependencies": [], "dependents": []}, {"path": "C:\\Users\\<USER>\\Desktop\\代码库\\wordpress\\new_site\\app\\public\\melon-english-wx-test\\pages\\auth\\index.ttml", "name": "index.ttml", "isDirectory": false, "importance": 2, "dependencies": [], "packageDependencies": [], "dependents": []}, {"path": "C:\\Users\\<USER>\\Desktop\\代码库\\wordpress\\new_site\\app\\public\\melon-english-wx-test\\pages\\auth\\index.ttss", "name": "index.ttss", "isDirectory": false, "importance": 2, "dependencies": [], "packageDependencies": [], "dependents": []}, {"path": "C:\\Users\\<USER>\\Desktop\\代码库\\wordpress\\new_site\\app\\public\\melon-english-wx-test\\pages\\auth\\index.wxml", "name": "index.wxml", "isDirectory": false, "importance": 2, "dependencies": [], "packageDependencies": [], "dependents": []}, {"path": "C:\\Users\\<USER>\\Desktop\\代码库\\wordpress\\new_site\\app\\public\\melon-english-wx-test\\pages\\auth\\index.wxss", "name": "index.wxss", "isDirectory": false, "importance": 2, "dependencies": [], "packageDependencies": [], "dependents": []}]}, {"path": "C:\\Users\\<USER>\\Desktop\\代码库\\wordpress\\new_site\\app\\public\\melon-english-wx-test\\pages\\course-detail", "name": "course-detail", "isDirectory": true, "children": [{"path": "C:\\Users\\<USER>\\Desktop\\代码库\\wordpress\\new_site\\app\\public\\melon-english-wx-test\\pages\\course-detail\\index.js", "name": "index.js", "isDirectory": false, "importance": 4, "dependencies": [], "packageDependencies": [], "dependents": []}, {"path": "C:\\Users\\<USER>\\Desktop\\代码库\\wordpress\\new_site\\app\\public\\melon-english-wx-test\\pages\\course-detail\\index.json", "name": "index.json", "isDirectory": false, "importance": 3, "dependencies": [], "packageDependencies": [], "dependents": []}, {"path": "C:\\Users\\<USER>\\Desktop\\代码库\\wordpress\\new_site\\app\\public\\melon-english-wx-test\\pages\\course-detail\\index.wxml", "name": "index.wxml", "isDirectory": false, "importance": 2, "dependencies": [], "packageDependencies": [], "dependents": []}, {"path": "C:\\Users\\<USER>\\Desktop\\代码库\\wordpress\\new_site\\app\\public\\melon-english-wx-test\\pages\\course-detail\\index.wxss", "name": "index.wxss", "isDirectory": false, "importance": 2, "dependencies": [], "packageDependencies": [], "dependents": []}]}, {"path": "C:\\Users\\<USER>\\Desktop\\代码库\\wordpress\\new_site\\app\\public\\melon-english-wx-test\\pages\\courses", "name": "courses", "isDirectory": true, "children": [{"path": "C:\\Users\\<USER>\\Desktop\\代码库\\wordpress\\new_site\\app\\public\\melon-english-wx-test\\pages\\courses\\index.js", "name": "index.js", "isDirectory": false, "importance": 4, "dependencies": [], "packageDependencies": [], "dependents": []}, {"path": "C:\\Users\\<USER>\\Desktop\\代码库\\wordpress\\new_site\\app\\public\\melon-english-wx-test\\pages\\courses\\index.json", "name": "index.json", "isDirectory": false, "importance": 3, "dependencies": [], "packageDependencies": [], "dependents": []}, {"path": "C:\\Users\\<USER>\\Desktop\\代码库\\wordpress\\new_site\\app\\public\\melon-english-wx-test\\pages\\courses\\index.wxml", "name": "index.wxml", "isDirectory": false, "importance": 2, "dependencies": [], "packageDependencies": [], "dependents": []}, {"path": "C:\\Users\\<USER>\\Desktop\\代码库\\wordpress\\new_site\\app\\public\\melon-english-wx-test\\pages\\courses\\index.wxss", "name": "index.wxss", "isDirectory": false, "importance": 2, "dependencies": [], "packageDependencies": [], "dependents": []}]}, {"path": "C:\\Users\\<USER>\\Desktop\\代码库\\wordpress\\new_site\\app\\public\\melon-english-wx-test\\pages\\daily-task", "name": "daily-task", "isDirectory": true, "children": [{"path": "C:\\Users\\<USER>\\Desktop\\代码库\\wordpress\\new_site\\app\\public\\melon-english-wx-test\\pages\\daily-task\\index.js", "name": "index.js", "isDirectory": false, "importance": 6, "dependencies": ["C:\\Users\\<USER>\\Desktop\\代码库\\wordpress\\new_site\\app\\public\\melon-english-wx-test\\mock\\dailyTask.js", "C:\\Users\\<USER>\\Desktop\\代码库\\wordpress\\new_site\\app\\public\\melon-english-wx-test\\utils\\request.js"], "packageDependencies": [], "dependents": []}, {"path": "C:\\Users\\<USER>\\Desktop\\代码库\\wordpress\\new_site\\app\\public\\melon-english-wx-test\\pages\\daily-task\\index.json", "name": "index.json", "isDirectory": false, "importance": 3, "dependencies": [], "packageDependencies": [], "dependents": []}, {"path": "C:\\Users\\<USER>\\Desktop\\代码库\\wordpress\\new_site\\app\\public\\melon-english-wx-test\\pages\\daily-task\\index.wxml", "name": "index.wxml", "isDirectory": false, "importance": 2, "dependencies": [], "packageDependencies": [], "dependents": []}, {"path": "C:\\Users\\<USER>\\Desktop\\代码库\\wordpress\\new_site\\app\\public\\melon-english-wx-test\\pages\\daily-task\\index.wxss", "name": "index.wxss", "isDirectory": false, "importance": 2, "dependencies": [], "packageDependencies": [], "dependents": []}]}, {"path": "C:\\Users\\<USER>\\Desktop\\代码库\\wordpress\\new_site\\app\\public\\melon-english-wx-test\\pages\\index", "name": "index", "isDirectory": true, "children": [{"path": "C:\\Users\\<USER>\\Desktop\\代码库\\wordpress\\new_site\\app\\public\\melon-english-wx-test\\pages\\index\\index.js", "name": "index.js", "isDirectory": false, "importance": 4, "dependencies": [], "packageDependencies": [], "dependents": []}, {"path": "C:\\Users\\<USER>\\Desktop\\代码库\\wordpress\\new_site\\app\\public\\melon-english-wx-test\\pages\\index\\index.json", "name": "index.json", "isDirectory": false, "importance": 3, "dependencies": [], "packageDependencies": [], "dependents": []}, {"path": "C:\\Users\\<USER>\\Desktop\\代码库\\wordpress\\new_site\\app\\public\\melon-english-wx-test\\pages\\index\\index.ttml", "name": "index.ttml", "isDirectory": false, "importance": 2, "dependencies": [], "packageDependencies": [], "dependents": []}, {"path": "C:\\Users\\<USER>\\Desktop\\代码库\\wordpress\\new_site\\app\\public\\melon-english-wx-test\\pages\\index\\index.ttss", "name": "index.ttss", "isDirectory": false, "importance": 2, "dependencies": [], "packageDependencies": [], "dependents": []}, {"path": "C:\\Users\\<USER>\\Desktop\\代码库\\wordpress\\new_site\\app\\public\\melon-english-wx-test\\pages\\index\\index.wxml", "name": "index.wxml", "isDirectory": false, "importance": 2, "dependencies": [], "packageDependencies": [], "dependents": []}, {"path": "C:\\Users\\<USER>\\Desktop\\代码库\\wordpress\\new_site\\app\\public\\melon-english-wx-test\\pages\\index\\index.wxss", "name": "index.wxss", "isDirectory": false, "importance": 2, "dependencies": [], "packageDependencies": [], "dependents": []}]}, {"path": "C:\\Users\\<USER>\\Desktop\\代码库\\wordpress\\new_site\\app\\public\\melon-english-wx-test\\pages\\lessons", "name": "lessons", "isDirectory": true, "children": [{"path": "C:\\Users\\<USER>\\Desktop\\代码库\\wordpress\\new_site\\app\\public\\melon-english-wx-test\\pages\\lessons\\index.js", "name": "index.js", "isDirectory": false, "importance": 4, "dependencies": [], "packageDependencies": [], "dependents": []}, {"path": "C:\\Users\\<USER>\\Desktop\\代码库\\wordpress\\new_site\\app\\public\\melon-english-wx-test\\pages\\lessons\\index.json", "name": "index.json", "isDirectory": false, "importance": 3, "dependencies": [], "packageDependencies": [], "dependents": []}, {"path": "C:\\Users\\<USER>\\Desktop\\代码库\\wordpress\\new_site\\app\\public\\melon-english-wx-test\\pages\\lessons\\index.wxml", "name": "index.wxml", "isDirectory": false, "importance": 2, "dependencies": [], "packageDependencies": [], "dependents": []}, {"path": "C:\\Users\\<USER>\\Desktop\\代码库\\wordpress\\new_site\\app\\public\\melon-english-wx-test\\pages\\lessons\\index.wxss", "name": "index.wxss", "isDirectory": false, "importance": 2, "dependencies": [], "packageDependencies": [], "dependents": []}]}, {"path": "C:\\Users\\<USER>\\Desktop\\代码库\\wordpress\\new_site\\app\\public\\melon-english-wx-test\\pages\\logs", "name": "logs", "isDirectory": true, "children": [{"path": "C:\\Users\\<USER>\\Desktop\\代码库\\wordpress\\new_site\\app\\public\\melon-english-wx-test\\pages\\logs\\logs.js", "name": "logs.js", "isDirectory": false, "importance": 3, "dependencies": ["C:\\Users\\<USER>\\Desktop\\代码库\\wordpress\\new_site\\app\\public\\melon-english-wx-test\\utils\\util.js"], "packageDependencies": [], "dependents": []}, {"path": "C:\\Users\\<USER>\\Desktop\\代码库\\wordpress\\new_site\\app\\public\\melon-english-wx-test\\pages\\logs\\logs.json", "name": "logs.json", "isDirectory": false, "importance": 1, "dependencies": [], "packageDependencies": [], "dependents": []}, {"path": "C:\\Users\\<USER>\\Desktop\\代码库\\wordpress\\new_site\\app\\public\\melon-english-wx-test\\pages\\logs\\logs.ttml", "name": "logs.ttml", "isDirectory": false, "importance": 0, "dependencies": [], "packageDependencies": [], "dependents": []}, {"path": "C:\\Users\\<USER>\\Desktop\\代码库\\wordpress\\new_site\\app\\public\\melon-english-wx-test\\pages\\logs\\logs.ttss", "name": "logs.ttss", "isDirectory": false, "importance": 0, "dependencies": [], "packageDependencies": [], "dependents": []}, {"path": "C:\\Users\\<USER>\\Desktop\\代码库\\wordpress\\new_site\\app\\public\\melon-english-wx-test\\pages\\logs\\logs.wxml", "name": "logs.wxml", "isDirectory": false, "importance": 0, "dependencies": [], "packageDependencies": [], "dependents": []}, {"path": "C:\\Users\\<USER>\\Desktop\\代码库\\wordpress\\new_site\\app\\public\\melon-english-wx-test\\pages\\logs\\logs.wxss", "name": "logs.wxss", "isDirectory": false, "importance": 0, "dependencies": [], "packageDependencies": [], "dependents": []}]}, {"path": "C:\\Users\\<USER>\\Desktop\\代码库\\wordpress\\new_site\\app\\public\\melon-english-wx-test\\pages\\me", "name": "me", "isDirectory": true, "children": [{"path": "C:\\Users\\<USER>\\Desktop\\代码库\\wordpress\\new_site\\app\\public\\melon-english-wx-test\\pages\\me\\index.js", "name": "index.js", "isDirectory": false, "importance": 4, "dependencies": [], "packageDependencies": [], "dependents": []}, {"path": "C:\\Users\\<USER>\\Desktop\\代码库\\wordpress\\new_site\\app\\public\\melon-english-wx-test\\pages\\me\\index.json", "name": "index.json", "isDirectory": false, "importance": 3, "dependencies": [], "packageDependencies": [], "dependents": []}, {"path": "C:\\Users\\<USER>\\Desktop\\代码库\\wordpress\\new_site\\app\\public\\melon-english-wx-test\\pages\\me\\index.ttml", "name": "index.ttml", "isDirectory": false, "importance": 2, "dependencies": [], "packageDependencies": [], "dependents": []}, {"path": "C:\\Users\\<USER>\\Desktop\\代码库\\wordpress\\new_site\\app\\public\\melon-english-wx-test\\pages\\me\\index.ttss", "name": "index.ttss", "isDirectory": false, "importance": 2, "dependencies": [], "packageDependencies": [], "dependents": []}, {"path": "C:\\Users\\<USER>\\Desktop\\代码库\\wordpress\\new_site\\app\\public\\melon-english-wx-test\\pages\\me\\index.wxml", "name": "index.wxml", "isDirectory": false, "importance": 2, "dependencies": [], "packageDependencies": [], "dependents": []}, {"path": "C:\\Users\\<USER>\\Desktop\\代码库\\wordpress\\new_site\\app\\public\\melon-english-wx-test\\pages\\me\\index.wxss", "name": "index.wxss", "isDirectory": false, "importance": 2, "dependencies": [], "packageDependencies": [], "dependents": []}]}, {"path": "C:\\Users\\<USER>\\Desktop\\代码库\\wordpress\\new_site\\app\\public\\melon-english-wx-test\\pages\\pay", "name": "pay", "isDirectory": true, "children": [{"path": "C:\\Users\\<USER>\\Desktop\\代码库\\wordpress\\new_site\\app\\public\\melon-english-wx-test\\pages\\pay\\index.js", "name": "index.js", "isDirectory": false, "importance": 4, "dependencies": [], "packageDependencies": [], "dependents": []}, {"path": "C:\\Users\\<USER>\\Desktop\\代码库\\wordpress\\new_site\\app\\public\\melon-english-wx-test\\pages\\pay\\index.json", "name": "index.json", "isDirectory": false, "importance": 3, "dependencies": [], "packageDependencies": [], "dependents": []}, {"path": "C:\\Users\\<USER>\\Desktop\\代码库\\wordpress\\new_site\\app\\public\\melon-english-wx-test\\pages\\pay\\index.wxml", "name": "index.wxml", "isDirectory": false, "importance": 2, "dependencies": [], "packageDependencies": [], "dependents": []}, {"path": "C:\\Users\\<USER>\\Desktop\\代码库\\wordpress\\new_site\\app\\public\\melon-english-wx-test\\pages\\pay\\index.wxss", "name": "index.wxss", "isDirectory": false, "importance": 2, "dependencies": [], "packageDependencies": [], "dependents": []}]}, {"path": "C:\\Users\\<USER>\\Desktop\\代码库\\wordpress\\new_site\\app\\public\\melon-english-wx-test\\pages\\review", "name": "review", "isDirectory": true, "children": [{"path": "C:\\Users\\<USER>\\Desktop\\代码库\\wordpress\\new_site\\app\\public\\melon-english-wx-test\\pages\\review\\index.js", "name": "index.js", "isDirectory": false, "importance": 4, "dependencies": [], "packageDependencies": [], "dependents": []}, {"path": "C:\\Users\\<USER>\\Desktop\\代码库\\wordpress\\new_site\\app\\public\\melon-english-wx-test\\pages\\review\\index.json", "name": "index.json", "isDirectory": false, "importance": 3, "dependencies": [], "packageDependencies": [], "dependents": []}, {"path": "C:\\Users\\<USER>\\Desktop\\代码库\\wordpress\\new_site\\app\\public\\melon-english-wx-test\\pages\\review\\index.wxml", "name": "index.wxml", "isDirectory": false, "importance": 2, "dependencies": [], "packageDependencies": [], "dependents": []}, {"path": "C:\\Users\\<USER>\\Desktop\\代码库\\wordpress\\new_site\\app\\public\\melon-english-wx-test\\pages\\review\\index.wxss", "name": "index.wxss", "isDirectory": false, "importance": 2, "dependencies": [], "packageDependencies": [], "dependents": []}]}, {"path": "C:\\Users\\<USER>\\Desktop\\代码库\\wordpress\\new_site\\app\\public\\melon-english-wx-test\\pages\\settings", "name": "settings", "isDirectory": true, "children": [{"path": "C:\\Users\\<USER>\\Desktop\\代码库\\wordpress\\new_site\\app\\public\\melon-english-wx-test\\pages\\settings\\index.js", "name": "index.js", "isDirectory": false, "importance": 5, "dependencies": ["C:\\Users\\<USER>\\Desktop\\代码库\\wordpress\\new_site\\app\\public\\melon-english-wx-test\\utils\\request.js"], "packageDependencies": [], "dependents": []}, {"path": "C:\\Users\\<USER>\\Desktop\\代码库\\wordpress\\new_site\\app\\public\\melon-english-wx-test\\pages\\settings\\index.json", "name": "index.json", "isDirectory": false, "importance": 3, "dependencies": [], "packageDependencies": [], "dependents": []}, {"path": "C:\\Users\\<USER>\\Desktop\\代码库\\wordpress\\new_site\\app\\public\\melon-english-wx-test\\pages\\settings\\index.ttml", "name": "index.ttml", "isDirectory": false, "importance": 2, "dependencies": [], "packageDependencies": [], "dependents": []}, {"path": "C:\\Users\\<USER>\\Desktop\\代码库\\wordpress\\new_site\\app\\public\\melon-english-wx-test\\pages\\settings\\index.ttss", "name": "index.ttss", "isDirectory": false, "importance": 2, "dependencies": [], "packageDependencies": [], "dependents": []}, {"path": "C:\\Users\\<USER>\\Desktop\\代码库\\wordpress\\new_site\\app\\public\\melon-english-wx-test\\pages\\settings\\index.wxml", "name": "index.wxml", "isDirectory": false, "importance": 2, "dependencies": [], "packageDependencies": [], "dependents": []}, {"path": "C:\\Users\\<USER>\\Desktop\\代码库\\wordpress\\new_site\\app\\public\\melon-english-wx-test\\pages\\settings\\index.wxss", "name": "index.wxss", "isDirectory": false, "importance": 2, "dependencies": [], "packageDependencies": [], "dependents": []}]}, {"path": "C:\\Users\\<USER>\\Desktop\\代码库\\wordpress\\new_site\\app\\public\\melon-english-wx-test\\pages\\study", "name": "study", "isDirectory": true, "children": [{"path": "C:\\Users\\<USER>\\Desktop\\代码库\\wordpress\\new_site\\app\\public\\melon-english-wx-test\\pages\\study\\index.js", "name": "index.js", "isDirectory": false, "importance": 4, "dependencies": [], "packageDependencies": [], "dependents": []}, {"path": "C:\\Users\\<USER>\\Desktop\\代码库\\wordpress\\new_site\\app\\public\\melon-english-wx-test\\pages\\study\\index.json", "name": "index.json", "isDirectory": false, "importance": 3, "dependencies": [], "packageDependencies": [], "dependents": []}, {"path": "C:\\Users\\<USER>\\Desktop\\代码库\\wordpress\\new_site\\app\\public\\melon-english-wx-test\\pages\\study\\index.wxml", "name": "index.wxml", "isDirectory": false, "importance": 2, "dependencies": [], "packageDependencies": [], "dependents": []}, {"path": "C:\\Users\\<USER>\\Desktop\\代码库\\wordpress\\new_site\\app\\public\\melon-english-wx-test\\pages\\study\\index.wxss", "name": "index.wxss", "isDirectory": false, "importance": 2, "dependencies": [], "packageDependencies": [], "dependents": []}]}, {"path": "C:\\Users\\<USER>\\Desktop\\代码库\\wordpress\\new_site\\app\\public\\melon-english-wx-test\\pages\\测试接口", "name": "测试接口", "isDirectory": false, "importance": 0, "dependencies": [], "packageDependencies": [], "dependents": []}]}, {"path": "C:\\Users\\<USER>\\Desktop\\代码库\\wordpress\\new_site\\app\\public\\melon-english-wx-test\\project.config.json", "name": "project.config.json", "isDirectory": false, "importance": 1, "dependencies": [], "packageDependencies": [], "dependents": []}, {"path": "C:\\Users\\<USER>\\Desktop\\代码库\\wordpress\\new_site\\app\\public\\melon-english-wx-test\\project.miniapp.json", "name": "project.miniapp.json", "isDirectory": false, "importance": 1, "dependencies": [], "packageDependencies": [], "dependents": []}, {"path": "C:\\Users\\<USER>\\Desktop\\代码库\\wordpress\\new_site\\app\\public\\melon-english-wx-test\\project.private.config.json", "name": "project.private.config.json", "isDirectory": false, "importance": 1, "dependencies": [], "packageDependencies": [], "dependents": []}, {"path": "C:\\Users\\<USER>\\Desktop\\代码库\\wordpress\\new_site\\app\\public\\melon-english-wx-test\\prompts", "name": "prompts", "isDirectory": true, "children": [{"path": "C:\\Users\\<USER>\\Desktop\\代码库\\wordpress\\new_site\\app\\public\\melon-english-wx-test\\prompts\\慢思考智能体_0321", "name": "慢思考智能体_0321", "isDirectory": true, "children": [{"path": "C:\\Users\\<USER>\\Desktop\\代码库\\wordpress\\new_site\\app\\public\\melon-english-wx-test\\prompts\\慢思考智能体_0321\\README.txt", "name": "README.txt", "isDirectory": false, "importance": 0, "dependencies": [], "packageDependencies": [], "dependents": []}, {"path": "C:\\Users\\<USER>\\Desktop\\代码库\\wordpress\\new_site\\app\\public\\melon-english-wx-test\\prompts\\慢思考智能体_0321\\任务流程设计_0321.md", "name": "任务流程设计_0321.md", "isDirectory": false, "importance": 1, "dependencies": [], "packageDependencies": [], "dependents": []}, {"path": "C:\\Users\\<USER>\\Desktop\\代码库\\wordpress\\new_site\\app\\public\\melon-english-wx-test\\prompts\\慢思考智能体_0321\\慢思考提示词生成_0321.md", "name": "慢思考提示词生成_0321.md", "isDirectory": false, "importance": 1, "dependencies": [], "packageDependencies": [], "dependents": []}, {"path": "C:\\Users\\<USER>\\Desktop\\代码库\\wordpress\\new_site\\app\\public\\melon-english-wx-test\\prompts\\慢思考智能体_0321\\角色定义_0321.md", "name": "角色定义_0321.md", "isDirectory": false, "importance": 1, "dependencies": [], "packageDependencies": [], "dependents": []}]}, {"path": "C:\\Users\\<USER>\\Desktop\\代码库\\wordpress\\new_site\\app\\public\\melon-english-wx-test\\prompts\\提示词片段_321.md", "name": "提示词片段_321.md", "isDirectory": false, "importance": 1, "dependencies": [], "packageDependencies": [], "dependents": []}]}, {"path": "C:\\Users\\<USER>\\Desktop\\代码库\\wordpress\\new_site\\app\\public\\melon-english-wx-test\\sitemap.json", "name": "sitemap.json", "isDirectory": false, "importance": 1, "dependencies": [], "packageDependencies": [], "dependents": []}, {"path": "C:\\Users\\<USER>\\Desktop\\代码库\\wordpress\\new_site\\app\\public\\melon-english-wx-test\\utils", "name": "utils", "isDirectory": true, "children": [{"path": "C:\\Users\\<USER>\\Desktop\\代码库\\wordpress\\new_site\\app\\public\\melon-english-wx-test\\utils\\request.js", "name": "request.js", "isDirectory": false, "importance": 6, "dependencies": ["C:\\Users\\<USER>\\Desktop\\代码库\\wordpress\\new_site\\app\\public\\melon-english-wx-test\\config\\index.js"], "packageDependencies": [], "dependents": ["C:\\Users\\<USER>\\Desktop\\代码库\\wordpress\\new_site\\app\\public\\melon-english-wx-test\\pages\\auth\\index.js", "C:\\Users\\<USER>\\Desktop\\代码库\\wordpress\\new_site\\app\\public\\melon-english-wx-test\\pages\\daily-task\\index.js", "C:\\Users\\<USER>\\Desktop\\代码库\\wordpress\\new_site\\app\\public\\melon-english-wx-test\\pages\\settings\\index.js"]}, {"path": "C:\\Users\\<USER>\\Desktop\\代码库\\wordpress\\new_site\\app\\public\\melon-english-wx-test\\utils\\util.js", "name": "util.js", "isDirectory": false, "importance": 3, "dependencies": [], "packageDependencies": [], "dependents": ["C:\\Users\\<USER>\\Desktop\\代码库\\wordpress\\new_site\\app\\public\\melon-english-wx-test\\pages\\logs\\logs.js"]}]}]}}