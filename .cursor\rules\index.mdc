---
description: 
globs: 
alwaysApply: true
---
# 项目规则索引

**规则目录:** `C:\Users\<USER>\Desktop\代码库\wordpress\new_site\app\public\.cursor\rules`

本文档是此代码库中所有活动 Cursor 规则的中心索引，按类别组织。这些规则定义了开发流程、结构和指南。

## 通用与核心 (General & Core)

基础的项目指南和结构。

| 规则文件                   | 描述                                     |
| :------------------------- | :--------------------------------------- |
| [cursor_rules.mdc](mdc:.cursor/rules/cursor_rules.mdc)          | 撰写 cursor_rules 规则         |
| [file_structure.mdc](mdc:.cursor/rules/file_structure.mdc)        | 项目文件结构与特定目录说明 (WordPress 主题) |

## API 开发 (API Development)

与 API 设计、创建和文档相关的指南。

| 规则文件                           | 描述                                     |
| :--------------------------------- | :--------------------------------------- |
| [api-creation-standard.mdc](mdc:.cursor/rules/api-creation-standard.mdc)         | (主题) API 接口创建标准流程                      |
| [wechat-pay-orders-api-standard.mdc](mdc:.cursor/rules/wechat-pay-orders-api-standard.mdc) | (插件 wechat-pay-orders) API 接口创建标准 |
| [api-documentation-standard.mdc](mdc:.cursor/rules/api-documentation-standard.mdc)    | API 接口文档编写标准                      |
| [course-stage-data-compatibility.mdc](mdc:.cursor/rules/course-stage-data-compatibility.mdc) | 课程阶段数据 (JSON) 兼容性处理规则        |
| [course-stage-json-for-mini-program.mdc](mdc:.cursor/rules/course-stage-json-for-mini-program.mdc) | 新版课程关卡 JSON 文件定位及处理规则 |
| [review-mes.mdc](mdc:.cursor/rules/review-mes.mdc)                      | 用户复习计划 JSON 文件定位及处理规则      |

## 代码规范 (Coding Standards)

代码编写和注释相关的规范。

| 规则文件                         | 描述                                     |
| :------------------------------- | :--------------------------------------- |
| [code-commenting-standard.mdc](mdc:.cursor/rules/code-commenting-standard.mdc)    | 代码注释编写标准与习惯                    |

## 前端 (Frontend)

与前端项目（如微信小程序）相关的指南。

| 规则文件                                  | 描述                                     |
| :---------------------------------------- | :--------------------------------------- |
| [melon-english-wx-test-structure.mdc](mdc:.cursor/rules/melon-english-wx-test-structure.mdc)      | melon-english-wx-test 小程序结构指南     |
| [vue-frontend-development-sop.mdc](mdc:.cursor/rules/vue-frontend-development-sop.mdc)         | Vue 前端项目新页面开发标准流程 (SOP)      |
| [vue-frontend-structure.mdc](mdc:.cursor/rules/vue-frontend-structure.mdc)         | vue-frontend 项目技术栈、文件结构与开发模式指南 |


</rewritten_file>