---
description: 
globs: 
alwaysApply: true
---
# review-mes 规则

该规则用于帮助 AI 定位和理解用户复习计划 JSON 数据及其处理流程：

- **文件路径:** `wp-content/themes/shuimitao.online/json/review_mes.json`
- **数据库字段 (usermeta):** `review_mes`，存储于用户表的自定义字段中。
- **获取方式:** 在 WordPress 环境下，通过以下代码获取原始 JSON 字符串：
  ```php
  $json = get_user_meta($user_id, 'review_mes', true);
  ```

- **JSON 特征:**
  - 顶层为数组，每个元素代表一个复习项目对象。
  - 主要字段：
    - `post_id` (int): 课程/文章 ID。
    - `finishTimes` (int): 已完成复习次数。
    - `model` (int): 课程分类 ID。
    - `reviewTime` (int): Unix 时间戳，标记下次复习时间（秒）。
    - `word_arr` (array): 与单词练习相关的候选数据数组。
    - `sentence_arr` (array): 与句子练习相关的候选数据数组。

- **示例 JSON:**
  ```json
  [
      {
          "post_id": 30201,
          "finishTimes": 1,
          "model": 43,
          "reviewTime": 1745366299,
          "word_arr": [],
          "sentence_arr": []
      },
      {
          "post_id": 22339,
          "finishTimes": 3,
          "model": 43,
          "reviewTime": 1745366299,
          "word_arr": [],
          "sentence_arr": []
      }
  ]
  ```

- **处理流程:**
  1. 在 `ReviewPlanHandler` 构造函数中，使用 `get_user_meta($user_id, 'review_mes', true)` 获取并通过 `json_decode` 解析为 PHP 数组。
  2. 根据 `$reviewTime` 与当天凌晨(`$today_morning`)和明日凌晨(`$tomorrow_morning`)比较，将项目分类到：
     - `$todayReviewArr`: 今天需要复习。
     - `$notTodayReviewArr`: 未来复习。
     - `$finishReviewArr`: 已完成复习（`reviewTime == 0`）。
  3. 通过 `update_user_meta($user_id, 'review_mes', json_encode($review_mes_arr))` 保存更新后的复习计划。



