.container {
  padding: 40rpx;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  min-height: 100vh;
}

.logo {
  margin-bottom: 40rpx;
}

.logo image {
  width: 200rpx;
  height: 200rpx;
}

.title {
  font-size: 36rpx;
  font-weight: bold;
  margin-bottom: 60rpx;
}

.subtitle {
  font-size: 28rpx;
  color: #666;
  margin-bottom: 60rpx;
}

.login-btn {
  width: 85%;
  height: 88rpx;
  line-height: 88rpx;
  border-radius: 44rpx;
  font-size: 32rpx;
  background: linear-gradient(to right, #4caf50, #45a049);
  box-shadow: 0 4rpx 12rpx rgba(76, 175, 80, 0.3);
  margin-bottom: 30rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 0;
}

.login-btn::after {
  border: none;
}

.login-btn[disabled] {
  background: #cccccc !important;
  box-shadow: none;
  color: #ffffff;
}

.login-btn[disabled]::after {
  border: none;
}

.agreement {
  display: flex;
  align-items: center;
  font-size: 24rpx;
  color: #666;
}

.agreement checkbox {
  transform: scale(0.7);
  margin-right: 4rpx;
}

.agreement-text {
  margin-right: 4rpx;
}

.agreement-link {
  color: #4caf50;
  text-decoration: underline;
}
