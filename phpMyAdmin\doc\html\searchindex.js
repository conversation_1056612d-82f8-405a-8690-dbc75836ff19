Search.setIndex({"docnames": ["bookmarks", "charts", "config", "copyright", "credits", "developers", "faq", "glossary", "import_export", "index", "intro", "other", "privileges", "relations", "require", "security", "settings", "setup", "themes", "transformations", "two_factor", "user", "vendors"], "filenames": ["bookmarks.rst", "charts.rst", "config.rst", "copyright.rst", "credits.rst", "developers.rst", "faq.rst", "glossary.rst", "import_export.rst", "index.rst", "intro.rst", "other.rst", "privileges.rst", "relations.rst", "require.rst", "security.rst", "settings.rst", "setup.rst", "themes.rst", "transformations.rst", "two_factor.rst", "user.rst", "vendors.rst"], "titles": ["Bookmarks", "Charts", "Configuration", "Copyright", "Credits", "Developers Information", "FAQ - Frequently Asked Questions", "Glossary", "Import and export", "Welcome to phpMyAdmin\u2019s documentation!", "Introduction", "Other sources of information", "User management", "Relations", "Requirements", "Security policy", "Configuring phpMyAdmin", "Installation", "Custom Themes", "Transformations", "Two-factor authentication", "User Guide", "Distributing and packaging phpMyAdmin"], "terms": {"you": [0, 1, 2, 3, 4, 5, 6, 7, 8, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 22], "need": [0, 2, 8, 10, 12, 13, 14, 15, 17, 18, 19, 20], "have": [0, 2, 3, 4, 5, 7, 8, 10, 11, 12, 13, 14, 15, 17, 18, 19], "configur": [0, 4, 7, 9, 10, 13, 15, 19, 20, 21], "phpmyadmin": [0, 1, 2, 3, 4, 5, 7, 8, 10, 11, 12, 13, 14, 15, 18, 19, 20, 21], "storag": [0, 2, 4, 6, 7, 9, 13, 15, 16, 19, 20], "featur": [0, 2, 4, 5, 7, 8, 9, 11, 12, 13, 17, 18, 19], "ani": [0, 2, 3, 4, 7, 8, 10, 12, 13, 15, 17, 19], "queri": [0, 1, 4, 7, 8, 9, 10, 15, 17], "i": [0, 1, 2, 3, 4, 5, 7, 8, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 22], "execut": [0, 2, 7, 10, 15, 17], "can": [0, 1, 2, 3, 5, 7, 8, 10, 11, 12, 13, 15, 16, 17, 18, 19, 20, 22], "mark": [0, 2, 4, 7], "page": [0, 4, 7, 9, 10, 12, 13, 14, 17, 18, 19], "where": [0, 2, 3, 7, 8, 12, 15, 17, 19], "result": [0, 1, 2, 10, 17], "ar": [0, 1, 2, 4, 7, 8, 10, 12, 13, 15, 16, 18, 19, 20, 22], "displai": [0, 1, 4, 7, 8, 9, 10, 13, 14, 17, 18, 19], "find": [0, 2, 6, 11, 15, 16, 17], "button": [0, 2, 6, 7, 17, 18, 20], "label": [0, 1, 6, 7, 15], "thi": [0, 1, 2, 3, 4, 7, 8, 12, 13, 15, 16, 17, 18, 19, 20, 22], "just": [0, 2, 4, 7, 8, 13, 17, 20], "end": [0, 2, 6, 8, 17, 19], "As": [0, 2, 6, 15, 17], "soon": 0, "link": [0, 1, 2, 4, 6, 10, 12, 13, 15, 17, 18, 19], "databas": [0, 4, 7, 8, 9, 10, 13, 15, 16, 19, 21], "now": [0, 2, 7, 8, 13, 17], "access": [0, 2, 4, 7, 13, 14, 15, 17], "dropdown": [0, 6], "each": [0, 2, 3, 7, 8, 12, 17, 19], "box": [0, 9], "appear": [0, 2, 6, 15], "also": [0, 2, 4, 6, 7, 8, 10, 12, 15, 16, 17, 19, 22], "add": [0, 2, 4, 7, 8, 10, 12, 17], "placehold": 0, "done": [0, 2, 6, 17, 20], "insert": [0, 2, 4, 8, 17, 19], "sql": [0, 1, 4, 7, 9, 10, 11, 17, 19], "comment": [0, 2, 4, 7, 8, 17], "between": [0, 1, 2, 6], "The": [0, 2, 3, 4, 7, 8, 11, 13, 14, 15, 16, 17, 19, 20, 22], "special": [0, 2, 7, 8, 13, 15, 17, 19], "string": [0, 2, 4, 17, 19], "number": [0, 1, 2, 7, 8, 15, 17], "Be": 0, "awar": [0, 6, 17], "whole": [0, 2, 6, 15, 17], "minu": 0, "must": [0, 2, 6, 8, 10, 12, 13, 17, 20], "valid": [0, 2, 4, 6, 8, 10, 15, 16, 17], "itself": [0, 2, 6, 17], "otherwis": [0, 2, 6], "won": [0, 2], "t": [0, 2, 4, 10, 12, 17, 19], "abl": [0, 2, 4, 12, 15, 17, 20], "note": [0, 2, 6, 17, 19, 22], "text": [0, 4, 7, 9, 10, 17, 19], "case": [0, 2, 6, 12, 13, 17, 19], "sensit": [0, 2, 6, 15], "when": [0, 2, 4, 7, 8, 10, 13, 14, 15, 17, 19, 20], "everyth": [0, 6, 17], "type": [0, 1, 2, 4, 7, 8, 13, 17, 19], "input": [0, 2, 4, 17, 19], "replac": [0, 2, 6, 17, 18, 22], "your": [0, 2, 6, 8, 9, 10, 12, 13, 14, 15, 16, 18, 19], "rememb": [0, 2, 17], "els": [0, 2, 4, 6, 17], "remain": [0, 2], "wai": [0, 2, 4, 8, 13, 15, 16, 17, 18, 20], "strip": [0, 2], "char": [0, 2, 6, 15], "so": [0, 2, 4, 5, 8, 12, 13, 15, 17, 19, 20, 22], "variable1": 0, "AS": [0, 1, 17], "mynam": 0, "which": [0, 1, 2, 3, 7, 8, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19], "expand": [0, 2, 6], "enter": [0, 2, 17, 19, 20], "1": [0, 2, 7, 8, 9, 10, 14, 17, 22], "A": [0, 1, 2, 3, 4, 7, 9], "more": [0, 1, 2, 3, 4, 7, 8, 15, 17, 19, 20], "complex": [0, 2, 10], "exampl": [0, 4, 8, 9, 10, 15, 18, 19, 20, 21, 22], "sai": [0, 13, 19], "select": [0, 1, 2, 4, 8, 12, 13, 17, 18, 19], "name": [0, 2, 4, 7, 12, 13, 17, 18, 19], "address": [0, 6, 7, 17, 19], "from": [0, 1, 2, 4, 7, 8, 9, 10, 12, 13, 15, 18, 19, 20, 22], "AND": [0, 2], "like": [0, 2, 4, 8, 10, 13, 15, 17, 19], "If": [0, 2, 3, 6, 7, 8, 12, 13, 15, 16, 17, 18, 19], "wish": [0, 2, 7, 8, 12, 17], "full": [0, 2, 12, 17], "THE": [0, 19], "absenc": [0, 2, 6], "OF": 0, "space": [0, 2, 6], "construct": [0, 6], "later": [0, 2, 8, 17], "mai": [0, 2, 6, 7, 10, 17, 19], "lead": [0, 2, 6], "unexpect": [0, 2], "especi": [0, 2, 6, 17], "expans": [0, 2, 6], "express": [0, 2, 6, 7, 19], "ha": [0, 2, 6, 7, 8, 17, 18, 19], "same": [0, 1, 2, 12, 17], "6": [0, 2, 4, 7, 13, 17], "18": [0, 1], "why": [0, 2], "see": [0, 2, 3, 4, 8, 10, 12, 13, 14, 15, 17, 18, 19], "below": [0, 2, 7, 12, 13, 18, 19], "what": [0, 2, 12, 15, 17, 19], "22": [0, 1], "default": [0, 7, 9, 14, 17, 18, 19], "automat": [0, 2, 4, 7, 8, 10, 16, 17], "mode": [0, 4, 9, 14], "new": [1, 2, 4, 7, 8, 15, 17, 18, 19, 20, 21], "version": [1, 2, 3, 7, 8, 9, 10, 14, 18, 20, 22], "3": [1, 2, 4, 7, 8, 17, 22], "4": [1, 2, 4, 7, 8, 14, 17, 18, 20, 22], "0": [1, 2, 8, 9, 14, 17, 18, 20, 22], "sinc": [1, 2, 4, 6, 8, 14, 17, 20], "easili": [1, 2, 6, 8, 17], "gener": [1, 3, 4, 7, 8, 9, 12, 15, 17, 19, 20, 22], "click": [1, 2, 7, 12, 13, 15, 19, 20], "oper": [1, 2, 7, 8, 17, 19], "area": [1, 2], "window": [1, 2, 4, 7, 9, 19, 20], "layer": [1, 7, 16], "shown": [1, 2, 8, 13, 17, 18, 19], "custom": [1, 4, 8, 9, 12, 16, 19, 21, 22], "follow": [1, 2, 4, 7, 8, 10, 15, 17], "option": [1, 9, 15, 16, 17, 19], "allow": [1, 2, 4, 7, 8, 10, 12, 13, 17, 18, 19], "choos": [1, 2, 6, 17, 19], "support": [1, 2, 4, 7, 8, 9, 13, 14, 17, 18, 20], "onli": [1, 2, 4, 7, 8, 12, 13, 15, 16, 17, 19, 20], "applic": [1, 2, 7, 13, 15, 17, 21], "current": [1, 2, 4, 6, 7, 10, 13, 15, 17, 18, 22], "seri": [1, 7], "offer": [1, 2, 6, 7, 13, 16, 17], "x": [1, 2, 7, 17, 20], "axi": [1, 6], "field": [1, 4, 7, 8, 9, 13, 17, 18, 19], "main": [1, 4, 9, 12, 17, 18, 19], "multipl": [1, 4, 8, 10, 15, 17], "titl": [1, 8, 9, 17], "specifi": [1, 2, 4, 8, 17, 19], "abov": [1, 2, 6, 17, 19], "y": 1, "ax": 1, "start": [1, 2, 4, 8, 14, 17], "row": [1, 2, 4, 7, 8], "set": [1, 4, 7, 9, 10, 16, 17, 19, 20, 22], "drawn": [1, 19], "us": [1, 3, 4, 5, 7, 9, 10, 11, 12, 13, 14, 15, 16, 18, 19, 20, 21], "jqplot": 1, "jqueri": [1, 3, 22], "librari": [1, 2, 3, 4, 7, 9, 14, 17, 19], "simpl": [1, 2, 7, 8, 17, 21], "food": 1, "expens": 1, "1250": 1, "amount": [1, 2, 6, 19], "union": [1, 6], "accommod": 1, "500": [1, 2], "travel": [1, 17], "720": 1, "misc": 1, "220": 1, "And": [1, 4, 6, 17], "both": [1, 2, 6, 8, 13, 16, 17], "chat": 1, "stack": 1, "upon": [1, 16], "one": [1, 2, 7, 8, 10, 12, 13, 15, 17, 20, 22], "checkbox": [1, 2, 8, 12], "academi": 1, "dinosaur": 1, "99": 1, "rental_r": 1, "20": [1, 7], "replacement_cost": 1, "ac": [1, 6], "goldfing": 1, "12": [1, 17], "adapt": 1, "hole": [1, 2], "2": [1, 2, 3, 9, 14, 17, 19, 22], "affair": 1, "prejudic": 1, "26": [1, 17], "african": 1, "egg": 1, "identifi": [1, 2, 6, 7, 17], "movement": 1, "variabl": [1, 2, 19, 21], "": [1, 2, 3, 4, 7, 8, 10, 11, 12, 13, 14, 15, 17, 18, 19], "compar": 1, "anoth": [1, 2, 6, 7, 13, 17, 19], "data": [1, 2, 7, 10, 15, 16, 17, 19], "section": [1, 2, 5, 6, 15, 17, 19], "These": [1, 2, 6, 13, 19], "illustr": 1, "trend": 1, "underli": [1, 17, 18], "draw": 1, "smooth": 1, "while": [1, 2, 19], "take": [1, 2, 6, 10, 19], "distanc": 1, "date": [1, 2, 6, 8, 17], "time": [1, 2, 7, 15, 17, 20], "consider": [1, 17], "2006": 1, "01": 1, "08": [1, 15, 17], "2056": 1, "revenu": 1, "1378": 1, "cost": 1, "09": 1, "1898": 1, "2301": 1, "15": [1, 2], "1560": 1, "600": [1, 6, 17], "17": [1, 2, 10, 14], "3457": 1, "1565": 1, "2016": [1, 15, 17], "all": [2, 5, 6, 7, 8, 10, 12, 13, 15, 17, 18, 19, 22], "place": [2, 6, 17], "config": [2, 6, 16, 22], "inc": [2, 6, 7, 16, 17, 18, 22], "php": [2, 4, 7, 9, 10, 16, 17, 18, 19, 20, 22], "toplevel": 2, "file": [2, 3, 4, 7, 9, 10, 14, 16, 18, 21], "doe": [2, 10, 12, 14, 15, 17, 18, 19, 20], "exist": [2, 4, 7, 8, 15, 17, 19, 21], "pleas": [2, 6, 7, 8, 15, 17, 19, 22], "refer": [2, 6, 7, 13], "instal": [2, 7, 8, 9, 14, 15, 20], "creat": [2, 4, 7, 8, 10, 13, 19, 21], "contain": [2, 8, 13, 17, 18, 19, 22], "paramet": [2, 8, 17, 19], "want": [2, 8, 12, 17, 18, 19, 22], "chang": [2, 4, 7, 8, 10, 12, 14, 17, 18, 19, 22], "correspond": [2, 6, 17, 19], "valu": [2, 7, 8, 13, 17, 19], "intend": [2, 10, 15, 17, 22], "direct": [2, 6, 17, 19], "miss": [2, 7, 15, 19], "line": [2, 6, 7, 8, 17, 19], "over": [2, 6, 7, 12, 13, 17], "write": [2, 8], "here": [2, 6, 8, 17, 19], "relat": [2, 4, 7, 9, 10, 17, 19, 21], "color": 2, "themenam": [2, 17], "scss": [2, 18], "_variabl": [2, 18], "might": [2, 6, 8, 11, 12, 15, 17, 22], "footer": 2, "header": [2, 7, 8, 17], "site": [2, 6, 7, 11, 17], "specif": [2, 7, 8, 9, 19, 21], "code": [2, 4, 6, 7, 15, 17, 18, 20], "includ": [2, 3, 4, 7, 8, 10, 15, 17, 18, 19, 20, 22], "some": [2, 4, 7, 8, 15, 17, 19, 22], "distribut": [2, 3, 4, 9], "eg": [2, 15], "debian": 2, "ubuntu": 2, "store": [2, 4, 7, 10, 16, 17, 18, 19, 21], "etc": [2, 6, 8, 17, 19], "instead": [2, 6, 8, 17], "within": [2, 6, 12], "sourc": [2, 4, 5, 6, 7, 9, 17, 20, 21], "cfg": [2, 6, 8, 12, 13, 17, 18, 19, 20], "pmaabsoluteuri": [2, 6, 17], "5": [2, 4, 8, 14, 17, 22], "wa": [2, 4, 6, 17, 18, 19], "avail": [2, 3, 6, 7, 13, 15, 17, 19, 20, 22], "complet": [2, 4, 8, 15, 17], "url": [2, 7, 17, 18], "path": [2, 17, 22], "e": [2, 4, 6, 7, 8, 12, 15, 17, 19], "g": [2, 4, 8, 12, 15, 17], "http": [2, 3, 4, 7, 8, 14, 18], "www": [2, 3, 4, 6, 7, 17, 18], "net": [2, 3, 4, 6, 7, 15, 17, 18], "path_to_your_phpmyadmin_directori": 2, "most": [2, 4, 6, 7, 8, 10, 13, 17, 19, 20], "even": [2, 3, 6, 8, 10, 12, 17], "don": [2, 12, 17], "forget": [2, 6, 17], "trail": 2, "slash": 2, "advis": [2, 17], "try": [2, 17], "leav": [2, 6, 17, 19], "blank": [2, 19], "In": [2, 7, 8, 12, 13, 15, 17, 18], "detect": [2, 4, 6, 17], "proper": [2, 6, 8, 10, 13, 17, 19], "user": [2, 4, 7, 9, 15, 16, 19, 20, 22], "port": [2, 7, 17], "forward": 2, "revers": [2, 17], "proxi": [2, 17], "good": [2, 6, 17, 18, 19], "test": [2, 4, 6, 17, 20, 22], "tabl": [2, 4, 7, 10, 12, 13, 17, 19, 21], "There": [2, 6, 7, 13, 16, 17, 19, 20], "should": [2, 3, 4, 7, 8, 12, 15, 17, 18, 20, 22], "an": [2, 7, 8, 9, 13, 14, 15, 19, 20, 21], "error": [2, 4, 7, 8, 17], "messag": [2, 17], "troubl": [2, 7, 8], "auto": 2, "correct": [2, 17], "get": [2, 11, 13, 14, 15, 17, 18, 19], "autodetect": 2, "fail": [2, 17], "post": [2, 6, 17], "bug": [2, 4], "report": [2, 4, 6, 9], "our": [2, 5, 6, 15, 17, 18, 19], "tracker": [2, 6, 15], "we": [2, 3, 6, 15, 17, 22], "improv": [2, 4, 7, 8, 10, 15, 17], "40": 2, "via": [2, 12, 15, 17], "apach": [2, 7, 14, 17], "login": [2, 4, 14, 15, 16, 17, 20], "work": [2, 4, 13, 15, 17, 19], "drop": [2, 4, 8, 10, 12, 13, 19], "404": 2, "found": [2, 15, 17], "m": [2, 3, 4, 17], "ask": [2, 7, 9, 20], "log": [2, 7, 10, 15, 20], "again": [2, 13, 17], "wrong": [2, 13], "7": [2, 8, 14, 17], "than": [2, 4, 8, 10, 12, 17, 20], "onc": [2, 12, 13, 17, 20], "16": [2, 8, 17], "With": [2, 12, 17], "internet": [2, 7, 17], "explor": [2, 15, 17], "deni": [2, 4, 17], "javascript": [2, 4, 8, 14, 15, 17], "Or": [2, 17, 20], "cannot": [2, 8, 19], "make": [2, 5, 8, 15, 17, 18, 19], "under": [2, 3, 15, 17, 18, 22], "pmanorelation_disablewarn": 2, "boolean": 2, "fals": [2, 6, 17, 22], "lot": [2, 15], "master": [2, 11, 13, 17], "foreign": [2, 7, 10, 13], "pmadb": [2, 13, 17], "tri": [2, 6, 15, 17], "up": [2, 8, 17, 19], "look": [2, 6, 7, 13, 17, 19], "would": [2, 13, 15, 17, 18, 19], "analyz": [2, 4, 6], "those": [2, 12, 13, 16, 17, 19], "been": [2, 5, 6, 8, 17, 18], "disabl": [2, 6, 17, 20], "do": [2, 12, 13, 15, 17, 18, 19, 20], "true": [2, 17], "stop": [2, 6], "authlog": [2, 17], "8": [2, 7, 13, 17, 18, 20], "destin": [2, 7], "depend": [2, 6, 8, 17, 20, 22], "authlogsuccess": 2, "attempt": [2, 6, 15, 17], "accord": 2, "let": [2, 13], "syslog": [2, 17], "auth": [2, 17, 20], "facil": 2, "system": [2, 4, 6, 7, 8, 16, 17, 18, 19, 22], "var": [2, 6, 17, 22], "sapi": 2, "other": [2, 4, 5, 6, 7, 8, 9, 10, 12, 13, 15, 16, 17, 19, 21, 22], "treat": [2, 6, 7], "filenam": [2, 6, 19], "entri": [2, 6, 17], "written": [2, 5, 6, 10], "sure": [2, 17], "its": [2, 7, 8, 10, 15, 18], "permiss": [2, 6, 12, 17], "correctli": [2, 6, 8], "close": [2, 6, 17], "match": [2, 6, 8, 17, 18], "instruct": [2, 6, 19], "describ": [2, 7, 15, 16, 19], "tempdir": [2, 6, 17], "whether": [2, 12, 17, 22], "success": [2, 6, 15, 17], "suhosindisablewarn": [2, 6], "warn": [2, 17], "suhosin": 2, "logincookievaliditydisablewarn": 2, "session": [2, 4, 6, 14, 16, 17], "gc_maxlifetim": 2, "lower": [2, 6], "serverlibrarydifference_disablewarn": 2, "deprec": 2, "remov": [2, 4, 6, 10, 12, 17, 18], "well": [2, 4, 6, 10, 15, 17], "differ": [2, 4, 7, 10, 17, 19], "reservedworddisablewarn": 2, "column": [2, 4, 7, 10, 19], "word": [2, 6, 9], "reserv": 2, "turn": [2, 6], "off": [2, 6, 17], "longer": 2, "translationwarningthreshold": 2, "integ": [2, 17], "80": [2, 6, 10, 17], "show": [2, 6, 15, 17, 19], "about": [2, 7, 8, 9, 11, 17, 19], "incomplet": 2, "translat": [2, 9], "certain": [2, 7, 19], "threshold": 2, "senderrorreport": 2, "alwai": [2, 7, 15, 17, 19], "never": [2, 17], "behavior": [2, 17, 22], "whenev": [2, 6, 17], "sent": [2, 4, 7, 15], "team": [2, 15, 17], "agre": 2, "everytim": 2, "howev": [2, 6, 17, 19], "send": 2, "without": [2, 3, 17, 18, 19], "confirm": [2, 6, 20], "prefer": [2, 4, 17], "person": [2, 6, 7, 17], "charg": 2, "multi": [2, 4, 7, 9, 17], "userprefsdisallow": 2, "arrai": [2, 6, 17, 18, 19], "consoleenterexecut": 2, "press": 2, "ctrl": [2, 6, 10], "shift": [2, 6], "behaviour": [2, 6], "temporarili": [2, 6, 16], "interfac": [2, 4, 6, 7, 8, 12, 14, 16, 19], "allowthirdpartyfram": 2, "insid": [2, 6, 15, 17, 19, 21, 22], "frame": [2, 4, 6], "potenti": [2, 17], "secur": [2, 4, 7, 9, 12, 19, 21], "cross": [2, 7, 17], "script": [2, 4, 8, 19, 22], "attack": [2, 17], "clickjack": 2, "sameorigin": 2, "prevent": [2, 17], "document": [2, 7, 14, 17, 22], "unless": [2, 6, 17, 20], "belong": [2, 17], "domain": [2, 6, 22], "list": [2, 4, 7, 8, 15, 17, 20, 22], "administr": [2, 4, 6, 7, 10, 12, 15, 17], "therefor": 2, "ad": [2, 4, 6, 8, 10, 17, 19], "inform": [2, 3, 7, 8, 9, 10, 17, 19, 21], "first": [2, 4, 6, 7, 8, 12, 13, 15, 17, 19, 20], "host": [2, 4, 7, 17], "hostnam": [2, 6, 7, 17], "second": [2, 6, 12, 17, 19, 20], "definit": [2, 6, 11, 13, 18], "put": [2, 15, 17, 18, 19], "mani": [2, 4, 5, 7, 8, 10, 16, 17, 19], "copi": [2, 3, 10, 12, 17, 18], "block": [2, 6, 7, 17], "part": [2, 6, 7], "defin": [2, 6, 7, 12, 17, 19], "increment": 2, "sever": [2, 3, 6, 7, 8, 13, 16, 17, 19, 20, 22], "localhost": [2, 17], "th": 2, "possibl": [2, 5, 8, 15, 17, 19], "mydb": [2, 6], "org": [2, 3, 4, 6, 7, 8, 17], "127": [2, 6], "192": [2, 6, 17], "168": [2, 6, 17], "10": [2, 17], "ipv6": [2, 7], "2001": [2, 3, 4], "cdba": 2, "0000": 2, "3257": 2, "9652": 2, "dot": 2, "pipe": 2, "empti": [2, 17], "handl": [2, 6, 8, 10, 12, 17, 18, 19], "socket": [2, 7, 17], "base": [2, 4, 7, 8, 14, 15, 17, 20], "protocol": [2, 7, 17], "To": [2, 4, 6, 8, 12, 13, 14, 15, 17, 18, 19, 20], "tcp": [2, 6, 7], "network": [2, 6, 7, 8, 17], "db": [2, 4, 7, 17], "com": [2, 3, 4, 6, 8, 14, 17], "dev": [2, 6, 8, 17], "doc": [2, 6, 7, 8, 14, 17], "refman": [2, 6, 8], "en": [2, 6, 7, 8, 17], "html": [2, 4, 8, 15, 17, 19], "3306": [2, 6, 17], "ignor": [2, 6], "real": [2, 6, 13], "determin": [2, 8, 12, 17], "check": [2, 3, 4, 5, 6, 7, 10, 12, 17, 19, 22], "command": [2, 6, 17, 20], "client": [2, 7, 9, 17], "issu": [2, 9], "statu": [2, 4, 6], "among": [2, 8], "effect": [2, 11, 19], "enabl": [2, 7, 12, 14, 17, 18, 19], "extens": [2, 4, 7, 10, 14, 17], "none": 2, "appli": [2, 6, 13, 17, 19], "strongli": [2, 14], "recommend": [2, 6, 7, 8, 14, 17], "mysqli": [2, 4, 7, 10], "ssl_kei": [2, 17], "ssl_cert": [2, 17], "ssl_ca": [2, 17], "ssl_ca_path": [2, 17], "ssl_cipher": [2, 17], "ssl_verifi": [2, 17], "null": [2, 17], "kei": [2, 4, 7, 9, 13, 15, 17, 21], "For": [2, 4, 6, 8, 15, 17, 18, 19, 22], "pem": 2, "certif": [2, 17], "ca": [2, 4, 17], "trust": [2, 17], "format": [2, 4, 7, 8, 10, 17, 19], "cipher": [2, 7], "nativ": [2, 13], "driver": 2, "mysqlnd": 2, "self": 2, "sign": [2, 17], "chanc": 2, "due": [2, 6, 17], "verifi": [2, 6, 9], "cn": 2, "verif": [2, 6, 17], "defeat": 2, "purpos": [2, 3, 7, 8, 15, 20], "vulner": [2, 6, 9, 17], "man": 2, "middl": 2, "flag": 2, "connect_typ": 2, "decid": [2, 6], "could": [2, 8, 12, 15, 17], "accordingli": [2, 6, 19], "It": [2, 4, 8, 17, 18, 19, 20], "nearli": 2, "guarante": 2, "platform": [2, 6, 7, 17], "machin": [2, 7, 8, 17], "compress": [2, 7, 8, 10], "experiment": 2, "controlhost": [2, 17], "permit": [2, 12], "altern": [2, 6, 7, 17], "hold": [2, 6], "control_": 2, "controlport": [2, 17], "controlus": [2, 6, 17], "controlpass": [2, 6, 17], "account": [2, 6, 10, 12, 17], "singl": [2, 7, 8, 17], "share": [2, 17], "give": [2, 17, 22], "were": [2, 4, 6], "call": [2, 6, 7, 10, 13, 17, 19], "stduser": 2, "stdpass": 2, "mix": 2, "control": [2, 4, 7, 12, 15, 17], "prefix": [2, 6], "aspect": [2, 14, 22], "control_ssl": 2, "secret": 2, "control_ssl_kei": 2, "control_ssl_cert": 2, "cert": [2, 17], "author": [2, 4, 6, 8, 17, 18], "control_ssl_ca": 2, "auth_typ": [2, 17], "plain": [2, 6, 17, 19], "old": [2, 6, 11, 17], "usernam": [2, 10, 12, 17], "password": [2, 10, 12, 17], "help": [2, 4, 5, 17], "prepar": [2, 7, 15, 17], "suppli": [2, 17], "auth_http_realm": 2, "realm": [2, 17], "explicitli": [2, 17], "combin": [2, 6, 8], "either": [2, 6, 8, 15, 16, 17, 19], "verbos": [2, 6, 17], "auth_swekey_config": 2, "auth_feebee_config": 2, "renam": [2, 8, 10, 17, 22], "befor": [2, 6, 8, 15, 18, 19], "releas": [2, 4, 6, 9, 15], "becaus": [2, 4, 17, 18, 19], "mainten": [2, 4, 10], "swekei": 2, "id": [2, 4, 6, 15, 17], "hardwar": [2, 21], "deactiv": [2, 6], "root": [2, 12, 17], "pair": [2, 17], "nopassword": 2, "produc": [2, 8, 13], "togeth": [2, 15, 17], "still": [2, 12, 15, 17], "fallback": 2, "method": [2, 7, 8, 13, 17, 19], "allownopassword": [2, 17], "only_db": 2, "wildcard": [2, 6], "charact": [2, 7, 8, 19], "_": [2, 19], "liter": [2, 6], "instanc": 2, "escap": [2, 6, 15], "them": [2, 7, 16, 17, 19, 22], "my": [2, 4, 13, 17], "_db": 2, "my_db": 2, "effici": [2, 6], "load": [2, 4, 7, 10, 16, 17], "latter": 2, "request": [2, 7, 17], "build": [2, 6, 17, 19], "But": [2, 8, 13, 15, 17], "privileg": [2, 4, 10, 15, 21], "rule": [2, 6, 17], "mean": [2, 15, 17, 19], "db1": [2, 17], "db2": [2, 17], "previou": [2, 6, 17], "order": [2, 6, 7, 8, 9, 13, 17], "hide_db": 2, "regular": [2, 4, 7, 19], "hide": [2, 17], "unprivileg": [2, 17], "letter": [2, 6, 8], "pcre": [2, 7], "pattern": 2, "syntax": [2, 6], "portion": 2, "manual": [2, 6, 7, 19, 20], "pull": [2, 17], "down": [2, 12, 13, 19], "menu": [2, 6, 19, 21], "non": [2, 8], "u": [2, 6, 15, 19], "ascii": [2, 19], "benefit": [2, 6], "quick": [2, 6, 7, 9], "central": [2, 4, 17], "bookmarkt": 2, "bookmark": [2, 4, 9, 10, 17, 21], "often": [2, 6, 8, 17], "run": [2, 7, 10, 15, 20], "usag": [2, 12, 17, 21], "function": [2, 4, 7, 10, 12, 17, 19], "clickabl": [2, 13], "point": [2, 6, 7, 17], "tool": [2, 4, 5, 10, 15, 17], "tip": [2, 6], "move": 2, "mous": [2, 6, 13], "table_info": 2, "how": [2, 13, 17, 19], "21": [2, 17], "properti": [2, 4, 6, 19], "referenti": [2, 10], "integr": [2, 4, 6, 10, 17], "join": [2, 4, 6], "schema": [2, 4, 8, 13], "table_coord": [2, 6, 13], "numer": [2, 4, 6], "normal": [2, 4, 6, 17, 19], "open": [2, 5, 6, 7, 10, 13, 14, 17], "view": [2, 4, 7, 8, 10, 12, 21], "master_db": 2, "foreign_db": 2, "futur": [2, 6], "cursor": 2, "pma__table_info": [2, 6], "layout": [2, 10, 13], "next": [2, 6, 17], "That": [2, 6], "furthermor": 2, "requir": [2, 8, 9, 10, 15, 17, 18, 19, 20], "pdf_page": [2, 6], "addit": [2, 4, 7, 8, 15, 17, 18], "detail": [2, 3, 6, 15, 17], "further": [2, 4, 6, 16], "visual": [2, 4], "two": [2, 7, 9, 13, 17, 19, 21], "coordin": 2, "output": [2, 4, 6, 8, 19], "designer_coord": 2, "posit": [2, 7], "delet": [2, 4, 17, 18, 21], "pma__designer_coord": 2, "column_info": [2, 19], "content": [2, 7, 8, 9, 15, 17, 19, 22], "updat": [2, 4, 6, 17], "printview": [2, 4], "consequ": 2, "embed": [2, 8], "thei": [2, 4, 12, 17, 19, 22], "dump": [2, 4, 8, 10], "relev": 2, "mime": [2, 4, 7, 11, 19], "three": [2, 6, 17, 19], "mimetyp": [2, 19], "transformation_opt": [2, 19], "orient": [2, 7], "introduc": [2, 4, 6], "backward": [2, 18], "compat": [2, 4, 6, 7, 8, 13, 14, 17, 18, 22], "necessari": [2, 19], "upgrad": [2, 4, 9], "someth": [2, 6, 13, 15, 17], "goe": [2, 6], "upgrade_column_info_4_3_0": [2, 17], "pma__column_info": 2, "pre": [2, 17, 19], "column_com": 2, "alter": [2, 6, 7, 10], "pma__column_com": 2, "varchar": 2, "255": 2, "NOT": [2, 6, 8], "grant": [2, 6, 12, 17], "histori": [2, 4, 17], "item": [2, 6, 7, 17], "queryhistorymax": [2, 17], "On": [2, 6, 16], "everi": [2, 6, 7, 15, 17, 20], "cut": 2, "maximum": [2, 6, 17], "browser": [2, 7, 8, 9, 15, 16, 17, 19], "pma__histori": 2, "recent": [2, 6, 7, 8, 17], "jump": [2, 6], "across": [2, 7], "directli": [2, 7, 8, 10, 17], "numrecentt": 2, "navigationtreedefaulttabt": 2, "disappear": [2, 6], "after": [2, 4, 8], "logout": [2, 16, 17], "persist": [2, 16], "pma__rec": 2, "favorit": [2, 17, 18], "star": [2, 6], "icon": [2, 4, 6, 12, 17, 18, 19, 22], "numfavoritet": [2, 6], "pma__favorit": 2, "table_uipref": 2, "thing": [2, 6, 15], "sort": [2, 6, 8], "remembersort": 2, "visibl": [2, 17], "pma__table_uipref": 2, "associ": 2, "group": [2, 6, 7, 15, 21], "usergroup": [2, 12], "suggest": [2, 4, 6, 14, 15, 17], "attach": [2, 7], "assign": [2, 6, 15, 21], "pma__us": 2, "pma__usergroup": 2, "navigationhid": 2, "tree": [2, 4], "pma__navigationhid": 2, "central_column": 2, "per": [2, 6, 7], "similar": [2, 6, 8, 13, 15], "pma__central_column": 2, "designer_set": 2, "choic": [2, 8, 17], "regard": [2, 10, 19], "angular": 2, "snap": 2, "grid": 2, "toggl": [2, 10], "small": [2, 6], "big": [2, 8], "pin": 2, "pma__designer_set": 2, "savedsearch": 2, "search": [2, 4, 9, 10], "pma__savedsearch": 2, "export_templ": 2, "templat": [2, 4, 17, 19], "pma__export_templ": 2, "track": [2, 4, 10, 17], "mechan": [2, 4, 6, 17], "manipul": [2, 7, 8, 10, 17, 19], "statement": [2, 6, 10, 17], "creation": [2, 4], "snapshot": 2, "index": [2, 4, 7, 9, 10, 17], "Of": [2, 6], "cours": [2, 6], "filter": [2, 6], "rang": [2, 7], "separ": [2, 4, 6, 7, 8, 17], "temporari": [2, 6, 17], "pma__track": 2, "tracking_version_auto_cr": 2, "tracking_default_stat": 2, "truncat": 2, "tracking_add_drop_view": 2, "IF": 2, "tracking_add_drop_t": 2, "tracking_add_drop_databas": 2, "userconfig": 2, "themselv": 2, "local": [2, 4, 8, 16, 17], "unavail": 2, "until": [2, 4, 6, 16, 17, 19], "maxtableuipref": 2, "100": [2, 17], "invalid": [2, 6, 17], "keep": [2, 6, 17], "newest": 2, "older": [2, 9, 10], "sessiontimezon": [2, 17], "zone": 2, "explain": [2, 6, 13, 17], "allowroot": [2, 17], "shortcut": [2, 6, 9, 17], "allowdeni": [2, 6, 17], "unintend": 2, "left": [2, 4, 6, 8], "anonym": [2, 6], "explicit": 2, "perform": [2, 6, 10, 12, 14, 15, 17], "fashion": 2, "restrict": [2, 17], "trustedproxi": 2, "behind": [2, 6], "ipmask": 2, "few": [2, 6, 17], "server_address": 2, "webserv": [2, 6, 8, 17], "localneta": 2, "localnetb": 2, "localnetc": 2, "24": 2, "equival": [2, 19], "xxx": [2, 6, 19], "exact": [2, 6], "yyi": 2, "zzz": 2, "nn": 2, "cidr": 2, "classless": 2, "inter": [2, 7], "rout": [2, 6, 17], "xx": 2, "partial": 2, "xxxx": 2, "yyyi": 2, "zzzz": 2, "bob": [2, 6], "mari": 2, "50": 2, "through": [2, 7, 10, 15, 16, 17, 19, 20], "addition": [2, 3, 17], "disablei": 2, "information_schema": 2, "retriev": [2, 17], "speed": [2, 6, 8, 17], "present": [2, 6, 8, 12, 15, 17], "boost": 2, "signonscript": [2, 17], "obtain": [2, 15], "credenti": [2, 17], "approach": [2, 20], "provid": [2, 6, 7, 10, 12, 17, 19, 20, 22], "get_login_credenti": [2, 17], "return": [2, 17, 19], "accept": [2, 6, 7], "perfect": [2, 17], "declar": [2, 6, 8, 17, 19], "strict_typ": [2, 17], "phpc": [2, 17], "squiz": [2, 17], "globalfunct": [2, 17], "param": [2, 17], "pass": [2, 6, 10, 12, 17, 18, 19], "signonsess": [2, 17], "intern": [2, 4, 7, 8, 13, 17, 19], "signoncookieparam": [2, 17], "doesn": [2, 17, 19], "session_set_cookie_param": [2, 17], "lifetim": 2, "httponli": 2, "mention": [2, 6, 17], "session_get_cookie_param": 2, "signonurl": [2, 17], "redirect": [2, 17], "absolut": [2, 17], "logouturl": 2, "affect": [2, 6, 19], "hide_connection_error": 2, "9": [2, 14, 17], "mariadb": [2, 4, 6, 10, 14, 17], "target": [2, 6], "reveal": 2, "disableshortcutkei": 2, "serverdefault": 2, "autoconnect": 2, "given": [2, 6, 7, 8, 12, 15, 17], "versioncheck": 2, "latest": [2, 6, 7, 14, 17], "adjust": 2, "vendor": [2, 3, 6, 9], "proxyurl": 2, "outsid": [2, 19], "info": [2, 3, 4, 6, 18, 21], "submit": [2, 6, 17], "portnumb": 2, "proxyus": 2, "By": [2, 6, 12, 17], "No": [2, 17], "proxypass": [2, 6], "maxdblist": 2, "maxtablelist": 2, "250": 2, "except": [2, 6, 17, 19], "showhint": 2, "hint": [2, 6, 7], "hover": [2, 13], "maxcharactersindisplayedsql": 2, "1000": 2, "avoid": [2, 6, 8, 17], "ton": 2, "hexadecim": [2, 6, 17], "repres": [2, 6, 7], "blob": [2, 10], "length": [2, 8], "exce": 2, "persistentconnect": 2, "forcessl": 2, "forc": [2, 17], "balanc": 2, "mysqlsslwarningsafehost": [2, 17], "safe": [2, 17], "privat": [2, 6, 17, 20], "exectimelimit": [2, 6, 17], "300": [2, 8], "zero": [2, 8, 16], "impos": 2, "sessionsavepath": [2, 17], "session_save_path": [2, 17], "folder": [2, 8, 17], "publicli": 2, "risk": [2, 6], "leak": 2, "memorylimit": [2, 17], "byte": [2, 7, 8, 17], "alloc": 2, "memori": [2, 8, 17], "ini": [2, 6, 10, 14], "memory_limit": [2, 6, 17], "too": [2, 5, 6, 8], "low": [2, 6], "16m": 2, "ensur": [2, 6, 8, 17], "omit": 2, "suffix": [2, 17], "skiplockedt": 2, "lock": [2, 6], "23": [2, 17], "30": [2, 17], "showsql": 2, "retainquerybox": 2, "kept": [2, 19], "submiss": 2, "codemirroren": 2, "editor": [2, 4, 17, 19], "codemirror": 2, "highlight": 2, "past": 2, "clipboard": [2, 6], "linux": [2, 9, 22], "linten": 2, "parser": [2, 4, 8], "defaultforeignkeycheck": 2, "foreign_key_check": 2, "allowuserdropdatabas": 2, "measur": [2, 7], "circumv": [2, 6], "prohibit": 2, "revok": 2, "own": [2, 10, 15, 17, 18, 19], "mydatabas": 2, "reject": 2, "quit": [2, 4, 6, 18, 20], "practic": 2, "isp": [2, 7, 9, 17], "strict": 2, "natur": 2, "complic": 2, "accident": 2, "rather": [2, 6, 8, 12, 17], "realli": [2, 6, 17, 20], "re": [2, 5, 6, 8, 17], "lose": 2, "usedbsearch": 2, "ignoremultisubmiterror": 2, "continu": [2, 17], "abort": 2, "enable_drag_drop_import": 2, "drag": [2, 6, 8, 13], "urlqueryencrypt": 2, "encrypt": [2, 15, 17], "urlqueryencryptionsecretkei": 2, "decrypt": [2, 6], "32": [2, 17], "long": [2, 6, 7, 17], "random": [2, 4, 15, 17], "maxrowplotlimit": 2, "zoom": [2, 4], "blowfish_secret": [2, 6, 17], "sodium": [2, 7], "prompt": [2, 17], "binari": [2, 6, 19], "usual": [2, 6, 7, 17, 19], "printabl": [2, 6], "convert": [2, 6, 19], "represent": [2, 4, 6, 17], "sodium_bin2hex": 2, "sodium_hex2bin": [2, 6, 17], "f16ce59f45714194371b48fe362072dc3b019da7861558cd4ad29e4d6fb13851": [2, 6, 17], "jofw435365isca": 2, "q": 2, "cdugr": 2, "lsfuaz": 2, "ow": 2, "shorter": [2, 6], "last": [2, 6, 19], "durat": 2, "histor": 2, "reason": [2, 6, 14], "blowfish": [2, 4, 7], "algorithm": [2, 7, 17], "origin": [2, 6, 9, 14, 17, 18, 19, 22], "fly": 2, "bit": [2, 17], "weaker": 2, "imposs": [2, 17], "recal": 2, "sodium_crypto_secretbox": 2, "sodium_crypto_secretbox_open": 2, "respect": [2, 3, 6], "cookiesamesit": 2, "samesit": 2, "attribut": [2, 8], "respons": [2, 6, 7], "lax": 2, "rfc6265": 2, "bi": 2, "logincookierecal": 2, "logincookievalid": 2, "1440": 2, "lost": 2, "idea": [2, 4, 6, 15, 17, 18, 19], "least": [2, 6, 17], "logincookiestor": 2, "environ": [2, 6, 15], "logincookiedeleteal": 2, "easi": [2, 6, 8, 19], "out": [2, 5, 7], "allowarbitraryserv": [2, 17], "arbitrari": [2, 6, 17, 18], "carefulli": 2, "firewal": [2, 6, 17], "arbitraryserverregexp": 2, "enclos": [2, 6], "delimit": 2, "symbol": [2, 18], "yetdiffer": 2, "bypass": [2, 6], "captchamethod": 2, "invis": 2, "captcha": [2, 15, 17], "robot": [2, 17], "captchaapi": 2, "api": [2, 6, 7], "j": [2, 3, 4, 6, 17, 22], "servic": [2, 6, 7, 17], "captchacsp": 2, "gstatic": 2, "polici": [2, 7, 9], "snippet": 2, "captcharequestparam": 2, "captcharesponseparam": 2, "captchaloginpublickei": [2, 17], "public": [2, 3, 6, 17, 22], "admin": 2, "captchaloginprivatekei": [2, 17], "captchasiteverifyurl": 2, "siteverifi": 2, "action": [2, 12, 15, 17], "showdatabasesnavigationastre": 2, "selector": [2, 19], "firstlevelnavigationitem": 2, "level": [2, 6, 7, 8, 17], "maxnavigationitem": 2, "navigationtreeenablegroup": 2, "common": [2, 6, 7, 17, 20], "navigationtreedbsepar": 2, "navigationtreetablesepar": [2, 6], "__": 2, "nest": [2, 4], "first__second__third": 2, "hierarchi": 2, "third": [2, 6, 9, 11, 17, 18], "begin": [2, 6, 8, 17], "navigationtreetablelevel": 2, "sublevel": 2, "split": [2, 6, 8, 19], "zeroconf": [2, 17], "alreadi": [2, 17], "properli": [2, 6, 13, 17], "navigationlinkwithmainpanel": 2, "navigationdisplaylogo": 2, "logo": [2, 4], "top": [2, 6, 8, 16, 17], "navigationlogolink": 2, "made": [2, 4, 6, 7, 17], "rel": [2, 17], "extern": [2, 4, 6, 9, 15, 17], "scheme": [2, 6], "navigationlogolinkwindow": 2, "cspallow": 2, "navigationtreedisplayitemfilterminimum": 2, "minimum": [2, 6], "routin": [2, 4], "event": [2, 4, 7, 10], "high": [2, 6, 8], "9999": 2, "navigationtreedisplaydbfilterminimum": 2, "navigationdisplayserv": 2, "displayserverslist": 2, "navigationtreedefaulttabtable2": 2, "navigationtreeenableexpans": 2, "navigationtreeshowt": 2, "navigationtreeshowview": 2, "navigationtreeshowfunct": 2, "navigationtreeshowprocedur": 2, "procedur": [2, 7, 10], "navigationtreeshowev": 2, "navigationtreeautoexpandsingledb": 2, "navigationwidth": 2, "240": 2, "width": 2, "collaps": 2, "showstat": 2, "statist": [2, 4], "berkelei": 2, "showserverinfo": 2, "showphpinfo": 2, "right": [2, 8, 12, 17, 19], "phpinfo": [2, 6], "disable_funct": 2, "easier": [2, 7, 18], "remot": [2, 7, 17], "showchgpassword": 2, "hard": [2, 17], "showcreatedb": 2, "form": [2, 4, 7, 8, 17], "showgitrevis": 2, "git": [2, 9, 20], "revis": [2, 6, 7], "mysqlminvers": 2, "chosen": [2, 6], "plesk": 2, "eas": [2, 19], "showdbstructurecharset": 2, "charset": [2, 4, 6, 17, 19], "showdbstructurecom": 2, "showdbstructurecr": 2, "showdbstructurelastupd": 2, "showdbstructurelastcheck": 2, "hidestructureact": 2, "hidden": 2, "showcolumncom": 2, "tablenavigationlinksmod": 2, "actionlinksmod": 2, "rowactiontyp": 2, "segment": 2, "showal": 2, "less": [2, 6, 8, 17], "maxrow": 2, "25": 2, "claus": 2, "smart": [2, 4], "ascend": 2, "asc": [2, 17], "descend": 2, "desc": 2, "datetim": 2, "timestamp": 2, "displaybinaryashex": 2, "hex": 2, "tick": 2, "gridedit": 2, "doubl": [2, 6], "trigger": [2, 4, 7, 8, 10], "relationaldisplai": 2, "k": [2, 10, 17], "initi": [2, 6, 17], "d": [2, 4, 10, 17], "savecellsatonc": 2, "cell": [2, 6, 7], "protectbinari": 2, "protect": [2, 15, 17], "noblob": 2, "disallow": [2, 17], "opposit": 2, "showfunctionfield": 2, "showfieldtypesindataeditview": 2, "insertrow": 2, "bottom": [2, 6, 12, 13], "foreignkeymaxlimit": [2, 6], "fewer": 2, "style": [2, 4, 6, 17, 18], "foreignkeydropdownord": 2, "zipdump": 2, "gzipdump": 2, "bzipdump": 2, "zip": [2, 4, 6, 7, 8, 10, 14, 17], "gzip": [2, 4, 7, 10], "bzip2": [2, 6, 7], "compressonfli": [2, 6], "smaller": [2, 4, 6], "larger": [2, 6], "fit": [2, 3, 4, 8], "program": [2, 3, 6, 7, 8, 15], "seen": 2, "convers": [2, 4], "assum": [2, 6, 17], "utf": [2, 6, 7, 17], "file_template_t": 2, "27": 2, "file_template_databas": 2, "file_template_serv": 2, "remove_definer_from_definit": 2, "ep": 2, "dia": 2, "svg": [2, 22], "tabsmod": 2, "propertiesnumcolumn": 2, "util": [2, 6, 7, 15, 17], "defaulttabserv": 2, "welcom": [2, 17], "defaulttabdatabas": 2, "defaulttabt": 2, "pdfpages": 2, "a3": 2, "a4": 2, "a5": 2, "legal": 2, "paper": 2, "size": [2, 8], "pdfdefaultpages": 2, "defaultlang": 2, "lc_messag": 2, "mo": 2, "defaultconnectioncol": 2, "utf8mb4_general_ci": 2, "collat": 2, "lang": [2, 17], "filterlanguag": 2, "czech": [2, 4], "english": [2, 4, 8, 17], "c": [2, 3, 4, 6, 10, 17, 19], "recodingengin": 2, "iconv": 2, "recod": 2, "libiconv": 2, "recode_str": 2, "mb": 2, "mbstring": [2, 7, 14], "encod": [2, 4, 6, 17], "activ": [2, 6], "come": [2, 3, 17, 18], "iconvextraparam": 2, "translit": 2, "transliter": 2, "availablecharset": 2, "frequent": [2, 7, 9], "obgzip": [2, 6], "buffer": [2, 6, 19], "increas": [2, 6, 8], "transfer": [2, 6, 7, 17], "problem": [2, 7, 8, 17], "ie6": 2, "patch": [2, 4], "known": [2, 7, 9, 22], "caus": [2, 15, 17], "corrupt": [2, 6], "fill": [2, 6, 15, 17], "http_x_forwarded_for": 2, "gd2avail": 2, "gd": [2, 7], "ye": 2, "checkconfigurationpermiss": 2, "world": [2, 7], "writabl": [2, 22], "ntf": 2, "filesystem": [2, 6], "mount": 2, "seem": [2, 8, 17], "fact": [2, 6, 7, 17], "sysadmin": [2, 6], "linklengthlimit": 2, "ii": [2, 7, 14, 17], "imag": [2, 7, 10, 12, 14, 17, 19], "disablemultitablemainten": 2, "optim": [2, 4, 6], "slow": 2, "kind": [2, 6, 7, 15], "modifi": [2, 3], "although": [2, 4, 6], "overwritten": 2, "navigationtreepointeren": 2, "background": 2, "browsepointeren": 2, "browsemarkeren": 2, "limitchar": 2, "rowactionlink": 2, "side": [2, 7, 17], "nowher": 2, "rowactionlinkswithoutuniqu": 2, "uniqu": [2, 6, 7, 13], "being": [2, 8, 15, 17, 19], "tableprimarykeyord": 2, "primari": [2, 4, 7, 13, 17], "showbrowsecom": 2, "showpropertycom": 2, "css": [2, 4, 18], "dash": [2, 6], "firstdayofcalendar": 2, "dai": 2, "week": 2, "calendar": 2, "seven": 2, "sundai": 2, "saturdai": 2, "charedit": 2, "newlin": 2, "textarea": 2, "minsizeforinputfield": 2, "maxsizeforinputfield": 2, "60": [2, 6], "textareacol": 2, "textarearow": 2, "chartextareacol": 2, "chartextarearow": 2, "emphas": 2, "longtextdoubletextarea": 2, "longtext": 2, "textareaautoselect": 2, "enableautocompletefortablesandcolumn": 2, "autocomplet": [2, 17], "sqlqueri": 2, "showasphp": 2, "wrap": 2, "refresh": 2, "owner": [2, 17], "subject": [2, 6], "open_basedir": 2, "interpret": [2, 6], "publish": [2, 3, 15, 17], "htaccess": [2, 6, 7, 17], "guess": 2, "download": [2, 3, 6, 10, 15, 16, 17, 18, 22], "uploaddir": [2, 6, 8, 17], "ftp": [2, 6, 17], "bz2": 2, "gz": [2, 8, 17], "chapter": [2, 6, 7, 17], "timeout": [2, 8], "savedir": [2, 8, 17], "tmp": 2, "cach": [2, 6, 17], "esri": 2, "shapefil": [2, 8], "around": [2, 17], "11": 2, "who": [2, 4, 12, 17, 19, 22], "simpli": [2, 8, 12, 17], "chown": [2, 6], "chmod": [2, 6], "700": 2, "achiev": [2, 6, 12, 17], "acl": [2, 7, 17], "setfacl": 2, "rwx": [2, 6], "neither": [2, 6], "777": 2, "read": [2, 6, 8, 10, 17], "repeatcel": 2, "repeat": [2, 6], "editinwindow": 2, "pop": 2, "back": [2, 7, 8, 12, 13], "querywindowwidth": 2, "550": 2, "querywindowheight": 2, "310": 2, "queryhistorydb": [2, 17], "querywindowdeftab": 2, "append": [2, 19], "focu": 2, "suppress": 2, "overwrit": 2, "Then": [2, 6, 17], "compos": [2, 3, 6, 9, 20, 22], "uncheck": [2, 6], "though": [2, 7, 8, 15, 17], "browsemim": 2, "maxexactcount": [2, 6], "50000": 2, "innodb": [2, 7, 10, 13], "larg": [2, 8], "count": [2, 6, 17], "approxim": [2, 6], "500000": 2, "maxexactcountview": 2, "impact": [2, 6, 17], "naturalord": 2, "t1": 2, "t2": 2, "t10": 2, "implement": [2, 7, 17, 19, 20, 21], "initialslidersst": 2, "slider": 2, "state": 2, "class": [2, 6, 17, 19], "userprefsdevelopertab": 2, "bar": [2, 14], "four": 2, "titlet": 2, "http_host": [2, 6], "vserver": [2, 6], "titledatabas": 2, "titleserv": 2, "titledefault": 2, "thememanag": [2, 18], "themedefault": [2, 18], "pmahomm": [2, 6, 18, 22], "subdirectori": [2, 18], "themeperserv": 2, "fontsiz": 2, "82": 2, "thu": [2, 6, 8, 17], "font": 2, "defaultqueryt": 2, "defaultquerydatabas": 2, "didn": 2, "standard": [2, 4, 6, 7, 8, 14, 20], "defaultfunct": 2, "func_char": 2, "func_dat": 2, "func_numb": 2, "func_spati": 2, "geomfromtext": 2, "func_uuid": 2, "uuid": 2, "first_timestamp": 2, "meta": [2, 17, 19], "st_geomfromtext": 2, "utc_timestamp": 2, "defaulttransform": [2, 19], "substr": 2, "bool2text": 2, "f": [2, 6, 10], "preappend": 2, "dateformat": 2, "inlin": [2, 4, 6, 14], "textimagelink": 2, "textlink": 2, "mostli": [2, 8], "meant": 2, "starthistori": 2, "alwaysexpand": 2, "currentqueri": 2, "enterexecut": 2, "darkthem": 2, "switch": [2, 7, 17], "dark": 2, "height": 2, "92": 2, "huge": 2, "product": [2, 6, 8, 17, 20], "debug": [2, 4], "dbg": [2, 20], "sqllog": 2, "demo": [2, 8, 17], "simple2fa": [2, 20], "factor": [2, 6, 9, 17, 21], "typic": [2, 6, 7, 9, 20], "core": [2, 4, 7], "sampl": [2, 6, 8, 17], "ones": [2, 4, 6, 22], "faq": [2, 7, 9], "IN": 2, "FOR": [2, 3], "pma": [2, 17], "pmapass": [2, 17], "pma__bookmark": 2, "pma__rel": [2, 6], "pma__table_coord": 2, "pma__pdf_pag": 2, "pma__userconfig": 2, "record": [2, 7, 8, 12, 13], "uncom": [2, 17], "desir": [2, 6, 7, 17], "de": [2, 3, 4, 6], "occur": [2, 6, 17], "yet": [2, 8], "demonstr": [2, 20], "_server": [2, 17], "remote_addr": 2, "yourpassword": 2, "isn": 2, "reliabl": [2, 6, 8, 17], "no1": 2, "no2": 2, "addr": 2, "startup": [2, 6], "peer": 2, "project": [2, 4, 7, 9, 17], "************": 2, "pmatest": 2, "did": [2, 6], "expect": [2, 6, 8], "traffic": 2, "tell": [2, 6], "72048": 2, "me": [2, 4], "cluster": 2, "region": 2, "amazonaw": 2, "2019": [2, 6], "bundl": 2, "s3": 2, "eu": [2, 4], "west": [2, 4], "parent": [2, 6, 17], "guid": [2, 9, 11, 17], "h": [2, 6, 10], "dashboard": 2, "0xxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxx": 2, "websit": [2, 4, 5, 6, 7, 15, 18], "challeng": 2, "cloudflar": 2, "v0": 2, "static": 2, "cloudflareinsight": 2, "cf": 2, "0xxxxxxxxxxxxxxxxxxxxxx": 2, "0x4aaaaaaaa_xx_xxxxxxxxxxxxxxxxxxxx": 2, "xxxxxxxxxxxxxxxx": 2, "xxxxxxxxxxxx": 2, "xxxxxxxxx": 2, "xxxxxxxxxxxxxx": 2, "1998": [3, 4], "2000": [3, 4, 10], "tobia": [3, 4], "ratschil": [3, 4], "tobias_at_ratschil": [3, 4], "2018": [3, 4], "marc": [3, 4, 6, 11, 17], "delisl": [3, 4, 11, 17], "marc_at_infomarc": [3, 4], "olivi": [3, 4], "m\u00fcller": [3, 4], "om_at_omni": [3, 4], "ch": [3, 4], "robin": [3, 4], "johnson": [3, 4], "robbat2_at_us": [3, 4], "sourceforg": [3, 4], "alexand": [3, 4], "turek": [3, 4], "me_at_derrabu": [3, 4], "michal": [3, 4, 17], "\u010diha\u0159": [3, 4, 17], "michal_at_cihar": [3, 4], "garvin": [3, 4], "hick": [3, 4], "me_at_supergarv": [3, 4], "michael": [3, 4], "keck": [3, 4], "mkkeck_at_us": [3, 4], "sebastian": [3, 4], "mendel": [3, 4], "cybot_tm_at_us": [3, 4], "credit": [3, 9, 15], "free": [3, 7, 10, 19, 22], "softwar": [3, 6, 7, 8, 10, 17, 20, 22], "redistribut": [3, 22], "term": 3, "gnu": [3, 7, 17], "foundat": 3, "hope": 3, "warranti": 3, "impli": 3, "merchant": 3, "particular": [3, 6, 7, 12, 17], "receiv": [3, 7, 19], "along": [3, 17], "got": [3, 8], "mit": [3, 17], "gpl": [3, 22], "repositori": [3, 7, 17, 20], "txt": [3, 6, 17], "kit": [3, 17, 22], "directori": [3, 6, 7, 8, 9, 17, 18, 19, 22], "creator": 4, "maintain": [4, 6, 17], "summer": 4, "languag": [4, 7, 9, 10, 17], "decemb": 4, "variou": [4, 6, 8, 9, 10, 17, 22], "fix": [4, 13, 15], "analys": 4, "2015": [4, 17], "march": 4, "sync": 4, "ed": 4, "cv": 4, "bugfix": 4, "dynam": [4, 6, 7], "lo\u00efc": 4, "chapeaux": 4, "lolo_at_phpheaven": 4, "rewrot": 4, "dhtml": 4, "dom": 4, "stuff": 4, "pear": [4, 7, 17], "xhtml1": 4, "css2": 4, "compliant": 4, "authent": [4, 9, 14, 15, 21], "ip": [4, 6, 7, 17], "Not": [4, 6], "pretti": 4, "printer": 4, "armel": 4, "fauveau": 4, "fauveau_at_globali": 4, "geert": 4, "lund": 4, "glund_at_silversoft": 4, "dk": 4, "moder": 4, "former": 4, "forum": [4, 6], "phpwizard": [4, 6], "korakot": 4, "chaovavanich": 4, "korakot_at_inam": 4, "pete": 4, "kelli": 4, "webmaster_at_trafficg": 4, "steve": 4, "alberti": 4, "alberty_at_neptunlab": 4, "php4": [4, 6], "mysql": [4, 7, 8, 9, 10, 11, 12, 13, 14, 15, 17, 22], "benjamin": 4, "gandon": 4, "gandon_at_isia": 4, "cma": 4, "fr": 4, "abstract": [4, 19], "pma_dbi": 4, "xml": [4, 7, 10, 14, 17], "export": [4, 9, 10, 13, 17, 21], "german": 4, "mike": 4, "beck": 4, "beck_at_web": 4, "qbe": [4, 10], "enhanc": 4, "christoph": 4, "gesch\u00e9": 4, "phpmysqlformgen": 4, "built": [4, 6], "vertic": [4, 7], "transform": [4, 9, 10, 11, 17, 21], "alia": 4, "hierarch": 4, "pdf": [4, 7, 9, 10, 11, 13, 17], "scratchboard": 4, "wysiwyg": 4, "yukihiro": 4, "kawada": 4, "kawada_at_den": 4, "fujifilm": 4, "co": 4, "jp": [4, 6], "japanes": 4, "kanji": 4, "piotr": 4, "roszatycki": 4, "d3xter_at_us": 4, "dan": 4, "wilson": 4, "cooki": [4, 7, 9, 14, 18], "axel": 4, "sander": 4, "n8falke_at_us": 4, "maxim": [4, 8], "delorm": 4, "maxime_at_fre": 4, "thank": [4, 6], "plathei": 4, "fpdf": 4, "steven": 4, "witten": 4, "ufpdf": 4, "nicola": 4, "asuni": 4, "tcpdf": [4, 6, 7], "olof": 4, "edlund": 4, "edlund_at_upright": 4, "se": 4, "server": [4, 7, 8, 9, 10, 11, 12, 13, 15, 20, 22], "ivan": 4, "r": [4, 6], "lanin": 4, "ivanlanin_at_us": 4, "june": 4, "2004": 4, "cochran": 4, "mike_at_graftonhal": 4, "nz": 4, "hord": 4, "withdrawn": 4, "marcel": 4, "tschopp": 4, "ne0x_at_us": 4, "tecnick": 4, "redesign": 4, "sailboat": 4, "mathia": 4, "landh\u00e4u\u00df": 4, "confer": 4, "kirillov": 4, "design": [4, 7, 9, 17, 21], "raj": 4, "kissu": 4, "rajandran": 4, "googl": [4, 6, 8, 17, 20], "2008": 4, "blobstream": 4, "przybylski": 4, "2010": 4, "2011": 4, "setup": [4, 9, 13, 22], "drizzl": 4, "derek": 4, "schaefer": 4, "2009": 4, "import": [4, 7, 9, 10, 14, 17, 21], "rutkowski": 4, "zahra": 4, "naeem": 4, "synchron": [4, 9], "tom\u00e1\u0161": 4, "srnka": 4, "replic": [4, 6], "muhammad": 4, "adnan": 4, "lori": 4, "lee": 4, "enum": [4, 19], "simplifi": [4, 12], "ninad": 4, "pundalik": 4, "ajaxifi": 4, "martyna": 4, "mickevi\u010diu": 4, "chart": [4, 9, 21], "barri": 4, "lesli": 4, "pbm": 4, "ankit": 4, "gupta": 4, "builder": 4, "madhura": 4, "jayaratn": 4, "opengi": 4, "ammar": 4, "yasir": 4, "ari": 4, "feryanto": 4, "brows": [4, 9, 10, 13, 19, 21], "thilanka": 4, "kaushalya": 4, "ajaxif": 4, "tyron": 4, "madlen": 4, "zarubin": 4, "sta": 4, "autom": [4, 17], "rouslan": 4, "placella": 4, "2012": 4, "italian": 4, "navig": [4, 9, 12], "dieter": 4, "adriaenssen": 4, "dutch": 4, "alex": 4, "marin": 4, "plugin": [4, 6, 8, 19], "thilina": 4, "buddika": 4, "abeyrathna": 4, "refactor": 4, "atul": 4, "pratap": 4, "singh": 4, "chanaka": 4, "indrajith": 4, "yasitha": 4, "pandithawatta": 4, "jim": 4, "wigginton": 4, "phpseclib": 4, "bin": [4, 6], "zu": 4, "2013": 4, "supun": 4, "nakandala": 4, "moham": 4, "ashraf": 4, "ajax": 4, "adam": 4, "kang": 4, "ayush": 4, "chaudhari": 4, "kasun": 4, "chathuranga": 4, "hugu": 4, "peccatt": 4, "save": [4, 6, 8, 9, 16, 17, 18], "smita": 4, "kumari": 4, "2014": 4, "structur": [4, 7, 8, 9, 10, 13, 17, 18, 21], "ashutosh": 4, "dhundhara": 4, "dhananjai": 4, "nakrani": 4, "edward": 4, "cheng": 4, "consol": [4, 9, 10], "kankanamg": 4, "bimal": 4, "yashodha": 4, "chirayu": 4, "chirip": 4, "handler": 4, "ungureanu": 4, "nisarg": 4, "jhaveri": 4, "ui": 4, "deven": 4, "bansod": 4, "print": [4, 21], "2017": 4, "selenium": 4, "manish": 4, "bisht": 4, "mobil": [4, 20], "raghuram": 4, "vadap": 4, "maur\u00edcio": 4, "meneghini": 4, "fauth": 4, "major": [4, 6, 18], "modern": 4, "twig": 4, "william": 4, "desport": 4, "phpstan": 4, "emanuel": 4, "bronshtein": 4, "comprehens": [4, 8], "assess": 4, "lakshya": 4, "arora": 4, "modal": [4, 6], "expir": 4, "constraint": 4, "saksham": 4, "theme": [4, 9, 17, 21, 22], "leonardo": 4, "strozzi": 4, "piyush": 4, "vijai": 4, "webpack": 4, "babel": 4, "yarn": [4, 17], "eslint": 4, "jsdoc": 4, "peopl": [4, 5, 6, 10, 22], "contribut": [4, 5, 6], "minor": [4, 6], "bora": 4, "alioglu": 4, "ricardo": 4, "sven": 4, "erik": [4, 6], "andersen": 4, "alessandro": 4, "astarita": 4, "p\u00e9ter": 4, "bakondi": 4, "borg": 4, "botelho": 4, "bussier": 4, "neil": 4, "darlow": 4, "mat": 4, "engstrom": 4, "ian": 4, "davidson": 4, "laurent": 4, "dhima": 4, "kristof": 4, "hamann": 4, "thoma": [4, 7], "kl\u00e4ger": 4, "lubo": 4, "klokner": 4, "martin": 4, "marconcini": 4, "girish": 4, "nair": 4, "david": 4, "nordenberg": 4, "andrea": 4, "paulei": 4, "bernard": 4, "piller": 4, "haa": 4, "sakamoto": 4, "yuval": 4, "sarna": 4, "securer": 4, "au": 4, "alexi": 4, "soulard": 4, "alvar": 4, "soom": 4, "siu": 4, "sun": 4, "peter": 4, "svec": 4, "taceloski": 4, "rachim": 4, "tamsjadi": 4, "kosit": 4, "uro": 4, "lu\u00ed": 4, "v": [4, 17], "martijn": 4, "w": 4, "van": 4, "der": 4, "algi": 4, "vainauska": 4, "daniel": 4, "villanueva": 4, "vinai": 4, "ignacio": 4, "vazquez": 4, "abram": 4, "chee": 4, "jakub": 4, "wilk": 4, "winningham": 4, "viliu": 4, "zigmanta": 4, "manuzhai": 4, "albanian": 4, "arben": 4, "\u00e7okaj": 4, "acokaj_at_shkod": 4, "arab": 4, "ahm": 4, "saleh": 4, "abd": 4, "el": 4, "raouf": 4, "isma": 4, "ismael_at_gmail": 4, "saad": 4, "egbrave_at_hotmail": 4, "hassan": 4, "mokhtari": 4, "persiste1_at_gmail": 4, "armenian": 4, "andrei": 4, "aleksany": 4, "aaleksanyants_at_yahoo": 4, "azerbaijani": 4, "mirc\u0259lal": 4, "01youknowme_at_gmail": 4, "huseyn": 4, "huseyn_esgerov_at_mail": 4, "ru": 4, "sevdimali": 4, "i\u0307sa": 4, "sevdimaliisayev_at_mail": 4, "jafar": 4, "sharifov_at_programm": 4, "belarusian": 4, "viktar": 4, "palstsiuk": 4, "vipals_at_gmail": 4, "bulgarian": 4, "boyan": 4, "kehayov": 4, "bkehayov_at_gmail": 4, "valter": 4, "georgiev": 4, "blagynchy_at_gmail": 4, "valentin": 4, "mladenov": 4, "hudsonvsm_at_gmail": 4, "p": [4, 17], "plamen_mbx_at_yahoo": 4, "krasimir": 4, "vip_at_krasio": 4, "valia": 4, "catalan": 4, "josep": 4, "constanti": 4, "jconstanti_at_yahoo": 4, "xavier": 4, "navarro": 4, "xvnavarro_at_gmail": 4, "chines": 4, "china": 4, "vincent": 4, "lau": 4, "3092849_at_qq": 4, "zheng": 4, "clanboy_at_163": 4, "disorderman": 4, "disorderman_at_qq": 4, "rex": 4, "duguying2008_at_gmail": 4, "fundawang_at_gmail": 4, "popcorn": 4, "memoword_at_163": 4, "yizhou": 4, "qiang": 4, "qyz": 4, "yswy_at_hotmail": 4, "zz": 4, "tczzjin_at_gmail": 4, "terri": 4, "weng": 4, "wengshiyu_at_gmail": 4, "whh": 4, "whhlcj_at_126": 4, "taiwan": 4, "albert": 4, "song": 4, "albb0920_at_gmail": 4, "chien": 4, "wei": 4, "lin": 4, "cwlin0416_at_gmail": 4, "dave": 4, "hello": 4, "xs910203_at_gmail": 4, "colognian": 4, "purodha": 4, "publi_at_web": 4, "ale\u0161": 4, "hakl": 4, "ales_at_hakl": 4, "dalibor": 4, "straka": 4, "straka3_at_gmail": 4, "vidner": 4, "martin_at_vidn": 4, "ondra": 4, "\u0161ime\u010dek": 4, "ondrasek": 4, "simecek_at_gmail": 4, "jan": [4, 17], "palid": 4, "palider_at_seznam": 4, "cz": 4, "petr": 4, "kate\u0159i\u0148\u00e1k": 4, "katerinak_at_gmail": 4, "danish": [4, 8], "aputsia\u0138": 4, "niel": 4, "janussen": 4, "aj_at_isit": 4, "gl": 4, "denni": 4, "jakobsen": 4, "jakobsen_at_gmail": 4, "jona": 4, "den": 4, "smarte_at_gmail": 4, "clau": 4, "svalekja": 4, "smtp": 4, "server_at_gmail": 4, "voogt": 4, "voogt_at_hccnet": 4, "nl": 4, "dingo": 4, "thirteen": 4, "dingo13_at_gmail": 4, "vliet": 4, "info_at_robinvandervliet": 4, "ruleant_at_us": 4, "niko": 4, "strijbol": 4, "niko_at_gmail": 4, "unit": 4, "kingdom": 4, "dri": 4, "verschuer": 4, "verschuere_at_outlook": 4, "francisco": 4, "rocha": 4, "o": [4, 7, 9, 11, 17, 20], "rocha_at_zoho": 4, "marek": 4, "toma\u0161t\u00edk": 4, "tomastik": 4, "m_at_gmail": 4, "esperanto": 4, "eliovir": 4, "eliovir_at_gmail": 4, "estonian": 4, "kristjan": 4, "r\u00e4t": 4, "kristjanrats_at_gmail": 4, "finnish": 4, "juha": 4, "reme": 4, "jremes_at_outlook": 4, "lari": 4, "oesch": 4, "lari_at_oesch": 4, "french": 4, "frisian": 4, "galician": 4, "xos\u00e9": 4, "calvo": 4, "xosecalvo_at_gmail": 4, "julian": [4, 7], "ladisch": 4, "github": [4, 6, 17], "t3if_at_ladisch": 4, "zassenhau": 4, "zassenhaus_at_jgerman": 4, "lass": 4, "goerick": 4, "lasse_at_mydom": 4, "matthia": 4, "bluthardt": 4, "matthias_at_bluthardt": 4, "koch": 4, "koch_at_enough": 4, "ann": 4, "phpmyadmin_at_zweisteinsoft": 4, "pma_at_sebastianmendel": 4, "phillip": 4, "rohmberg": 4, "rohmberger_at_hotmail": 4, "hauk": 4, "henningsen": 4, "sqrt_at_entless": 4, "greek": 4, "\u03c0\u03b1\u03bd\u03b1\u03b3\u03b9\u03ce\u03c4\u03b7\u03c2": 4, "\u03c0\u03b1\u03c0\u03ac\u03b6\u03bf\u03b3\u03bb\u03bf\u03c5": 4, "papaz_p_at_yahoo": 4, "hebrew": 4, "mosh": 4, "harush": 4, "mmh15_at_windowsl": 4, "yaron": 4, "shahrabani": 4, "sh": [4, 19], "yaron_at_gmail": 4, "eyal": 4, "visok": 4, "visokereyal_at_gmail": 4, "hindi": 4, "atulpratapsingh05_at_gmail": 4, "yogeshwar": 4, "charanyogeshwar_at_gmail": 4, "devenbansod": 4, "bits_at_gmail": 4, "kushagra": 4, "pandei": 4, "kushagra4296_at_gmail": 4, "nisargjhaveri_at_gmail": 4, "roohan": 4, "kazi": 4, "roohan_cena_at_yahoo": 4, "yugal": 4, "pantola": 4, "yug": 4, "scorpio_at_gmail": 4, "hungarian": 4, "ako": 4, "ero": 4, "erosakos02_at_gmail": 4, "d\u00e1niel": 4, "t\u00f3th": 4, "leedermeister_at_gmail": 4, "sz\u00e1sz": 4, "attila": 4, "undernetangel_at_gmail": 4, "bal\u00e1z": 4, "\u00far": 4, "urbalazs_at_gmail": 4, "indonesian": 4, "deki": 4, "arifianto": 4, "deky40_at_gmail": 4, "andika": 4, "triwidada": 4, "andika_at_gmail": 4, "dadan": 4, "setia": 4, "da2n_s_at_yahoo": 4, "setia_at_gmail": 4, "yohan": 4, "edwin": 4, "edwin_at_yohanesedwin": 4, "fadhiil": 4, "rachman": 4, "fadhiilrachman_at_gmail": 4, "benni": 4, "tarzq28_at_gmail": 4, "tommi": 4, "surbakti": 4, "tommy_at_surbakti": 4, "zufar": 4, "fathi": 4, "suhardi": 4, "bogor_at_gmail": 4, "interlingua": 4, "giovanni": 4, "sora": 4, "sora_at_tiscali": 4, "francesco": 4, "saverio": 4, "giacobazzi": 4, "giacobazzi_at_ferrania": 4, "marco": 4, "pozzato": 4, "ironpotts_at_gmail": 4, "stefano": 4, "martinelli": 4, "ste": 4, "martinelli_at_gmail": 4, "k725": 4, "alexalex": 4, "kobayashi_at_gmail": 4, "hiroshi": 4, "chiyokawa": 4, "chiyokawa_at_gmail": 4, "masahiko": 4, "hisakawa": 4, "orzkun_at_ageag": 4, "worldwideski": 4, "worldwideskier_at_yahoo": 4, "kannada": 4, "shameem": 4, "mulla": 4, "sam_at_gmail": 4, "korean": 4, "bumsoo": 4, "kim": 4, "bskim45_at_gmail": 4, "kyeong": 4, "su": 4, "shin": 4, "cdac1234_at_gmail": 4, "dongyoung": 4, "dckyoung_at_gmail": 4, "myung": 4, "han": 4, "yu": 4, "greatymh_at_gmail": 4, "jongdeok": 4, "human": [4, 8], "zion_at_gmail": 4, "yong": 4, "kim_at_nhn": 4, "\uc774\uacbd\uc900": 4, "kyungjun2_at_gmail": 4, "seongki": 4, "skshin_at_gmail": 4, "yoon": 4, "bum": 4, "jong": 4, "virusyoon_at_gmail": 4, "koo": 4, "youngmin": 4, "youngminz": 4, "kr_at_gmail": 4, "kurdish": 4, "sorani": 4, "alan": 4, "hilal": 4, "hilal94_at_gmail": 4, "aso": 4, "naderi": 4, "naderi_at_gmail": 4, "esy_vb_at_yahoo": 4, "zrng": 4, "abdulla": 4, "zhyarabdulla94_at_gmail": 4, "latvian": 4, "tv": 4, "dnighttv_at_gmail": 4, "edgar": 4, "neimani": 4, "edgarsneims5092_at_inbox": 4, "lv": 4, "ukko": 4, "perkontevs_at_gmail": 4, "limburgish": 4, "lithuanian": 4, "vytauta": 4, "motuza": 4, "motuzas_at_gmail": 4, "malai": 4, "amir": 4, "hamzah": 4, "overlord666_at_gmail": 4, "diprofinfin": 4, "anonynuin": 4, "999_at_yahoo": 4, "nepali": 4, "nabin": 4, "ghimir": 4, "nnabinn_at_hotmail": 4, "norwegian": 4, "bokm\u00e5l": 4, "b\u00f8rge": 4, "holm": 4, "wennberg": 4, "borge947_at_gmail": 4, "tor": 4, "stokkan": 4, "danorse_at_gmail": 4, "espen": 4, "fr\u00f8yshov": 4, "efroys_at_gmail": 4, "kurt": 4, "eilertsen": 4, "kurt_at_kh": 4, "christoff": 4, "haugom": 4, "ph3n1x": 4, "nobody_at_gmail": 4, "sebastian_at_sgundersen": 4, "toma": 4, "tomas_at_tomasruud": 4, "persian": 4, "ashkan": 4, "shirian": 4, "shirian_at_gmail": 4, "hm": 4, "goodlinuxuser_at_chmail": 4, "ir": 4, "polish": 4, "andrzej": 4, "andrzej_at_kynu": 4, "pl": 4, "przemo": 4, "info_at_opsbielani": 4, "waw": 4, "krystian": 4, "biesaga": 4, "krystian4842_at_gmail": 4, "maciej": 4, "gryniuk": 4, "maciejka45_at_gmail": 4, "micha\u0142": 4, "vonflyne": 4, "vonflynee_at_gmail": 4, "portugues": 4, "alexandr": 4, "badalo": 4, "badalo_at_sapo": 4, "pt": 4, "jo\u00e3o": 4, "rodrigu": 4, "geral_at_jonil": 4, "pedro": 4, "ribeiro": 4, "m42": 4, "ribeiro_at_gmail": 4, "sandro": 4, "amar": 4, "sandro123iv_at_gmail": 4, "brazil": 4, "rohled": 4, "alexrohleder96_at_outlook": 4, "bruno": 4, "mendax": 4, "brunomendax_at_gmail": 4, "danilo": 4, "guia": 4, "eng_at_globomail": 4, "dougla": 4, "rafael": 4, "morai": 4, "kollar": 4, "kollar_at_pg": 4, "df": 4, "gov": 4, "br": [4, 17], "eccker": 4, "douglaseccker_at_hotmail": 4, "jr": 4, "edjacobjunior_at_gmail": 4, "guilherm": 4, "souza": 4, "silva": 4, "szsilva_at_gmail": 4, "seibt": 4, "gui_at_webseibt": 4, "helder": 4, "santana": 4, "b": [4, 6, 8, 19], "santana_at_gmail": 4, "junior": 4, "zancan": 4, "jrzancan_at_hotmail": 4, "lui": 4, "eduardo": 4, "braschi_at_outlook": 4, "algeri": 4, "malgeri_at_gmail": 4, "renato": 4, "lima": 4, "j\u00fanio": 4, "renatomdd_at_yahoo": 4, "thiago": 4, "casotti": 4, "casotti_at_uol": 4, "victor": 4, "laureano": 4, "laureano_at_gmail": 4, "vin\u00edciu": 4, "ara\u00fajo": 4, "vinipitta_at_gmail": 4, "washington": 4, "cav": 4, "washingtonbruno_at_msn": 4, "yan": 4, "gabriel": 4, "yansilvagabriel_at_gmail": 4, "punjabi": 4, "romanian": 4, "amihaita_at_yahoo": 4, "costel": 4, "cocerhan": 4, "costa1988sv_at_gmail": 4, "ion": 4, "adrian": 4, "ionut": 4, "john_at_panevo": 4, "ro": 4, "raul": 4, "molnar": 4, "raul_at_wservic": 4, "noreply_at_webl": 4, "stefan": 4, "murariu": 4, "murariu_at_yahoo": 4, "russian": 4, "ddrmoscow_at_gmail": 4, "\u0445\u043e\u043c\u0443\u0442\u043e\u0432": 4, "\u0438\u0432\u0430\u043d": 4, "\u0441\u0435\u0440\u0433\u0435\u0435\u0432\u0438\u0447": 4, "khomutov": 4, "ivan_at_mail": 4, "alexei": 4, "rubinov": 4, "orion1979_at_yandex": 4, "\u043e\u043b\u0435\u0433": 4, "\u043a\u0430\u0440\u043f\u043e\u0432": 4, "salvadoporjc_at_gmail": 4, "egorov": 4, "artyom": 4, "unlucky_at_inbox": 4, "serbian": 4, "kid": 4, "kidsmart33_at_gmail": 4, "sinhala": 4, "cj_at_gmail": 4, "slovak": 4, "lacina": 4, "martin_at_whistl": 4, "sk": 4, "patrik": 4, "kollmann": 4, "parkourpotex_at_gmail": 4, "jozef": 4, "pistej": 4, "pistej2_at_gmail": 4, "slovenian": 4, "domen": 4, "mitenem_at_outlook": 4, "spanish": 4, "garc\u00eda": 4, "sevillano": 4, "floss": 4, "dev_at_gmail": 4, "franco": 4, "fulanodet": 4, "github1_at_openaliasbox": 4, "ruiz": 4, "luisan00_at_hotmail": 4, "macof": 4, "languagetool_at_gmail": 4, "mat\u00eda": 4, "bellon": 4, "matiasbellon": 4, "weblate_at_gmail": 4, "rodrigo": 4, "ra4_at_openmailbox": 4, "famma": 4, "noticia": 4, "medio": 4, "revistafammatvmus": 4, "oficial_at_gmail": 4, "ronni": 4, "simon": 4, "ronniesimonf_at_gmail": 4, "swedish": 4, "ander": 4, "jonsson": 4, "jonsson_at_norsjovallen": 4, "tamil": 4, "\u0b95\u0ba3": 4, "\u0bb7": 4, "\u0b95": 4, "\u0bae": 4, "\u0bb0": 4, "ganeshtheone_at_gmail": 4, "achchuthan": 4, "yogarajah": 4, "achch1990_at_gmail": 4, "rifthi": 4, "rifthy456_at_gmail": 4, "thai": 4, "nontawat39_at_gmail": 4, "somthanat": 4, "somthanat_at_gmail": 4, "turkish": 4, "burak": 4, "yavuz": 4, "hitowerdigit_at_hotmail": 4, "ukrainian": 4, "\u0441\u0435\u0440\u0433\u0456\u0439": 4, "\u043f\u0435\u0434\u044c\u043a\u043e": 4, "nitrotoll_at_gmail": 4, "igor": 4, "vmta_at_yahoo": 4, "vitalii": 4, "perekupka": 4, "vperekupka_at_gmail": 4, "vietnames": 4, "bao": 4, "phan": 4, "baophan94_at_icloud": 4, "xuan": 4, "hung": 4, "mr": 4, "hungdx_at_gmail": 4, "trinh": 4, "minh": 4, "trinhminhbao_at_gmail": 4, "flemish": 4, "azzabi": 4, "ahmedtek1993_at_gmail": 4, "omar": 4, "essam": 4, "omar_2412_at_l": 4, "joan": 4, "montan\u00e9": 4, "joan_at_montan": 4, "cat": 4, "\u7f57\u6500\u767b": 4, "6375lpd_at_gmail": 4, "itxiaopang": 4, "djh1017555_at_126": 4, "tunnel213": 4, "tunnel213_at_aliyun": 4, "koolen": 4, "nast3zz_at_gmail": 4, "rai": 4, "borggrev": 4, "ray_at_datahui": 4, "tom": 4, "hofman": 4, "hofman_at_gmail": 4, "c\u00e9dric": 4, "corazza": 4, "cedric": 4, "corazza_at_wanadoo": 4, "\u00e9tienn": 4, "gilli": 4, "etienn": 4, "gilli_at_gmail": 4, "donavan_martin": 4, "mart": 4, "donavan_at_hotmail": 4, "gnauk89_at_googlemail": 4, "jh": 4, "janhenrikm_at_yahoo": 4, "niemand": 4, "jedermann": 4, "predatorix_at_web": 4, "tw": 4, "tablettws_at_gmail": 4, "eshin": 4, "kunishima": 4, "ek_at_luna": 4, "miko": 4, "im": 4, "jur": 4, "ki": 4, "atvejis_at_gmail": 4, "dovyda": 4, "dovi": 4, "buz_at_gmail": 4, "moretti": 4, "alemoretti2010_at_hotmail": 4, "michel": 4, "ekio_at_gmail": 4, "azevedo": 4, "mrdaniloazevedo_at_gmail": 4, "kuppelwies": 4, "webadmin": 4, "hi": [4, 17], "web": [4, 6, 7, 8, 9, 15, 17, 22], "php3": 4, "concept": [4, 10], "ve": [4, 12, 15], "borrow": 4, "him": 4, "told": 4, "he": 4, "wasn": 4, "go": [4, 6, 8, 10, 12, 17, 19], "develop": [4, 7, 8, 9, 15, 17, 18], "great": [4, 5], "amalesh": 4, "kempf": 4, "ak": 4, "lsml_at_liv": 4, "tbl_creat": 4, "ldi_": 4, "plu": [4, 6], "legenhausen": 4, "jan_at_nrw": 4, "signific": 4, "delislma_at_collegesherbrook": 4, "qc": 4, "independ": [4, 6, 7], "outsourc": 4, "bravo": 4, "abravo_at_hq": 4, "admir": 4, "tbl_select": 4, "chri": 4, "jackson": 4, "chrisj_at_ctel": 4, "tbl_chang": 4, "walton": 4, "walton_at_nordicdm": 4, "contributor": 4, "ash": 4, "ga244_at_is8": 4, "nyu": 4, "edu": [4, 17], "kraai": 4, "jordi": 4, "bruguera": 4, "miquel": 4, "obrador": 4, "kleemann": 4, "leiding": 4, "kiko": 4, "albiol": 4, "chao": 4, "pavel": 4, "piankov": 4, "sascha": 4, "kettler": 4, "joe": [4, 6], "pruett": 4, "kronsbein": 4, "janni": 4, "hermann": 4, "wiegger": 4, "everyon": 4, "email": [4, 6, 15], "feedback": 4, "invit": 5, "offici": [6, 11, 17, 19], "homepag": [6, 19], "depth": 6, "coverag": 6, "zlib": [6, 7, 10], "output_compress": 6, "possibli": [6, 16, 17], "forgot": 6, "12061": 6, "isapi": [6, 7, 17], "stabl": 6, "filippo": 6, "simoncini": 6, "workaround": [6, 17], "better": 6, "doctyp": [6, 17], "gzencod": 6, "dir": [6, 17], "upload_tmp_dir": 6, "mkdir": 6, "ownership": 6, "restart": [6, 17], "rob": 6, "httpd": [6, 7], "conf": 6, "setenvif": [6, 17], "agent": [6, 7], "msie": 6, "nokeepal": 6, "ssl": [6, 7, 9], "unclean": 6, "shutdown": 6, "clear": [6, 17], "recov": 6, "loss": 6, "reset": 6, "contact": [6, 15], "align": 6, "engin": [6, 7, 8], "advantag": 6, "max_execution_tim": [6, 17], "upload_max_files": [6, 17], "post_max_s": [6, 17], "unwil": 6, "scp": 6, "bigdump": 6, "parti": [6, 9, 11, 17, 18], "shell": 6, "becom": 6, "unsupport": 6, "solut": [6, 17, 20], "compil": 6, "Its": 6, "probabl": [6, 17, 22], "php_mysqli": 6, "dll": 6, "solv": 6, "packag": [6, 9, 17], "extension_dir": 6, "loadabl": 6, "modul": [6, 7], "resid": 6, "apache2": 6, "ext": 6, "phpinidir": 6, "loadmodul": 6, "php7_modul": 6, "php7": 6, "php7apache2_4": 6, "ifmodul": 6, "addtyp": 6, "addhandl": 6, "rare": 6, "anyth": [6, 17], "max_link": 6, "higher": [6, 17], "file_upload": 6, "lower_case_table_nam": 6, "ON": [6, 17], "mysqld": 6, "insensit": 6, "myisam": [6, 7, 10], "tablenam": 6, "lettercas": 6, "jose": 6, "fando": 6, "mod_gzip_item_includ": 6, "iusr_machin": 6, "upper": 6, "56": 6, "gamma": 6, "conflict": 6, "setoutputfilt": 6, "setinputfilt": 6, "saw": 6, "addon": 6, "turck": 6, "mmcach": 6, "hhvm": 6, "rfc2616_header": 6, "dialog": [6, 8, 17, 19], "basic": [6, 9, 17, 19, 22], "digest": 6, "ok": 6, "401": 6, "rfc": [6, 7, 10], "2616": 6, "global": [6, 10, 12, 16, 17, 19], "symlinksifownermatch": 6, "allowoverrid": 6, "fileinfo": 6, "mod_rewrit": 6, "rewriteengin": 6, "rewritebas": 6, "path_to_phpmyadmin": 6, "rewriterul": 6, "za": 6, "z0": 6, "9_": 6, "z_": 6, "rewrit": [6, 17], "remote_us": 6, "l": [6, 22], "explan": [6, 17], "clue": 6, "consist": [6, 18], "architectur": 6, "pack": [6, 22], "unpack": [6, 17], "openssl": [6, 14, 17], "textual": [6, 8], "malfunct": 6, "fulli": [6, 17], "neg": 6, "usabl": 6, "diagnos": 6, "scenario": [6, 17], "max_var": 6, "2048": 6, "max_array_index_length": 6, "256": 6, "max_totalname_length": 6, "8192": 6, "max_value_length": 6, "1024": [6, 17], "bailout_on_error": 6, "slowdown": 6, "multiselect": 6, "modif": 6, "executor": 6, "max_travers": 6, "mitig": [6, 15], "inclus": 6, "recaptcha": [6, 14], "disable_emodifi": 6, "knowledg": 6, "ssloption": 6, "stdenvvar": 6, "mod": 6, "mod_ssl": 6, "mirror": [6, 17], "foo": 6, "backend": [6, 17], "proxypassrevers": 6, "proxypassreversecookiedomain": 6, "proxypassreversecookiepath": 6, "tild": 6, "7e": 6, "7euser": 6, "mod_proxi": [6, 7], "mysql_upgrad": 6, "verb": 6, "rewritecond": 6, "request_method": 6, "nc": 6, "OR": 6, "ban": 6, "scanner": 6, "kick": 6, "kiddi": 6, "http_user_ag": 6, "java": [6, 8], "curl": [6, 14], "wget": 6, "libwww": 6, "perl": [6, 7], "python": 6, "nikto": 6, "wkito": 6, "pikto": 6, "scan": [6, 20], "acunetix": 6, "winhttp": 6, "httrack": 6, "clshttp": 6, "archiv": [6, 7, 8, 17], "loader": 6, "harvest": 6, "extract": [6, 17], "grab": [6, 17], "miner": 6, "crawler": 6, "panel": [6, 9], "bot": 6, "ultim": [6, 17], "useless": 6, "intitl": 6, "intext": 6, "wiki": [6, 7, 8, 17], "adsbot": 6, "ia_archiv": 6, "scooter": 6, "jeev": 6, "baiduspid": 6, "exabot": 6, "fast": [6, 7], "enterpris": 6, "webcrawl": 6, "neomo": 6, "gigabot": 6, "mediapartn": 6, "desktop": 6, "feedfetch": 6, "googlebot": 6, "heis": 6, "IT": 6, "markt": 6, "heritrix": 6, "ibm": [6, 9], "iccrawl": 6, "ichiro": 6, "mj12bot": 6, "metagerbot": 6, "msnbot": 6, "newsblog": 6, "media": [6, 7, 19], "ng": 6, "lucen": 6, "nutchcv": 6, "omniexplorer_bot": 6, "onlin": 6, "psbot0": 6, "seekbot": 6, "sensi": 6, "seo": 6, "seoma": 6, "seosearch": 6, "snappi": 6, "urltrend": 6, "tkl": 6, "tokyo": 6, "synoobot": 6, "crawleradmin": 6, "telekom": 6, "turnitinbot": 6, "voyag": 6, "w3": 6, "sitesearch": 6, "w3c": [6, 7], "checklink": 6, "w3c_valid": 6, "wisenutbot": 6, "yacybot": 6, "yahoo": 6, "mmcrawler": 6, "slurp": 6, "yahooseek": 6, "confus": 6, "degrad": 6, "gracefulli": 6, "unus": 6, "subfold": [6, 17], "language_stat": 6, "advanc": [6, 15, 17], "src": [6, 17], "dist": 6, "rm": 6, "rv": 6, "tecnickcom": 6, "autoload": 6, "interact": [6, 7], "williamd": 6, "kb": 6, "lt": [6, 20], "u2f": [6, 21], "pragmarx": [6, 20], "2fa": [6, 21], "bacon": [6, 20], "qr": [6, 20], "qrcode": [6, 20], "encount": 6, "mysqli_real_connect": 6, "hy000": 6, "2054": 6, "began": 6, "abil": 6, "experienc": 6, "encourag": 6, "WITH": [6, 17], "mysql_native_password": [6, 17], "BY": [6, 17, 22], "14220": 6, "stackoverflow": 6, "49948350": 6, "76243": 6, "tag": 6, "mysql_connect": 6, "haven": 6, "2002": 6, "respond": 6, "mysqladmin": 6, "bunch": 6, "readabl": [6, 8], "redhat": 6, "lib": [6, 22], "default_socket": 6, "alpha": 6, "php_self": [6, 17], "request_uri": [6, 17], "resolv": 6, "wasser": 6, "mayb": 6, "broken": 6, "zend": 6, "31134": 6, "harden": [6, 15], "varfilt": 6, "max_request_vari": 6, "200": 6, "klau": 6, "dorning": 6, "arg_separ": 6, "save_path": 6, "dure": [6, 8, 17], "uploadprogress": 6, "moreov": 6, "json": [6, 14, 17, 18], "upload_progress": 6, "problemat": 6, "One": [6, 17], "suitabl": [6, 8], "cryptograph": [6, 7, 17], "random_byt": 6, "echo": [6, 17], "bin2hex": 6, "php_eol": 6, "overcom": 6, "21704": 6, "54": 6, "horizont": [6, 7], "think": [6, 15], "tablesepar": 6, "nor": 6, "parse_url": 6, "surround": 6, "tbl_row_delet": 6, "element": [6, 7, 19], "http_post_var": 6, "tutori": [6, 19, 21], "quot": 6, "7383": 6, "seriou": 6, "sum": 6, "49": 6, "differenti": 6, "break": 6, "apart": 6, "largest": 6, "sake": 6, "At": [6, 17], "recreat": 6, "12232": 6, "kindli": 6, "sponsor": 6, "netcologn": 6, "gmbh": 6, "manag": [6, 7, 8, 9, 10, 11, 17, 21], "suffici": 6, "telnet": 6, "660": 6, "phpmy": 6, "whose": [6, 17], "include_path": 6, "somewher": [6, 15], "interfer": 6, "suit": [6, 7, 17], "fragment": 6, "172": [6, 17], "sakila": 6, "pma_usernam": 6, "pma_password": [6, 17], "pmasa": [6, 15], "reproduc": 6, "win98": 6, "98se": 6, "winnt4": 6, "win2k": 6, "ll": [6, 14, 17], "downgrad": [6, 17], "propos": [6, 10], "tbl_dump": 6, "buggi": [6, 17], "till": [6, 17], "ey": 6, "26882": 6, "bugzilla": 6, "maco": [6, 7], "chimera": 6, "situat": [6, 7, 17], "ie": 6, "center": [6, 17], "443": 6, "tabbrows": 6, "troubleshoot": 6, "awai": 6, "wrongli": 6, "antiviru": 6, "emit": 6, "overrid": [6, 17], "kasperski": 6, "wordfenc": 6, "examin": 6, "care": [6, 19], "argument": 6, "had": [6, 17], "transmit": [6, 7, 20], "station": 6, "hand": [6, 17], "town": 6, "countri": 6, "rel_countri": 6, "country_cod": 6, "descript": [6, 8, 17, 18, 19], "INTO": 6, "canada": 6, "rel_person": 6, "tinyint": 6, "auto_incr": [6, 8], "person_nam": 6, "town_cod": 6, "paul": 6, "rel_town": 6, "sherbrook": 6, "montr\u00e9al": 6, "step": [6, 17], "tickbox": 6, "silent": 6, "backslash": 6, "john_db": 6, "john1db": 6, "john2db": 6, "averag": 6, "backquot": 6, "bigger": [6, 8], "delai": 6, "best": [6, 8, 17], "entir": [6, 14, 17], "bad": [6, 17], "wors": 6, "condit": 6, "arrow": 6, "overview": [6, 19], "heart": 6, "minim": [6, 17], "tex": [6, 7, 8], "documentclass": [6, 8], "articl": [6, 8, 11], "usepackag": [6, 8], "longtabl": [6, 8], "shorten": 6, "179": 6, "distinct": 6, "97": 6, "migrat": 6, "everywher": 6, "strftime": 6, "context": [6, 17], "visualis": 6, "actual": [6, 17, 19], "shp": 6, "geometri": [6, 8], "dbf": 6, "dbase": 6, "chose": [6, 17], "cd": 6, "pink": 6, "unset": [6, 17], "scatter": 6, "plot": 6, "sub": [6, 15], "criteria": 6, "taken": 6, "max": 6, "wheel": 6, "pan": 6, "interest": [6, 11], "dialogu": 6, "difficult": 6, "reorder": 6, "tooltip": [6, 13], "veri": [6, 8, 17], "much": [6, 19], "grai": 6, "front": 6, "wait": 6, "yellow": 6, "exclud": 6, "final": [6, 13, 17, 19], "upto": 6, "wizard": [6, 17], "vetoffic": 6, "petnam": 6, "64": 6, "petbre": 6, "pettyp": 6, "petdob": 6, "ownerlastnam": 6, "ownerfirstnam": 6, "ownerphone1": 6, "ownerphone2": 6, "owneremail": 6, "suppos": 6, "ownerphon": 6, "consecut": 6, "accomplish": [6, 8, 17], "involv": [6, 17], "exclus": 6, "rearrang": 6, "future_id": 6, "object": [6, 8, 17, 19], "id_new": 6, "similarli": 6, "columns_priv": 6, "tables_priv": 6, "procs_priv": 6, "flush": 6, "colon": 6, "substitut": 6, "1031": 6, "table_nam": 6, "row_format": 6, "innodb_strict_mod": 6, "successfulli": 6, "skill": 6, "easiest": [6, 17, 20], "userid": 6, "userstatu": 6, "logformat": 6, "n": [6, 17], "pma_combin": 6, "display_error": [6, 17], "spreadsheet": [6, 10, 14], "believ": 6, "anywai": 6, "discuss": 6, "feel": 6, "sanit": 6, "wikipedia": [7, 8, 15, 17], "encyclopedia": 7, "symmetr": 7, "1993": 7, "bruce": 7, "schneier": 7, "blowfish_": 7, "locat": [7, 17], "wide": [7, 8, 16, 20], "web_brows": 7, "seward": 7, "cgi": [7, 17], "gatewai": 7, "technologi": 7, "common_gateway_interfac": 7, "changelog": 7, "comput": [7, 17], "client_": 7, "particularli": [7, 17], "column_": 7, "packet": [7, 8], "http_cooki": 7, "csv": [7, 10, 17], "comma": [7, 8, 17], "separated_valu": 7, "organ": 7, "collect": [7, 22], "extend": [7, 8, 17, 19], "software_extens": 7, "question": [7, 9], "commonli": [7, 8, 19], "answer": 7, "divid": 7, "field_": 7, "computer_sci": 7, "foreign_kei": 7, "graphic": [7, 10, 13, 17], "boutel": 7, "gd_graphics_librari": 7, "gd2": [7, 14], "short": [7, 8], "connect": [7, 9, 10, 12, 15, 16], "node": [7, 17], "host_": 7, "devic": [7, 14], "hypertext": 7, "convei": 7, "hypertext_transfer_protocol": 7, "iec": [7, 10], "electrotechn": 7, "commiss": 7, "microsoft": [7, 10], "internet_information_servic": 7, "database_index": 7, "commun": [7, 10], "internetwork": 7, "internet_protocol": 7, "ip_address": 7, "deal": 7, "anticip": 7, "predecessor": 7, "ipv4": [7, 19], "internet_server_application_programming_interfac": 7, "busi": 7, "internet_service_provid": 7, "iso": [7, 10], "international_organization_for_standard": 7, "jpeg": [7, 14, 19], "lossi": 7, "photograph": 7, "jpg": 7, "latex": [7, 10], "typeset": [7, 8], "mac": 7, "appl": 7, "macintosh": 7, "manufactur": [7, 20], "market": 7, "ship": [7, 17, 18, 22], "consum": 7, "profession": 7, "notabl": 7, "book": [7, 8, 21], "formerli": 7, "media_typ": 7, "multipurpos": 7, "mail": 7, "modular": 7, "apache_http_serv": 7, "mod_proxy_fcgi": [7, 17], "fastcgi": 7, "multithread": 7, "dbm": 7, "opendocu": [7, 10], "offic": [7, 8], "portabl": 7, "adob": 7, "dimension": 7, "resolut": 7, "perl_compatible_regular_express": 7, "preprocessor": 7, "reflect": 7, "mainli": 7, "broader": 7, "port_": 7, "computer_network": 7, "radio": 7, "memoranda": 7, "encompass": 7, "research": 7, "innov": 7, "methodologi": 7, "request_for_com": 7, "1952": [7, 10], "tupl": 7, "implicitli": 7, "row_": 7, "server_": 7, "disk": [7, 8], "tab": [7, 8, 9, 12, 13, 16, 17], "database_engin": 7, "process": [7, 12, 17], "unix_domain_socket": 7, "supersed": 7, "tl": [7, 17], "transport_layer_secur": 7, "subroutin": 7, "stored_procedur": 7, "table_": 7, "tar": [7, 17], "tape": 7, "tar_": 7, "transmiss": [7, 8], "internet_protocol_suit": 7, "database_trigg": 7, "uniform": 7, "resourc": 7, "sequenc": 7, "conform": 7, "serv": [7, 17], "web_serv": 7, "markup": [7, 8], "capabl": 7, "popular": [7, 8], "zip_": 7, "file_format": 7, "jean": 7, "loup": 7, "gailli": 7, "adler": 7, "content_security_polici": 7, "mozilla": 7, "csp": 7, "consult": 8, "upload": [8, 9, 14, 17, 19], "mydump": 8, "popul": 8, "limit": [8, 9, 12, 15, 17], "geospati": 8, "vector": 8, "geograph": 8, "regul": 8, "interoper": 8, "workbook": 8, "sheet": 8, "concern": [8, 15], "quickli": 8, "screen": [8, 17, 18], "newli": [8, 17], "formula": 8, "calcul": 8, "evalu": 8, "restor": 8, "nhibern": 8, "plan": 8, "hibern": 8, "pdo": 8, "preliminari": 8, "preconfigur": 8, "localis": 8, "newer": [8, 14, 17], "openoffic": 8, "notat": 8, "lightweight": 8, "interchang": 8, "pars": [8, 18, 19], "marker": 8, "indic": [8, 17], "car": 8, "car_id": 8, "green": 8, "chrysler": 8, "make_id": 8, "mileag": 8, "113688": 8, "price": 8, "13545": 8, "00": 8, "yearmad": 8, "2007": 8, "emb": 8, "scientif": 8, "mathemat": 8, "typograph": 8, "qualiti": 8, "learn": [8, 11], "render": [8, 15], "lscape": 8, "setlength": 8, "parind": 8, "0pt": 8, "2cm": 8, "nohead": 8, "nofoot": 8, "pdfpagewidth": 8, "210mm": 8, "pdfpageheight": 8, "297mm": 8, "maketitl": 8, "adopt": 8, "libreoffic": 8, "processor": 8, "abiword": 8, "kword": 8, "edit": [8, 9, 10, 13, 17, 18, 21], "multidimension": 8, "undocu": 8, "experi": 8, "max_allowed_packet": 8, "neglig": 8, "significantli": 8, "decreas": 8, "files": 8, "parsabl": 8, "serial": 8, "computation": 8, "power": 8, "introduct": [9, 21], "docker": 9, "cloud": 9, "licens": 9, "copyright": 9, "chronolog": 9, "glossari": 9, "task": 10, "batch": 10, "26300": 10, "administ": 10, "subset": 10, "predefin": 10, "alt": 10, "home": [10, 12], "backspac": 10, "difficulti": 10, "understand": [10, 17], "footnot": 10, "endors": 11, "seri\u00e1l": 11, "phpmyadminovi": 11, "fun": 11, "superus": [12, 17], "textbox": 12, "pencil": 12, "individu": 12, "consid": [12, 17, 20], "gain": 12, "inexperienc": 12, "overwhelm": 12, "relationship": 13, "fall": 13, "enforc": 13, "matter": 13, "categori": 13, "category_id": 13, "link_id": 13, "uri": 13, "hyperlink": 13, "shall": 13, "nginx": 14, "spl": 14, "hash": [14, 17], "ctype": 14, "thumbnail": 14, "ratio": 14, "progress": 14, "libxml": 14, "allow_url_open": 14, "31": 14, "bootstrap": 14, "getbootstrap": 14, "effort": 15, "exploit": 15, "announc": [15, 17], "cve": 15, "surfac": 15, "piec": [15, 22], "malici": 15, "craft": 15, "trick": 15, "nasti": 15, "userdata": 15, "provok": 15, "token": [15, 20], "pose": 15, "regener": 15, "harder": 15, "preform": 15, "controlconnect": 15, "databaseinterfac": 15, "escapestr": 15, "rate": 15, "stateless": 15, "against": [15, 17], "fail2ban": [15, 17], "pgp": [15, 17], "da68ab39218ab947": 15, "fingerprint": [15, 17], "pub": 15, "4096r": 15, "02": 15, "5bad": 15, "38cf": 15, "b980": 15, "50b9": 15, "4bd7": 15, "fb5b": 15, "da68": 15, "ab39": 15, "218a": 15, "b947": 15, "uid": 15, "5e4176fb497a31f7": 15, "keyserv": [15, 17], "keyr": [15, 17], "keybas": [15, 17], "job": 17, "lighttpd": 17, "dbconfig": 17, "readm": 17, "usr": 17, "stock": 17, "webapp": 17, "emerg": 17, "contrib": 17, "branch": 17, "cento": 17, "driven": 17, "extra": 17, "epel": 17, "xampp": 17, "clone": 17, "skip": [17, 22], "invok": 17, "packagist": 17, "tarbal": 17, "deploi": 17, "listen": 17, "your_db_host": 17, "pma_arbitrari": 17, "pma_host": 17, "pma_verbos": 17, "pma_us": 17, "pma_port": 17, "pma_socket": 17, "pma_absolute_uri": 17, "qualifi": 17, "pma_queryhistorydb": 17, "pma_queryhistorymax": 17, "pma_controlhost": 17, "pma_controlus": 17, "pma_controlpass": 17, "pma_controlport": 17, "pma_pmadb": 17, "hide_php_vers": 17, "expose_php": 17, "upload_limit": 17, "fpm": 17, "2048k": 17, "kilobyt": 17, "megabyt": 17, "gigabyt": 17, "1k": 17, "512m": 17, "pma_config_base64": 17, "base64": 17, "decod": 17, "pma_user_config_base64": 17, "pma_uploaddir": 17, "pma_savedir": 17, "apache_port": 17, "8090": 17, "pma_ssl_dir": 17, "pma_ssl": 17, "pma_ssl_verifi": 17, "pma_ssl_ca": 17, "mutual": 17, "pma_ssl_ca_base64": 17, "pma_ssl_cas_base64": 17, "pma_ssl_cert": 17, "pma_ssl_cert_base64": 17, "pma_ssl_certs_base64": 17, "pma_ssl_kei": 17, "pma_ssl_key_base64": 17, "pma_ssl_keys_base64": 17, "tz": 17, "timezon": 17, "utc": 17, "mysql_root_password": 17, "tweak": 17, "csv_column": 17, "mydb1": 17, "mydb2": 17, "dbhost": 17, "8080": 17, "dbhost1": 17, "dbhost2": 17, "dbhost3": 17, "mysql_db_serv": 17, "yml": 17, "container_nam": 17, "expos": 17, "frontend": 17, "bind": 17, "forwardfor": 17, "localnet": 17, "path_dir": 17, "use_backend": 17, "reqirep": 17, "head": 17, "traefik": 17, "defaultentrypoint": 17, "entrypoint": 17, "regex": 17, "myadmin": 17, "passhosthead": 17, "pathprefixstrip": 17, "addprefix": 17, "domainnam": 17, "8000": 17, "36": 17, "appropri": 17, "genuin": 17, "untar": 17, "unzip": 17, "xzvf": 17, "phpmyadmin_x": 17, "evil": 17, "tradition": 17, "okai": 17, "insist": 17, "changem": 17, "insecur": 17, "anybodi": 17, "visit": 17, "readi": 17, "review": 17, "sbin": 17, "juli": 17, "januari": 17, "0xfefc65d181af644a": 17, "436f": 17, "f188": 17, "4b1a": 17, "0c3f": 17, "dcbf": 17, "0d79": 17, "fefc": 17, "65d1": 17, "81af": 17, "644a": 17, "identif": 17, "io": [17, 20], "lem9": 17, "isaac": 17, "bennetch": 17, "0xce752f178259bd92": 17, "3d06": 17, "a59": 17, "ce73": 17, "0eb7": 17, "1b51": 17, "1c17": 17, "ce75": 17, "2f17": 17, "8259": 17, "bd92": 17, "ibennetch": 17, "0x9c27b31342b7511d": 17, "63cb": 17, "1df1": 17, "ef12": 17, "cf2a": 17, "c0ee": 17, "5a32": 17, "9c27": 17, "b313": 17, "42b7": 17, "511d": 17, "nijel": 17, "signatur": 17, "accompani": 17, "gpg": 17, "fri": 17, "29": 17, "59": 17, "37": 17, "am": 17, "est": 17, "rsa": 17, "8259bd92": 17, "complain": 17, "know": [17, 19], "hkp": 17, "recv": 17, "3d06a59ece730eb71b511c17ce752f178259bd92": 17, "gmail": 17, "total": 17, "aka": 17, "certifi": 17, "privaci": 17, "handbook": 17, "cover": 17, "topic": 17, "meet": 17, "exchang": 17, "reli": 17, "transit": 17, "met": 17, "regardless": 17, "unknown": 17, "prior": [17, 19], "infrastructur": 17, "scope": 17, "disturb": 17, "create_t": 17, "pai": 17, "attent": 17, "win32": 17, "lowercas": 17, "upgrade_tables_mysql_4_1_2": 17, "upgrade_tables_4_7_0": 17, "tune": 17, "pma_db": 17, "TO": 17, "caching_sha2_password": 17, "45": 17, "outdat": 17, "implic": 17, "breakag": 17, "set_magic_quotes_runtim": 17, "freshli": 17, "backup": 17, "plai": 17, "nevertheless": 17, "mind": 17, "ae": 17, "shouldn": 17, "ordinari": 17, "real_us": 17, "user_bas": 17, "real_password": 17, "35": 17, "fcgi": 17, "reach": [17, 19], "http_author": 17, "truli": 17, "conveni": 17, "ini_set": 17, "use_cooki": 17, "secure_cooki": 17, "session_nam": 17, "foobar": 17, "session_start": 17, "isset": 17, "_post": 17, "_session": 17, "pma_single_signon_us": 17, "pma_single_signon_password": 17, "pma_single_signon_host": 17, "pma_single_signon_port": 17, "pma_single_signon_cfgupd": 17, "pma_single_signon_hmac_secret": 17, "sha1": 17, "uniqid": 17, "strval": 17, "random_int": 17, "mt_getrandmax": 17, "session_id": 17, "session_write_clos": 17, "ltr": 17, "href": 17, "favicon": 17, "ico": 17, "bodi": 17, "pma_single_signon_error_messag": 17, "spellcheck": 17, "openid": 17, "auth_map": 17, "include_onc": 17, "relyingparti": 17, "exit": 17, "map": 17, "launchpad": 17, "psr1": 17, "sideeffect": 17, "show_pag": 17, "void": 17, "pma_single_signon_messag": 17, "die_error": 17, "div": 17, "relyingparty_result": 17, "htmlspecialchar": 17, "getmessag": 17, "return_to": 17, "server_nam": 17, "server_port": 17, "returnto": 17, "dirnam": 17, "strlen": 17, "_get": 17, "is_str": 17, "elseif": 17, "openid_relyingparti": 17, "catch": 17, "throwabl": 17, "authrequest": 17, "getauthorizeurl": 17, "querystr": 17, "explod": 17, "fetch": 17, "raw": 17, "file_get_cont": 17, "repli": 17, "openid_messag": 17, "format_http": 17, "claimed_id": 17, "wrapper": 17, "sometim": 17, "anyon": 17, "discov": 17, "unlik": 17, "perhap": 17, "incom": 17, "router": 17, "suffic": 17, "beyond": 17, "searchabl": 17, "hst": 17, "auxiliari": 17, "Such": 17, "exposur": 17, "happen": 17, "42": 17, "authtyp": 17, "authnam": 17, "authuserfil": 17, "passwd": 17, "htpasswd": 17, "afraid": 17, "brute": 17, "talk": 17, "caution": 17, "amazon": 17, "rd": 17, "aurora": 17, "11922": 17, "period": 17, "inact": [17, 19], "11898": 17, "your_theme_nam": 18, "img": [18, 22], "screenshot": 18, "png": 18, "prepend": 19, "five": 19, "hopefulli": 19, "grow": 19, "bound": 19, "subtyp": 19, "textfield": 19, "forth": 19, "exactli": 19, "ab": 19, "sens": 19, "convent": 19, "transformationsplugin": 19, "plug": 19, "getmimetyp": 19, "getmimesubtyp": 19, "getnam": 19, "getinfo": 19, "applytransform": 19, "template_abstract": 19, "transformations_generator_plugin": 19, "transformations_generator_main_class": 19, "mysql_fetch_field": 19, "unsign": 19, "zerofil": 19, "not_nul": 19, "opt": 20, "google2fa": 20, "hotp": 20, "totp": 20, "dozen": 20, "phone": 20, "freeotp": 20, "android": [20, 22], "pebbl": 20, "authi": 20, "chrome": 20, "lastpass": 20, "youbico": 20, "hyperfido": 20, "trezor": 20, "wallet": 20, "act": 20, "dongl": 20, "fido": 21, "technic": 21, "advic": 22, "vendor_config": 22, "setup_config_fil": 22, "setup_dir_writ": 22, "framework": 22, "silk": 22, "metro": 22, "b_renam": 22, "icons8": 22, "icomoon": 22, "cc": 22, "cc0": 22}, "objects": {"": [[2, 0, 1, "cfg_ActionLinksMode", "$cfg[&#x27;ActionLinksMode&#x27;]"], [2, 0, 1, "cfg_AllowArbitraryServer", "$cfg[&#x27;AllowArbitraryServer&#x27;]"], [2, 0, 1, "cfg_AllowThirdPartyFraming", "$cfg[&#x27;AllowThirdPartyFraming&#x27;]"], [2, 0, 1, "cfg_AllowUserDropDatabase", "$cfg[&#x27;AllowUserDropDatabase&#x27;]"], [2, 0, 1, "cfg_ArbitraryServerRegexp", "$cfg[&#x27;ArbitraryServerRegexp&#x27;]"], [2, 0, 1, "cfg_AuthLog", "$cfg[&#x27;AuthLog&#x27;]"], [2, 0, 1, "cfg_AuthLogSuccess", "$cfg[&#x27;AuthLogSuccess&#x27;]"], [2, 0, 1, "cfg_AvailableCharsets", "$cfg[&#x27;AvailableCharsets&#x27;]"], [2, 0, 1, "cfg_BZipDump", "$cfg[&#x27;BZipDump&#x27;]"], [2, 0, 1, "cfg_BrowseMIME", "$cfg[&#x27;BrowseMIME&#x27;]"], [2, 0, 1, "cfg_BrowseMarkerEnable", "$cfg[&#x27;BrowseMarkerEnable&#x27;]"], [2, 0, 1, "cfg_BrowsePointerEnable", "$cfg[&#x27;BrowsePointerEnable&#x27;]"], [2, 0, 1, "cfg_CSPAllow", "$cfg[&#x27;CSPAllow&#x27;]"], [2, 0, 1, "cfg_CaptchaApi", "$cfg[&#x27;CaptchaApi&#x27;]"], [2, 0, 1, "cfg_CaptchaCsp", "$cfg[&#x27;CaptchaCsp&#x27;]"], [2, 0, 1, "cfg_CaptchaLoginPrivateKey", "$cfg[&#x27;CaptchaLoginPrivateKey&#x27;]"], [2, 0, 1, "cfg_CaptchaLoginPublicKey", "$cfg[&#x27;CaptchaLoginPublicKey&#x27;]"], [2, 0, 1, "cfg_CaptchaMethod", "$cfg[&#x27;CaptchaMethod&#x27;]"], [2, 0, 1, "cfg_CaptchaRequestParam", "$cfg[&#x27;CaptchaRequestParam&#x27;]"], [2, 0, 1, "cfg_CaptchaResponseParam", "$cfg[&#x27;CaptchaResponseParam&#x27;]"], [2, 0, 1, "cfg_CaptchaSiteVerifyURL", "$cfg[&#x27;CaptchaSiteVerifyURL&#x27;]"], [2, 0, 1, "cfg_CharEditing", "$cfg[&#x27;CharEditing&#x27;]"], [2, 0, 1, "cfg_CharTextareaCols", "$cfg[&#x27;CharTextareaCols&#x27;]"], [2, 0, 1, "cfg_CharTextareaRows", "$cfg[&#x27;CharTextareaRows&#x27;]"], [2, 0, 1, "cfg_CheckConfigurationPermissions", "$cfg[&#x27;CheckConfigurationPermissions&#x27;]"], [2, 0, 1, "cfg_CodemirrorEnable", "$cfg[&#x27;CodemirrorEnable&#x27;]"], [2, 0, 1, "cfg_CompressOnFly", "$cfg[&#x27;CompressOnFly&#x27;]"], [2, 0, 1, "cfg_Confirm", "$cfg[&#x27;Confirm&#x27;]"], [2, 0, 1, "cfg_Console_AlwaysExpand", "$cfg[&#x27;Console&#x27;][&#x27;AlwaysExpand&#x27;]"], [2, 0, 1, "cfg_Console_CurrentQuery", "$cfg[&#x27;Console&#x27;][&#x27;CurrentQuery&#x27;]"], [2, 0, 1, "cfg_Console_DarkTheme", "$cfg[&#x27;Console&#x27;][&#x27;DarkTheme&#x27;]"], [2, 0, 1, "cfg_Console_EnterExecutes", "$cfg[&#x27;Console&#x27;][&#x27;EnterExecutes&#x27;]"], [2, 0, 1, "cfg_Console_Height", "$cfg[&#x27;Console&#x27;][&#x27;Height&#x27;]"], [2, 0, 1, "cfg_Console_Mode", "$cfg[&#x27;Console&#x27;][&#x27;Mode&#x27;]"], [2, 0, 1, "cfg_Console_StartHistory", "$cfg[&#x27;Console&#x27;][&#x27;StartHistory&#x27;]"], [2, 0, 1, "cfg_ConsoleEnterExecutes", "$cfg[&#x27;ConsoleEnterExecutes&#x27;]"], [2, 0, 1, "cfg_CookieSameSite", "$cfg[&#x27;CookieSameSite&#x27;]"], [2, 0, 1, "cfg_DBG", "$cfg[&#x27;DBG&#x27;]"], [2, 0, 1, "cfg_DBG_demo", "$cfg[&#x27;DBG&#x27;][&#x27;demo&#x27;]"], [2, 0, 1, "cfg_DBG_simple2fa", "$cfg[&#x27;DBG&#x27;][&#x27;simple2fa&#x27;]"], [2, 0, 1, "cfg_DBG_sql", "$cfg[&#x27;DBG&#x27;][&#x27;sql&#x27;]"], [2, 0, 1, "cfg_DBG_sqllog", "$cfg[&#x27;DBG&#x27;][&#x27;sqllog&#x27;]"], [2, 0, 1, "cfg_DefaultConnectionCollation", "$cfg[&#x27;DefaultConnectionCollation&#x27;]"], [2, 0, 1, "cfg_DefaultForeignKeyChecks", "$cfg[&#x27;DefaultForeignKeyChecks&#x27;]"], [2, 0, 1, "cfg_DefaultFunctions", "$cfg[&#x27;DefaultFunctions&#x27;]"], [2, 0, 1, "cfg_DefaultLang", "$cfg[&#x27;DefaultLang&#x27;]"], [2, 0, 1, "cfg_DefaultQueryDatabase", "$cfg[&#x27;DefaultQueryDatabase&#x27;]"], [2, 0, 1, "cfg_DefaultQueryTable", "$cfg[&#x27;DefaultQueryTable&#x27;]"], [2, 0, 1, "cfg_DefaultTabDatabase", "$cfg[&#x27;DefaultTabDatabase&#x27;]"], [2, 0, 1, "cfg_DefaultTabServer", "$cfg[&#x27;DefaultTabServer&#x27;]"], [2, 0, 1, "cfg_DefaultTabTable", "$cfg[&#x27;DefaultTabTable&#x27;]"], [2, 0, 1, "cfg_DefaultTransformations", "$cfg[&#x27;DefaultTransformations&#x27;]"], [2, 0, 1, "cfg_DefaultTransformations_Bool2Text", "$cfg[&#x27;DefaultTransformations&#x27;][&#x27;Bool2Text&#x27;]"], [2, 0, 1, "cfg_DefaultTransformations_DateFormat", "$cfg[&#x27;DefaultTransformations&#x27;][&#x27;DateFormat&#x27;]"], [2, 0, 1, "cfg_DefaultTransformations_External", "$cfg[&#x27;DefaultTransformations&#x27;][&#x27;External&#x27;]"], [2, 0, 1, "cfg_DefaultTransformations_Hex", "$cfg[&#x27;DefaultTransformations&#x27;][&#x27;Hex&#x27;]"], [2, 0, 1, "cfg_DefaultTransformations_Inline", "$cfg[&#x27;DefaultTransformations&#x27;][&#x27;Inline&#x27;]"], [2, 0, 1, "cfg_DefaultTransformations_PreApPend", "$cfg[&#x27;DefaultTransformations&#x27;][&#x27;PreApPend&#x27;]"], [2, 0, 1, "cfg_DefaultTransformations_Substring", "$cfg[&#x27;DefaultTransformations&#x27;][&#x27;Substring&#x27;]"], [2, 0, 1, "cfg_DefaultTransformations_TextImageLink", "$cfg[&#x27;DefaultTransformations&#x27;][&#x27;TextImageLink&#x27;]"], [2, 0, 1, "cfg_DefaultTransformations_TextLink", "$cfg[&#x27;DefaultTransformations&#x27;][&#x27;TextLink&#x27;]"], [2, 0, 1, "cfg_DisableMultiTableMaintenance", "$cfg[&#x27;DisableMultiTableMaintenance&#x27;]"], [2, 0, 1, "cfg_DisableShortcutKeys", "$cfg[&#x27;DisableShortcutKeys&#x27;]"], [2, 0, 1, "cfg_DisplayBinaryAsHex", "$cfg[&#x27;DisplayBinaryAsHex&#x27;]"], [2, 0, 1, "cfg_DisplayServersList", "$cfg[&#x27;DisplayServersList&#x27;]"], [2, 0, 1, "cfg_EditInWindow", "$cfg[&#x27;EditInWindow&#x27;]"], [2, 0, 1, "cfg_EnableAutocompleteForTablesAndColumns", "$cfg[&#x27;EnableAutocompleteForTablesAndColumns&#x27;]"], [2, 0, 1, "cfg_ExecTimeLimit", "$cfg[&#x27;ExecTimeLimit&#x27;]"], [2, 0, 1, "cfg_Export", "$cfg[&#x27;Export&#x27;]"], [2, 0, 1, "cfg_Export_charset", "$cfg[&#x27;Export&#x27;][&#x27;charset&#x27;]"], [2, 0, 1, "cfg_Export_compression", "$cfg[&#x27;Export&#x27;][&#x27;compression&#x27;]"], [2, 0, 1, "cfg_Export_file_template_database", "$cfg[&#x27;Export&#x27;][&#x27;file_template_database&#x27;]"], [2, 0, 1, "cfg_Export_file_template_server", "$cfg[&#x27;Export&#x27;][&#x27;file_template_server&#x27;]"], [2, 0, 1, "cfg_Export_file_template_table", "$cfg[&#x27;Export&#x27;][&#x27;file_template_table&#x27;]"], [2, 0, 1, "cfg_Export_format", "$cfg[&#x27;Export&#x27;][&#x27;format&#x27;]"], [2, 0, 1, "cfg_Export_method", "$cfg[&#x27;Export&#x27;][&#x27;method&#x27;]"], [2, 0, 1, "cfg_Export_remove_definer_from_definitions", "$cfg[&#x27;Export&#x27;][&#x27;remove_definer_from_definitions&#x27;]"], [2, 0, 1, "cfg_FilterLanguages", "$cfg[&#x27;FilterLanguages&#x27;]"], [2, 0, 1, "cfg_FirstDayOfCalendar", "$cfg[&#x27;FirstDayOfCalendar&#x27;]"], [2, 0, 1, "cfg_FirstLevelNavigationItems", "$cfg[&#x27;FirstLevelNavigationItems&#x27;]"], [2, 0, 1, "cfg_FontSize", "$cfg[&#x27;FontSize&#x27;]"], [2, 0, 1, "cfg_ForceSSL", "$cfg[&#x27;ForceSSL&#x27;]"], [2, 0, 1, "cfg_ForeignKeyDropdownOrder", "$cfg[&#x27;ForeignKeyDropdownOrder&#x27;]"], [2, 0, 1, "cfg_ForeignKeyMaxLimit", "$cfg[&#x27;ForeignKeyMaxLimit&#x27;]"], [2, 0, 1, "cfg_GD2Available", "$cfg[&#x27;GD2Available&#x27;]"], [2, 0, 1, "cfg_GZipDump", "$cfg[&#x27;GZipDump&#x27;]"], [2, 0, 1, "cfg_GridEditing", "$cfg[&#x27;GridEditing&#x27;]"], [2, 0, 1, "cfg_HideStructureActions", "$cfg[&#x27;HideStructureActions&#x27;]"], [2, 0, 1, "cfg_IconvExtraParams", "$cfg[&#x27;IconvExtraParams&#x27;]"], [2, 0, 1, "cfg_IgnoreMultiSubmitErrors", "$cfg[&#x27;IgnoreMultiSubmitErrors&#x27;]"], [2, 0, 1, "cfg_Import", "$cfg[&#x27;Import&#x27;]"], [2, 0, 1, "cfg_Import_charset", "$cfg[&#x27;Import&#x27;][&#x27;charset&#x27;]"], [2, 0, 1, "cfg_InitialSlidersState", "$cfg[&#x27;InitialSlidersState&#x27;]"], [2, 0, 1, "cfg_InsertRows", "$cfg[&#x27;InsertRows&#x27;]"], [2, 0, 1, "cfg_Lang", "$cfg[&#x27;Lang&#x27;]"], [2, 0, 1, "cfg_LimitChars", "$cfg[&#x27;LimitChars&#x27;]"], [2, 0, 1, "cfg_LinkLengthLimit", "$cfg[&#x27;LinkLengthLimit&#x27;]"], [2, 0, 1, "cfg_LintEnable", "$cfg[&#x27;LintEnable&#x27;]"], [2, 0, 1, "cfg_LoginCookieDeleteAll", "$cfg[&#x27;LoginCookieDeleteAll&#x27;]"], [2, 0, 1, "cfg_LoginCookieRecall", "$cfg[&#x27;LoginCookieRecall&#x27;]"], [2, 0, 1, "cfg_LoginCookieStore", "$cfg[&#x27;LoginCookieStore&#x27;]"], [2, 0, 1, "cfg_LoginCookieValidity", "$cfg[&#x27;LoginCookieValidity&#x27;]"], [2, 0, 1, "cfg_LoginCookieValidityDisableWarning", "$cfg[&#x27;LoginCookieValidityDisableWarning&#x27;]"], [2, 0, 1, "cfg_LongtextDoubleTextarea", "$cfg[&#x27;LongtextDoubleTextarea&#x27;]"], [2, 0, 1, "cfg_MaxCharactersInDisplayedSQL", "$cfg[&#x27;MaxCharactersInDisplayedSQL&#x27;]"], [2, 0, 1, "cfg_MaxDbList", "$cfg[&#x27;MaxDbList&#x27;]"], [2, 0, 1, "cfg_MaxExactCount", "$cfg[&#x27;MaxExactCount&#x27;]"], [2, 0, 1, "cfg_MaxExactCountViews", "$cfg[&#x27;MaxExactCountViews&#x27;]"], [2, 0, 1, "cfg_MaxNavigationItems", "$cfg[&#x27;MaxNavigationItems&#x27;]"], [2, 0, 1, "cfg_MaxRows", "$cfg[&#x27;MaxRows&#x27;]"], [2, 0, 1, "cfg_MaxSizeForInputField", "$cfg[&#x27;MaxSizeForInputField&#x27;]"], [2, 0, 1, "cfg_MaxTableList", "$cfg[&#x27;MaxTableList&#x27;]"], [2, 0, 1, "cfg_MemoryLimit", "$cfg[&#x27;MemoryLimit&#x27;]"], [2, 0, 1, "cfg_MinSizeForInputField", "$cfg[&#x27;MinSizeForInputField&#x27;]"], [2, 0, 1, "cfg_MysqlMinVersion", "$cfg[&#x27;MysqlMinVersion&#x27;]"], [2, 0, 1, "cfg_MysqlSslWarningSafeHosts", "$cfg[&#x27;MysqlSslWarningSafeHosts&#x27;]"], [2, 0, 1, "cfg_NaturalOrder", "$cfg[&#x27;NaturalOrder&#x27;]"], [2, 0, 1, "cfg_NavigationDisplayLogo", "$cfg[&#x27;NavigationDisplayLogo&#x27;]"], [2, 0, 1, "cfg_NavigationDisplayServers", "$cfg[&#x27;NavigationDisplayServers&#x27;]"], [2, 0, 1, "cfg_NavigationLinkWithMainPanel", "$cfg[&#x27;NavigationLinkWithMainPanel&#x27;]"], [2, 0, 1, "cfg_NavigationLogoLink", "$cfg[&#x27;NavigationLogoLink&#x27;]"], [2, 0, 1, "cfg_NavigationLogoLinkWindow", "$cfg[&#x27;NavigationLogoLinkWindow&#x27;]"], [2, 0, 1, "cfg_NavigationTreeAutoexpandSingleDb", "$cfg[&#x27;NavigationTreeAutoexpandSingleDb&#x27;]"], [2, 0, 1, "cfg_NavigationTreeDbSeparator", "$cfg[&#x27;NavigationTreeDbSeparator&#x27;]"], [2, 0, 1, "cfg_NavigationTreeDefaultTabTable", "$cfg[&#x27;NavigationTreeDefaultTabTable&#x27;]"], [2, 0, 1, "cfg_NavigationTreeDefaultTabTable2", "$cfg[&#x27;NavigationTreeDefaultTabTable2&#x27;]"], [2, 0, 1, "cfg_NavigationTreeDisplayDbFilterMinimum", "$cfg[&#x27;NavigationTreeDisplayDbFilterMinimum&#x27;]"], [2, 0, 1, "cfg_NavigationTreeDisplayItemFilterMinimum", "$cfg[&#x27;NavigationTreeDisplayItemFilterMinimum&#x27;]"], [2, 0, 1, "cfg_NavigationTreeEnableExpansion", "$cfg[&#x27;NavigationTreeEnableExpansion&#x27;]"], [2, 0, 1, "cfg_NavigationTreeEnableGrouping", "$cfg[&#x27;NavigationTreeEnableGrouping&#x27;]"], [2, 0, 1, "cfg_NavigationTreePointerEnable", "$cfg[&#x27;NavigationTreePointerEnable&#x27;]"], [2, 0, 1, "cfg_NavigationTreeShowEvents", "$cfg[&#x27;NavigationTreeShowEvents&#x27;]"], [2, 0, 1, "cfg_NavigationTreeShowFunctions", "$cfg[&#x27;NavigationTreeShowFunctions&#x27;]"], [2, 0, 1, "cfg_NavigationTreeShowProcedures", "$cfg[&#x27;NavigationTreeShowProcedures&#x27;]"], [2, 0, 1, "cfg_NavigationTreeShowTables", "$cfg[&#x27;NavigationTreeShowTables&#x27;]"], [2, 0, 1, "cfg_NavigationTreeShowViews", "$cfg[&#x27;NavigationTreeShowViews&#x27;]"], [2, 0, 1, "cfg_NavigationTreeTableLevel", "$cfg[&#x27;NavigationTreeTableLevel&#x27;]"], [2, 0, 1, "cfg_NavigationTreeTableSeparator", "$cfg[&#x27;NavigationTreeTableSeparator&#x27;]"], [2, 0, 1, "cfg_NavigationWidth", "$cfg[&#x27;NavigationWidth&#x27;]"], [2, 0, 1, "cfg_NumFavoriteTables", "$cfg[&#x27;NumFavoriteTables&#x27;]"], [2, 0, 1, "cfg_NumRecentTables", "$cfg[&#x27;NumRecentTables&#x27;]"], [2, 0, 1, "cfg_OBGzip", "$cfg[&#x27;OBGzip&#x27;]"], [2, 0, 1, "cfg_Order", "$cfg[&#x27;Order&#x27;]"], [2, 0, 1, "cfg_PDFDefaultPageSize", "$cfg[&#x27;PDFDefaultPageSize&#x27;]"], [2, 0, 1, "cfg_PDFPageSizes", "$cfg[&#x27;PDFPageSizes&#x27;]"], [2, 0, 1, "cfg_PersistentConnections", "$cfg[&#x27;PersistentConnections&#x27;]"], [2, 0, 1, "cfg_PmaAbsoluteUri", "$cfg[&#x27;PmaAbsoluteUri&#x27;]"], [2, 0, 1, "cfg_PmaNoRelation_DisableWarning", "$cfg[&#x27;PmaNoRelation_DisableWarning&#x27;]"], [2, 0, 1, "cfg_PropertiesNumColumns", "$cfg[&#x27;PropertiesNumColumns&#x27;]"], [2, 0, 1, "cfg_ProtectBinary", "$cfg[&#x27;ProtectBinary&#x27;]"], [2, 0, 1, "cfg_ProxyPass", "$cfg[&#x27;ProxyPass&#x27;]"], [2, 0, 1, "cfg_ProxyUrl", "$cfg[&#x27;ProxyUrl&#x27;]"], [2, 0, 1, "cfg_ProxyUser", "$cfg[&#x27;ProxyUser&#x27;]"], [2, 0, 1, "cfg_QueryHistoryDB", "$cfg[&#x27;QueryHistoryDB&#x27;]"], [2, 0, 1, "cfg_QueryHistoryMax", "$cfg[&#x27;QueryHistoryMax&#x27;]"], [2, 0, 1, "cfg_QueryWindowDefTab", "$cfg[&#x27;QueryWindowDefTab&#x27;]"], [2, 0, 1, "cfg_QueryWindowHeight", "$cfg[&#x27;QueryWindowHeight&#x27;]"], [2, 0, 1, "cfg_QueryWindowWidth", "$cfg[&#x27;QueryWindowWidth&#x27;]"], [2, 0, 1, "cfg_RecodingEngine", "$cfg[&#x27;RecodingEngine&#x27;]"], [2, 0, 1, "cfg_RelationalDisplay", "$cfg[&#x27;RelationalDisplay&#x27;]"], [2, 0, 1, "cfg_RememberSorting", "$cfg[&#x27;RememberSorting&#x27;]"], [2, 0, 1, "cfg_RepeatCells", "$cfg[&#x27;RepeatCells&#x27;]"], [2, 0, 1, "cfg_ReservedWordDisableWarning", "$cfg[&#x27;ReservedWordDisableWarning&#x27;]"], [2, 0, 1, "cfg_RetainQueryBox", "$cfg[&#x27;RetainQueryBox&#x27;]"], [2, 0, 1, "cfg_RowActionLinks", "$cfg[&#x27;RowActionLinks&#x27;]"], [2, 0, 1, "cfg_RowActionLinksWithoutUnique", "$cfg[&#x27;RowActionLinksWithoutUnique&#x27;]"], [2, 0, 1, "cfg_RowActionType", "$cfg[&#x27;RowActionType&#x27;]"], [2, 0, 1, "cfg_SQLQuery_Edit", "$cfg[&#x27;SQLQuery&#x27;][&#x27;Edit&#x27;]"], [2, 0, 1, "cfg_SQLQuery_Explain", "$cfg[&#x27;SQLQuery&#x27;][&#x27;Explain&#x27;]"], [2, 0, 1, "cfg_SQLQuery_Refresh", "$cfg[&#x27;SQLQuery&#x27;][&#x27;Refresh&#x27;]"], [2, 0, 1, "cfg_SQLQuery_ShowAsPHP", "$cfg[&#x27;SQLQuery&#x27;][&#x27;ShowAsPHP&#x27;]"], [2, 0, 1, "cfg_SaveCellsAtOnce", "$cfg[&#x27;SaveCellsAtOnce&#x27;]"], [2, 0, 1, "cfg_SaveDir", "$cfg[&#x27;SaveDir&#x27;]"], [2, 0, 1, "cfg_Schema", "$cfg[&#x27;Schema&#x27;]"], [2, 0, 1, "cfg_Schema_format", "$cfg[&#x27;Schema&#x27;][&#x27;format&#x27;]"], [2, 0, 1, "cfg_SendErrorReports", "$cfg[&#x27;SendErrorReports&#x27;]"], [2, 0, 1, "cfg_ServerDefault", "$cfg[&#x27;ServerDefault&#x27;]"], [2, 0, 1, "cfg_ServerLibraryDifference_DisableWarning", "$cfg[&#x27;ServerLibraryDifference_DisableWarning&#x27;]"], [2, 0, 1, "cfg_Servers", "$cfg[&#x27;Servers&#x27;]"], [2, 0, 1, "cfg_Servers_AllowDeny_order", "$cfg[&#x27;Servers&#x27;][$i][&#x27;AllowDeny&#x27;][&#x27;order&#x27;]"], [2, 0, 1, "cfg_Servers_AllowDeny_rules", "$cfg[&#x27;Servers&#x27;][$i][&#x27;AllowDeny&#x27;][&#x27;rules&#x27;]"], [2, 0, 1, "cfg_Servers_AllowNoPassword", "$cfg[&#x27;Servers&#x27;][$i][&#x27;AllowNoPassword&#x27;]"], [2, 0, 1, "cfg_Servers_AllowRoot", "$cfg[&#x27;Servers&#x27;][$i][&#x27;AllowRoot&#x27;]"], [2, 0, 1, "cfg_Servers_DisableIS", "$cfg[&#x27;Servers&#x27;][$i][&#x27;DisableIS&#x27;]"], [2, 0, 1, "cfg_Servers_LogoutURL", "$cfg[&#x27;Servers&#x27;][$i][&#x27;LogoutURL&#x27;]"], [2, 0, 1, "cfg_Servers_MaxTableUiprefs", "$cfg[&#x27;Servers&#x27;][$i][&#x27;MaxTableUiprefs&#x27;]"], [2, 0, 1, "cfg_Servers_SessionTimeZone", "$cfg[&#x27;Servers&#x27;][$i][&#x27;SessionTimeZone&#x27;]"], [2, 0, 1, "cfg_Servers_SignonCookieParams", "$cfg[&#x27;Servers&#x27;][$i][&#x27;SignonCookieParams&#x27;]"], [2, 0, 1, "cfg_Servers_SignonScript", "$cfg[&#x27;Servers&#x27;][$i][&#x27;SignonScript&#x27;]"], [2, 0, 1, "cfg_Servers_SignonSession", "$cfg[&#x27;Servers&#x27;][$i][&#x27;SignonSession&#x27;]"], [2, 0, 1, "cfg_Servers_SignonURL", "$cfg[&#x27;Servers&#x27;][$i][&#x27;SignonURL&#x27;]"], [2, 0, 1, "cfg_Servers_auth_http_realm", "$cfg[&#x27;Servers&#x27;][$i][&#x27;auth_http_realm&#x27;]"], [2, 0, 1, "cfg_Servers_auth_swekey_config", "$cfg[&#x27;Servers&#x27;][$i][&#x27;auth_swekey_config&#x27;]"], [2, 0, 1, "cfg_Servers_auth_type", "$cfg[&#x27;Servers&#x27;][$i][&#x27;auth_type&#x27;]"], [2, 0, 1, "cfg_Servers_bookmarktable", "$cfg[&#x27;Servers&#x27;][$i][&#x27;bookmarktable&#x27;]"], [2, 0, 1, "cfg_Servers_central_columns", "$cfg[&#x27;Servers&#x27;][$i][&#x27;central_columns&#x27;]"], [2, 0, 1, "cfg_Servers_column_info", "$cfg[&#x27;Servers&#x27;][$i][&#x27;column_info&#x27;]"], [2, 0, 1, "cfg_Servers_compress", "$cfg[&#x27;Servers&#x27;][$i][&#x27;compress&#x27;]"], [2, 0, 1, "cfg_Servers_connect_type", "$cfg[&#x27;Servers&#x27;][$i][&#x27;connect_type&#x27;]"], [2, 0, 1, "cfg_Servers_control_*", "$cfg[&#x27;Servers&#x27;][$i][&#x27;control_*&#x27;]"], [2, 0, 1, "cfg_Servers_controlhost", "$cfg[&#x27;Servers&#x27;][$i][&#x27;controlhost&#x27;]"], [2, 0, 1, "cfg_Servers_controlpass", "$cfg[&#x27;Servers&#x27;][$i][&#x27;controlpass&#x27;]"], [2, 0, 1, "cfg_Servers_controlport", "$cfg[&#x27;Servers&#x27;][$i][&#x27;controlport&#x27;]"], [2, 0, 1, "cfg_Servers_controluser", "$cfg[&#x27;Servers&#x27;][$i][&#x27;controluser&#x27;]"], [2, 0, 1, "cfg_Servers_designer_coords", "$cfg[&#x27;Servers&#x27;][$i][&#x27;designer_coords&#x27;]"], [2, 0, 1, "cfg_Servers_designer_settings", "$cfg[&#x27;Servers&#x27;][$i][&#x27;designer_settings&#x27;]"], [2, 0, 1, "cfg_Servers_export_templates", "$cfg[&#x27;Servers&#x27;][$i][&#x27;export_templates&#x27;]"], [2, 0, 1, "cfg_Servers_extension", "$cfg[&#x27;Servers&#x27;][$i][&#x27;extension&#x27;]"], [2, 0, 1, "cfg_Servers_favorite", "$cfg[&#x27;Servers&#x27;][$i][&#x27;favorite&#x27;]"], [2, 0, 1, "cfg_Servers_hide_connection_errors", "$cfg[&#x27;Servers&#x27;][$i][&#x27;hide_connection_errors&#x27;]"], [2, 0, 1, "cfg_Servers_hide_db", "$cfg[&#x27;Servers&#x27;][$i][&#x27;hide_db&#x27;]"], [2, 0, 1, "cfg_Servers_history", "$cfg[&#x27;Servers&#x27;][$i][&#x27;history&#x27;]"], [2, 0, 1, "cfg_Servers_host", "$cfg[&#x27;Servers&#x27;][$i][&#x27;host&#x27;]"], [2, 0, 1, "cfg_Servers_navigationhiding", "$cfg[&#x27;Servers&#x27;][$i][&#x27;navigationhiding&#x27;]"], [2, 0, 1, "cfg_Servers_nopassword", "$cfg[&#x27;Servers&#x27;][$i][&#x27;nopassword&#x27;]"], [2, 0, 1, "cfg_Servers_only_db", "$cfg[&#x27;Servers&#x27;][$i][&#x27;only_db&#x27;]"], [2, 0, 1, "cfg_Servers_password", "$cfg[&#x27;Servers&#x27;][$i][&#x27;password&#x27;]"], [2, 0, 1, "cfg_Servers_pdf_pages", "$cfg[&#x27;Servers&#x27;][$i][&#x27;pdf_pages&#x27;]"], [2, 0, 1, "cfg_Servers_pmadb", "$cfg[&#x27;Servers&#x27;][$i][&#x27;pmadb&#x27;]"], [2, 0, 1, "cfg_Servers_port", "$cfg[&#x27;Servers&#x27;][$i][&#x27;port&#x27;]"], [2, 0, 1, "cfg_Servers_recent", "$cfg[&#x27;Servers&#x27;][$i][&#x27;recent&#x27;]"], [2, 0, 1, "cfg_Servers_relation", "$cfg[&#x27;Servers&#x27;][$i][&#x27;relation&#x27;]"], [2, 0, 1, "cfg_Servers_savedsearches", "$cfg[&#x27;Servers&#x27;][$i][&#x27;savedsearches&#x27;]"], [2, 0, 1, "cfg_Servers_socket", "$cfg[&#x27;Servers&#x27;][$i][&#x27;socket&#x27;]"], [2, 0, 1, "cfg_Servers_ssl", "$cfg[&#x27;Servers&#x27;][$i][&#x27;ssl&#x27;]"], [2, 0, 1, "cfg_Servers_ssl_ca", "$cfg[&#x27;Servers&#x27;][$i][&#x27;ssl_ca&#x27;]"], [2, 0, 1, "cfg_Servers_ssl_ca_path", "$cfg[&#x27;Servers&#x27;][$i][&#x27;ssl_ca_path&#x27;]"], [2, 0, 1, "cfg_Servers_ssl_cert", "$cfg[&#x27;Servers&#x27;][$i][&#x27;ssl_cert&#x27;]"], [2, 0, 1, "cfg_Servers_ssl_ciphers", "$cfg[&#x27;Servers&#x27;][$i][&#x27;ssl_ciphers&#x27;]"], [2, 0, 1, "cfg_Servers_ssl_key", "$cfg[&#x27;Servers&#x27;][$i][&#x27;ssl_key&#x27;]"], [2, 0, 1, "cfg_Servers_ssl_verify", "$cfg[&#x27;Servers&#x27;][$i][&#x27;ssl_verify&#x27;]"], [2, 0, 1, "cfg_Servers_table_coords", "$cfg[&#x27;Servers&#x27;][$i][&#x27;table_coords&#x27;]"], [2, 0, 1, "cfg_Servers_table_info", "$cfg[&#x27;Servers&#x27;][$i][&#x27;table_info&#x27;]"], [2, 0, 1, "cfg_Servers_table_uiprefs", "$cfg[&#x27;Servers&#x27;][$i][&#x27;table_uiprefs&#x27;]"], [2, 0, 1, "cfg_Servers_tracking", "$cfg[&#x27;Servers&#x27;][$i][&#x27;tracking&#x27;]"], [2, 0, 1, "cfg_Servers_tracking_add_drop_database", "$cfg[&#x27;Servers&#x27;][$i][&#x27;tracking_add_drop_database&#x27;]"], [2, 0, 1, "cfg_Servers_tracking_add_drop_table", "$cfg[&#x27;Servers&#x27;][$i][&#x27;tracking_add_drop_table&#x27;]"], [2, 0, 1, "cfg_Servers_tracking_add_drop_view", "$cfg[&#x27;Servers&#x27;][$i][&#x27;tracking_add_drop_view&#x27;]"], [2, 0, 1, "cfg_Servers_tracking_default_statements", "$cfg[&#x27;Servers&#x27;][$i][&#x27;tracking_default_statements&#x27;]"], [2, 0, 1, "cfg_Servers_tracking_version_auto_create", "$cfg[&#x27;Servers&#x27;][$i][&#x27;tracking_version_auto_create&#x27;]"], [2, 0, 1, "cfg_Servers_user", "$cfg[&#x27;Servers&#x27;][$i][&#x27;user&#x27;]"], [2, 0, 1, "cfg_Servers_userconfig", "$cfg[&#x27;Servers&#x27;][$i][&#x27;userconfig&#x27;]"], [2, 0, 1, "cfg_Servers_usergroups", "$cfg[&#x27;Servers&#x27;][$i][&#x27;usergroups&#x27;]"], [2, 0, 1, "cfg_Servers_users", "$cfg[&#x27;Servers&#x27;][$i][&#x27;users&#x27;]"], [2, 0, 1, "cfg_Servers_verbose", "$cfg[&#x27;Servers&#x27;][$i][&#x27;verbose&#x27;]"], [2, 0, 1, "cfg_SessionSavePath", "$cfg[&#x27;SessionSavePath&#x27;]"], [2, 0, 1, "cfg_ShowAll", "$cfg[&#x27;ShowAll&#x27;]"], [2, 0, 1, "cfg_ShowBrowseComments", "$cfg[&#x27;ShowBrowseComments&#x27;]"], [2, 0, 1, "cfg_ShowChgPassword", "$cfg[&#x27;ShowChgPassword&#x27;]"], [2, 0, 1, "cfg_ShowColumnComments", "$cfg[&#x27;ShowColumnComments&#x27;]"], [2, 0, 1, "cfg_ShowCreateDb", "$cfg[&#x27;ShowCreateDb&#x27;]"], [2, 0, 1, "cfg_ShowDatabasesNavigationAsTree", "$cfg[&#x27;ShowDatabasesNavigationAsTree&#x27;]"], [2, 0, 1, "cfg_ShowDbStructureCharset", "$cfg[&#x27;ShowDbStructureCharset&#x27;]"], [2, 0, 1, "cfg_ShowDbStructureComment", "$cfg[&#x27;ShowDbStructureComment&#x27;]"], [2, 0, 1, "cfg_ShowDbStructureCreation", "$cfg[&#x27;ShowDbStructureCreation&#x27;]"], [2, 0, 1, "cfg_ShowDbStructureLastCheck", "$cfg[&#x27;ShowDbStructureLastCheck&#x27;]"], [2, 0, 1, "cfg_ShowDbStructureLastUpdate", "$cfg[&#x27;ShowDbStructureLastUpdate&#x27;]"], [2, 0, 1, "cfg_ShowFieldTypesInDataEditView", "$cfg[&#x27;ShowFieldTypesInDataEditView&#x27;]"], [2, 0, 1, "cfg_ShowFunctionFields", "$cfg[&#x27;ShowFunctionFields&#x27;]"], [2, 0, 1, "cfg_ShowGitRevision", "$cfg[&#x27;ShowGitRevision&#x27;]"], [2, 0, 1, "cfg_ShowHint", "$cfg[&#x27;ShowHint&#x27;]"], [2, 0, 1, "cfg_ShowPhpInfo", "$cfg[&#x27;ShowPhpInfo&#x27;]"], [2, 0, 1, "cfg_ShowPropertyComments", "$cfg[&#x27;ShowPropertyComments&#x27;]"], [2, 0, 1, "cfg_ShowSQL", "$cfg[&#x27;ShowSQL&#x27;]"], [2, 0, 1, "cfg_ShowServerInfo", "$cfg[&#x27;ShowServerInfo&#x27;]"], [2, 0, 1, "cfg_ShowStats", "$cfg[&#x27;ShowStats&#x27;]"], [2, 0, 1, "cfg_SkipLockedTables", "$cfg[&#x27;SkipLockedTables&#x27;]"], [2, 0, 1, "cfg_SuhosinDisableWarning", "$cfg[&#x27;SuhosinDisableWarning&#x27;]"], [2, 0, 1, "cfg_TableNavigationLinksMode", "$cfg[&#x27;TableNavigationLinksMode&#x27;]"], [2, 0, 1, "cfg_TablePrimaryKeyOrder", "$cfg[&#x27;TablePrimaryKeyOrder&#x27;]"], [2, 0, 1, "cfg_TabsMode", "$cfg[&#x27;TabsMode&#x27;]"], [2, 0, 1, "cfg_TempDir", "$cfg[&#x27;TempDir&#x27;]"], [2, 0, 1, "cfg_TextareaAutoSelect", "$cfg[&#x27;TextareaAutoSelect&#x27;]"], [2, 0, 1, "cfg_TextareaCols", "$cfg[&#x27;TextareaCols&#x27;]"], [2, 0, 1, "cfg_TextareaRows", "$cfg[&#x27;TextareaRows&#x27;]"], [2, 0, 1, "cfg_ThemeDefault", "$cfg[&#x27;ThemeDefault&#x27;]"], [2, 0, 1, "cfg_ThemeManager", "$cfg[&#x27;ThemeManager&#x27;]"], [2, 0, 1, "cfg_ThemePerServer", "$cfg[&#x27;ThemePerServer&#x27;]"], [2, 0, 1, "cfg_TitleDatabase", "$cfg[&#x27;TitleDatabase&#x27;]"], [2, 0, 1, "cfg_TitleDefault", "$cfg[&#x27;TitleDefault&#x27;]"], [2, 0, 1, "cfg_TitleServer", "$cfg[&#x27;TitleServer&#x27;]"], [2, 0, 1, "cfg_TitleTable", "$cfg[&#x27;TitleTable&#x27;]"], [2, 0, 1, "cfg_TranslationWarningThreshold", "$cfg[&#x27;TranslationWarningThreshold&#x27;]"], [2, 0, 1, "cfg_TrustedProxies", "$cfg[&#x27;TrustedProxies&#x27;]"], [2, 0, 1, "cfg_URLQueryEncryption", "$cfg[&#x27;URLQueryEncryption&#x27;]"], [2, 0, 1, "cfg_URLQueryEncryptionSecretKey", "$cfg[&#x27;URLQueryEncryptionSecretKey&#x27;]"], [2, 0, 1, "cfg_UploadDir", "$cfg[&#x27;UploadDir&#x27;]"], [2, 0, 1, "cfg_UseDbSearch", "$cfg[&#x27;UseDbSearch&#x27;]"], [2, 0, 1, "cfg_UserprefsDeveloperTab", "$cfg[&#x27;UserprefsDeveloperTab&#x27;]"], [2, 0, 1, "cfg_UserprefsDisallow", "$cfg[&#x27;UserprefsDisallow&#x27;]"], [2, 0, 1, "cfg_VersionCheck", "$cfg[&#x27;VersionCheck&#x27;]"], [2, 0, 1, "cfg_ZeroConf", "$cfg[&#x27;ZeroConf&#x27;]"], [2, 0, 1, "cfg_ZipDump", "$cfg[&#x27;ZipDump&#x27;]"], [2, 0, 1, "cfg_blowfish_secret", "$cfg[&#x27;blowfish_secret&#x27;]"], [2, 0, 1, "cfg_enable_drag_drop_import", "$cfg[&#x27;enable_drag_drop_import&#x27;]"], [2, 0, 1, "cfg_environment", "$cfg[&#x27;environment&#x27;]"], [2, 0, 1, "cfg_maxRowPlotLimit", "$cfg[&#x27;maxRowPlotLimit&#x27;]"], [8, 1, 1, "", "comment"], [8, 1, 1, "", "data"], [8, 1, 1, "", "database"], [8, 1, 1, "", "name"], [8, 1, 1, "", "type"], [8, 1, 1, "", "version"], [17, 2, 1, "-", "APACHE_PORT"], [17, 2, 1, "-", "HIDE_PHP_VERSION"], [17, 2, 1, "-", "MAX_EXECUTION_TIME"], [17, 2, 1, "-", "MEMORY_LIMIT"], [17, 2, 1, "-", "PMA_ABSOLUTE_URI"], [17, 2, 1, "-", "PMA_ARBITRARY"], [17, 2, 1, "-", "PMA_CONFIG_BASE64"], [17, 2, 1, "-", "PMA_CONTROLHOST"], [17, 2, 1, "-", "PMA_CONTROLPASS"], [17, 2, 1, "-", "PMA_CONTROLPORT"], [17, 2, 1, "-", "PMA_CONTROLUSER"], [17, 2, 1, "-", "PMA_HOST"], [17, 2, 1, "-", "PMA_HOSTS"], [17, 2, 1, "-", "PMA_PASSWORD"], [17, 2, 1, "-", "PMA_PMADB"], [17, 2, 1, "-", "PMA_PORT"], [17, 2, 1, "-", "PMA_PORTS"], [17, 2, 1, "-", "PMA_QUERYHISTORYDB"], [17, 2, 1, "-", "PMA_QUERYHISTORYMAX"], [17, 2, 1, "-", "PMA_SAVEDIR"], [17, 2, 1, "-", "PMA_SOCKET"], [17, 2, 1, "-", "PMA_SOCKETS"], [17, 2, 1, "-", "PMA_SSL"], [17, 2, 1, "-", "PMA_SSLS"], [17, 2, 1, "-", "PMA_SSL_CA"], [17, 2, 1, "-", "PMA_SSL_CAS"], [17, 2, 1, "-", "PMA_SSL_CAS_BASE64"], [17, 2, 1, "-", "PMA_SSL_CA_BASE64"], [17, 2, 1, "-", "PMA_SSL_CERT"], [17, 2, 1, "-", "PMA_SSL_CERTS"], [17, 2, 1, "-", "PMA_SSL_CERTS_BASE64"], [17, 2, 1, "-", "PMA_SSL_CERT_BASE64"], [17, 2, 1, "-", "PMA_SSL_DIR"], [17, 2, 1, "-", "PMA_SSL_KEY"], [17, 2, 1, "-", "PMA_SSL_KEYS"], [17, 2, 1, "-", "PMA_SSL_KEYS_BASE64"], [17, 2, 1, "-", "PMA_SSL_KEY_BASE64"], [17, 2, 1, "-", "PMA_SSL_VERIFIES"], [17, 2, 1, "-", "PMA_SSL_VERIFY"], [17, 2, 1, "-", "PMA_UPLOADDIR"], [17, 2, 1, "-", "PMA_USER"], [17, 2, 1, "-", "PMA_USER_CONFIG_BASE64"], [17, 2, 1, "-", "PMA_VERBOSE"], [17, 2, 1, "-", "PMA_VERBOSES"], [17, 2, 1, "-", "TZ"], [17, 2, 1, "-", "UPLOAD_LIMIT"]]}, "objtypes": {"0": "config:option", "1": "js:data", "2": "std:envvar"}, "objnames": {"0": ["config", "option", "Config config option"], "1": ["js", "data", "JavaScript data"], "2": ["std", "envvar", "environment variable"]}, "titleterms": {"bookmark": [0, 6], "store": [0, 6], "variabl": [0, 6, 17], "insid": 0, "brows": [0, 2, 6, 17], "tabl": [0, 6, 8, 9], "us": [0, 2, 6, 8, 17], "chart": [1, 6], "implement": 1, "exampl": [1, 2, 6, 13, 17], "pie": 1, "bar": [1, 6], "column": [1, 6, 8, 17], "scatter": 1, "line": 1, "spline": 1, "timelin": 1, "configur": [2, 6, 12, 16, 17, 18], "basic": 2, "set": [2, 6], "server": [2, 6, 14, 17], "connect": [2, 6, 17], "gener": [2, 6], "cooki": [2, 6, 17], "authent": [2, 6, 17, 20], "option": [2, 6, 8], "navig": [2, 6], "panel": 2, "setup": [2, 6, 17], "main": 2, "databas": [2, 6, 12, 14, 17], "structur": [2, 6, 19], "mode": [2, 6, 17], "edit": [2, 6, 12], "export": [2, 6, 8], "import": [2, 6, 8], "tab": [2, 6], "displai": [2, 6], "pdf": [2, 6, 8], "languag": [2, 6], "web": [2, 14], "theme": [2, 6, 18], "design": [2, 6, 13], "custom": [2, 6, 17, 18], "text": [2, 6, 8], "field": [2, 6], "sql": [2, 6, 8, 15], "queri": [2, 6], "box": [2, 6], "upload": [2, 6], "save": 2, "directori": 2, "variou": 2, "page": [2, 6], "titl": 2, "manag": [2, 12], "default": [2, 6], "mysql": [2, 6], "transform": [2, 6, 19], "consol": 2, "develop": [2, 5, 6], "signon": [2, 17], "ip": 2, "address": 2, "limit": [2, 6], "autologin": 2, "multipl": [2, 6], "googl": 2, "cloud": [2, 17], "ssl": [2, 17], "amazon": 2, "rd": 2, "aurora": 2, "recaptcha": 2, "hcaptcha": 2, "turnstil": 2, "v2": 2, "v3": 2, "copyright": 3, "third": 3, "parti": 3, "licens": [3, 22], "credit": 4, "chronolog": 4, "order": 4, "translat": [4, 6], "document": [4, 6, 8, 9], "origin": 4, "version": [4, 6, 17], "2": [4, 6], "1": [4, 6], "0": [4, 6], "inform": [5, 6, 11], "faq": 6, "frequent": 6, "ask": 6, "question": 6, "my": 6, "i": 6, "crash": 6, "each": 6, "time": 6, "specif": [6, 12, 17, 22], "action": 6, "requir": [6, 14], "phpmyadmin": [6, 9, 16, 17, 22], "send": 6, "blank": 6, "full": 6, "cryptic": 6, "charact": 6, "browser": [6, 14], "what": 6, "can": 6, "do": 6, "apach": 6, "when": 6, "3": 6, "withdrawn": 6, "4": 6, "ii": 6, "m": 6, "error": 6, "messag": 6, "The": 6, "specifi": 6, "cgi": 6, "applic": [6, 20], "misbehav": 6, "return": 6, "complet": 6, "http": [6, 17], "header": 6, "5": 6, "face": 6, "mani": 6, "6": 6, "t": 6, "pw": 6, "noth": 6, "7": 6, "how": 6, "gzip": 6, "dump": 6, "csv": [6, 8], "It": 6, "doe": 6, "seem": 6, "work": 6, "8": 6, "cannot": 6, "insert": 6, "file": [6, 8, 17, 19, 22], "get": 6, "an": [6, 12, 17], "about": [6, 10], "safe": 6, "being": 6, "effect": 6, "9": 6, "10": 6, "have": 6, "troubl": [6, 17], "run": [6, 17], "secur": [6, 15, 17, 20], "internet": 6, "explor": 6, "11": 6, "open_basedir": 6, "restrict": 6, "while": 6, "from": [6, 17], "12": 6, "lost": 6, "root": 6, "password": 6, "13": 6, "14": 6, "15": 6, "problem": 6, "user": [6, 10, 12, 17, 21], "name": [6, 8], "16": 6, "big": 6, "memori": 6, "timeout": 6, "17": 6, "which": 6, "support": [6, 10], "17a": 6, "alwai": 6, "client": 6, "protocol": 6, "request": [6, 15], "consid": 6, "upgrad": [6, 17], "18": 6, "19": 6, "relat": [6, 13], "featur": [6, 10], "becaus": 6, "script": [6, 15, 17], "know": 6, "font": 6, "20": 6, "receiv": 6, "miss": 6, "mysqli": 6, "extens": 6, "21": 6, "am": 6, "php": [6, 8, 14], "under": 6, "unix": 6, "log": [6, 17], "auth": 6, "22": 6, "don": 6, "see": 6, "locat": 6, "so": 6, "23": 6, "win32": 6, "machin": 6, "creat": [6, 12, 17, 18], "new": [6, 12], "ar": [6, 17], "chang": 6, "lowercas": 6, "24": 6, "25": 6, "mod_gzip": 6, "26": 6, "1a": 6, "window": [6, 17], "xp": 6, "undefin": 6, "just": 6, "instal": [6, 17], "No": 6, "input": 6, "try": 6, "27": 6, "empti": 6, "want": 6, "view": [6, 13], "huge": 6, "eg": 6, "db_structur": 6, "plenti": 6, "28": 6, "sometim": 6, "refus": 6, "errorcod": 6, "thi": 6, "mean": 6, "29": 6, "modifi": 6, "duplic": 6, "30": 6, "hash": 6, "31": 6, "32": 6, "33": 6, "34": 6, "directli": 6, "access": 6, "35": 6, "36": 6, "500": 6, "intern": 6, "37": 6, "cluster": 6, "differ": 6, "encrypt": 6, "doesn": 6, "38": 6, "suhosin": 6, "enabl": 6, "39": 6, "via": 6, "redirect": 6, "back": [6, 17], "caus": 6, "behavior": 6, "40": 6, "revers": 6, "proxi": 6, "login": 6, "41": 6, "its": 6, "privileg": [6, 12, 17], "unknown": 6, "42": 6, "prevent": 6, "robot": 6, "43": 6, "why": 6, "contain": 6, "hundr": 6, "44": 6, "reduc": 6, "size": 6, "disk": 6, "45": 6, "method": 6, "caching_sha2_password": 6, "warn": 6, "add": 6, "alreadi": 6, "sent": 6, "": [6, 9], "wrong": 6, "fail": 6, "local": 6, "through": 6, "socket": 6, "tmp": 6, "sock": 6, "111": 6, "row": 6, "drop": 6, "404": 6, "found": 6, "again": 6, "deni": 6, "localhost": 6, "ye": 6, "host": 6, "port": 6, "forward": 6, "paramet": 6, "progress": 6, "string": 6, "random": 6, "byte": 6, "known": [6, 17], "who": 6, "out": [6, 17], "same": 6, "nick": 6, "larg": 6, "compress": 6, "With": 6, "innodb": 6, "lose": 6, "foreign": 6, "kei": [6, 10, 20], "relationship": 6, "renam": 6, "unabl": [6, 17], "mysqldump": 6, "tool": 6, "bundl": 6, "distribut": [6, 17, 22], "nest": 6, "folder": 6, "hierarchi": 6, "manner": 6, "100": 6, "seri": 6, "like": 6, "pars": 6, "url": 6, "fix": 6, "clickabl": 6, "html": 6, "form": 6, "where": 6, "put": 6, "mime": 6, "onto": 6, "sql_mode": 6, "ansi": 6, "homonym": 6, "primari": 6, "result": 6, "select": 6, "more": 6, "one": 6, "valu": 6, "lastnam": 6, "employe": 6, "firstnam": 6, "A": [6, 10], "two": [6, 20], "smith": 6, "click": 6, "sure": 6, "intend": 6, "number": 6, "correct": 6, "enter": 6, "follow": 6, "db": 6, "hyphen": 6, "abl": 6, "right": 6, "thei": 6, "lump": 6, "togeth": 6, "singl": 6, "determin": 6, "appropri": 6, "data": [6, 8], "onli": 6, "int": 6, "decim": 6, "varchar": 6, "type": 6, "after": [6, 17], "some": 6, "gone": 6, "content": 6, "shown": 6, "usernam": 6, "unicod": 6, "\u00e1": 6, "isp": 6, "multi": 6, "central": 6, "copi": 6, "need": 6, "prefer": 6, "wai": 6, "make": 6, "against": 6, "evil": 6, "includ": 6, "lang": 6, "librari": [6, 22], "give": 6, "possibl": 6, "let": 6, "own": 6, "base": 6, "addit": 6, "than": 6, "onc": 6, "start": 6, "o": 6, "control": 6, "non": 6, "function": 6, "xitami": 6, "5b4": 6, "won": 6, "process": 6, "konqueror": 6, "never": 6, "refresh": 6, "reload": 6, "come": 6, "welcom": [6, 9], "mozilla": 6, "netscap": 6, "pr1": 6, "whitespac": 6, "area": 6, "scroll": 6, "down": 6, "extend": 6, "ascii": 6, "german": 6, "umlaut": 6, "mac": 6, "x": 6, "safari": 6, "special": 6, "javascript": 6, "Or": 6, "firefox": 6, "delet": [6, 12], "violat": 6, "polici": [6, 15], "potenti": 6, "unsaf": 6, "oper": 6, "execut": 6, "bring": 6, "up": 6, "index": 6, "those": 6, "null": 6, "backup": 6, "restor": 6, "produc": 6, "schema": 6, "happen": 6, "underscor": 6, "curiou": 6, "symbol": 6, "\u00f8": 6, "statist": 6, "understand": 6, "would": 6, "dot": 6, "blob": 6, "sai": 6, "without": 6, "length": 6, "simpli": 6, "move": 6, "mimetyp": 6, "ani": 6, "below": 6, "simpl": [6, 20], "latex": [6, 8], "lot": 6, "mine": 6, "them": 6, "In": 6, "list": 6, "automat": 6, "heard": 6, "microsoft": [6, 8], "excel": [6, 8], "now": 6, "nativ": 6, "comment": 6, "pmadb": 6, "rang": 6, "format": 6, "esri": [6, 8], "shapefil": 6, "zoom": 6, "search": 6, "favorit": 6, "improv": 6, "reassign": 6, "auto": 6, "increment": 6, "adjust": 6, "procedur": 6, "bind": 6, "checkbox": 6, "write": 6, "parameter": 6, "older": [6, 17], "pre": 6, "newer": 6, "fine": 6, "project": 6, "bug": 6, "exist": [6, 12], "help": 6, "should": 6, "proce": 6, "alert": 6, "issu": [6, 15, 17], "protect": 6, "brute": [6, 15], "forc": [6, 15], "attack": [6, 15], "path": 6, "disclosur": 6, "load": [6, 8], "certain": 6, "could": 6, "allow": 6, "formula": 6, "inject": [6, 15], "synchron": 6, "glossari": 7, "shape": 8, "mediawiki": 8, "open": 8, "spreadsheet": 8, "od": 8, "xml": 8, "codegen": 8, "word": [8, 10], "2000": 8, "json": 8, "opendocu": 8, "arrai": 8, "texi": 8, "yaml": 8, "indic": 9, "introduct": [10, 19], "shortcut": 10, "other": 11, "sourc": 11, "print": 11, "book": 11, "tutori": 11, "\u010deski": 11, "czech": 11, "english": 11, "\u0440\u0443\u0441\u0441\u043a\u0438\u0439": 11, "russian": 11, "assign": 12, "menu": 12, "group": 12, "technic": 13, "info": 13, "typic": 15, "vulner": 15, "cross": 15, "site": 15, "xss": 15, "forgeri": 15, "csrf": 15, "report": 15, "linux": 17, "debian": 17, "ubuntu": 17, "opensus": 17, "gentoo": 17, "mandriva": 17, "fedora": 17, "red": 17, "hat": 17, "enterpris": 17, "git": 17, "compos": 17, "docker": 17, "environ": 17, "volum": 17, "behind": 17, "haproxi": 17, "subdirectori": 17, "ibm": 17, "quick": 17, "manual": 17, "deriv": 17, "verifi": 17, "releas": 17, "storag": 17, "zero": 17, "config": 17, "your": 17, "metadata": 18, "share": 18, "imag": 18, "usag": 19, "factor": 20, "2fa": 20, "hardwar": 20, "fido": 20, "u2f": 20, "guid": 21, "packag": 22, "extern": 22, "vendor": 22}, "envversion": {"sphinx.domains.c": 2, "sphinx.domains.changeset": 1, "sphinx.domains.citation": 1, "sphinx.domains.cpp": 8, "sphinx.domains.index": 1, "sphinx.domains.javascript": 2, "sphinx.domains.math": 2, "sphinx.domains.python": 3, "sphinx.domains.rst": 2, "sphinx.domains.std": 2, "sphinx": 57}, "alltitles": {"Bookmarks": [[0, "bookmarks"]], "Storing bookmarks": [[0, "storing-bookmarks"]], "Variables inside bookmarks": [[0, "variables-inside-bookmarks"]], "Browsing a table using a bookmark": [[0, "browsing-a-table-using-a-bookmark"]], "Charts": [[1, "charts"]], "Chart implementation": [[1, "chart-implementation"]], "Examples": [[1, "examples"], [2, "examples"]], "Pie chart": [[1, "pie-chart"]], "Bar and column chart": [[1, "bar-and-column-chart"]], "Scatter chart": [[1, "scatter-chart"]], "Line, spline and timeline charts": [[1, "line-spline-and-timeline-charts"]], "Configuration": [[2, "configuration"], [6, "configuration"], [18, "configuration"]], "Basic settings": [[2, "basic-settings"]], "Server connection settings": [[2, "server-connection-settings"]], "Generic settings": [[2, "generic-settings"]], "Cookie authentication options": [[2, "cookie-authentication-options"]], "Navigation panel setup": [[2, "navigation-panel-setup"]], "Main panel": [[2, "main-panel"]], "Database structure": [[2, "database-structure"]], "Browse mode": [[2, "browse-mode"]], "Editing mode": [[2, "editing-mode"]], "Export and import settings": [[2, "export-and-import-settings"]], "Tabs display settings": [[2, "tabs-display-settings"]], "PDF Options": [[2, "pdf-options"]], "Languages": [[2, "languages"]], "Web server settings": [[2, "web-server-settings"]], "Theme settings": [[2, "theme-settings"]], "Design customization": [[2, "design-customization"]], "Text fields": [[2, "text-fields"]], "SQL query box settings": [[2, "sql-query-box-settings"]], "Web server upload/save/import directories": [[2, "web-server-upload-save-import-directories"]], "Various display setting": [[2, "various-display-setting"]], "Page titles": [[2, "page-titles"]], "Theme manager settings": [[2, "theme-manager-settings"]], "Default queries": [[2, "default-queries"]], "MySQL settings": [[2, "mysql-settings"]], "Default options for Transformations": [[2, "default-options-for-transformations"]], "Console settings": [[2, "console-settings"]], "Developer": [[2, "developer"]], "Basic example": [[2, "basic-example"]], "Example for signon authentication": [[2, "example-for-signon-authentication"]], "Example for IP address limited autologin": [[2, "example-for-ip-address-limited-autologin"]], "Example for using multiple MySQL servers": [[2, "example-for-using-multiple-mysql-servers"]], "Google Cloud SQL with SSL": [[2, "google-cloud-sql-with-ssl"]], "Amazon RDS Aurora with SSL": [[2, "amazon-rds-aurora-with-ssl"]], "reCaptcha using hCaptcha": [[2, "recaptcha-using-hcaptcha"]], "reCaptcha using Turnstile": [[2, "recaptcha-using-turnstile"]], "reCaptcha using Google reCaptcha v2/v3": [[2, "recaptcha-using-google-recaptcha-v2-v3"]], "Copyright": [[3, "copyright"]], "Third party licenses": [[3, "third-party-licenses"]], "Credits": [[4, "credits"]], "Credits, in chronological order": [[4, "credits-in-chronological-order"]], "Translators": [[4, "translators"]], "Documentation translators": [[4, "documentation-translators"]], "Original Credits of Version 2.1.0": [[4, "original-credits-of-version-2-1-0"]], "Developers Information": [[5, "developers-information"]], "FAQ - Frequently Asked Questions": [[6, "faq-frequently-asked-questions"]], "Server": [[6, "server"]], "1.1 My server is crashing each time a specific action is required or phpMyAdmin sends a blank page or a page full of cryptic characters to my browser, what can I do?": [[6, "my-server-is-crashing-each-time-a-specific-action-is-required-or-phpmyadmin-sends-a-blank-page-or-a-page-full-of-cryptic-characters-to-my-browser-what-can-i-do"]], "1.2 My Apache server crashes when using phpMyAdmin.": [[6, "my-apache-server-crashes-when-using-phpmyadmin"]], "1.3 (withdrawn).": [[6, "withdrawn"]], "1.4 Using phpMyAdmin on IIS, I\u2019m displayed the error message: \u201cThe specified CGI application misbehaved by not returning a complete set of HTTP headers \u2026\u201d.": [[6, "using-phpmyadmin-on-iis-i-m-displayed-the-error-message-the-specified-cgi-application-misbehaved-by-not-returning-a-complete-set-of-http-headers"]], "1.5 Using phpMyAdmin on IIS, I\u2019m facing crashes and/or many error messages with the HTTP.": [[6, "using-phpmyadmin-on-iis-i-m-facing-crashes-and-or-many-error-messages-with-the-http"]], "1.6 I can\u2019t use phpMyAdmin on PWS: nothing is displayed!": [[6, "i-can-t-use-phpmyadmin-on-pws-nothing-is-displayed"]], "1.7 How can I gzip a dump or a CSV export? It does not seem to work.": [[6, "how-can-i-gzip-a-dump-or-a-csv-export-it-does-not-seem-to-work"]], "1.8 I cannot insert a text file in a table, and I get an error about safe mode being in effect.": [[6, "i-cannot-insert-a-text-file-in-a-table-and-i-get-an-error-about-safe-mode-being-in-effect"]], "1.9 (withdrawn).": [[6, "faq1-9"]], "1.10 I\u2019m having troubles when uploading files with phpMyAdmin running on a secure server. My browser is Internet Explorer and I\u2019m using the Apache server.": [[6, "i-m-having-troubles-when-uploading-files-with-phpmyadmin-running-on-a-secure-server-my-browser-is-internet-explorer-and-i-m-using-the-apache-server"]], "1.11 I get an \u2018open_basedir restriction\u2019 while uploading a file from the import tab.": [[6, "i-get-an-open-basedir-restriction-while-uploading-a-file-from-the-import-tab"]], "1.12 I have lost my MySQL root password, what can I do?": [[6, "i-have-lost-my-mysql-root-password-what-can-i-do"]], "1.13 (withdrawn).": [[6, "faq1-13"]], "1.14 (withdrawn).": [[6, "faq1-14"]], "1.15 I have problems with mysql.user column names.": [[6, "i-have-problems-with-mysql-user-column-names"]], "1.16 I cannot upload big dump files (memory, HTTP or timeout problems).": [[6, "i-cannot-upload-big-dump-files-memory-http-or-timeout-problems"]], "1.17 Which Database versions does phpMyAdmin support?": [[6, "which-database-versions-does-phpmyadmin-support"]], "1.17a I cannot connect to the MySQL server. It always returns the error message, \u201cClient does not support authentication protocol requested by server; consider upgrading MySQL client\u201d": [[6, "a-i-cannot-connect-to-the-mysql-server-it-always-returns-the-error-message-client-does-not-support-authentication-protocol-requested-by-server-consider-upgrading-mysql-client"]], "1.18 (withdrawn).": [[6, "faq1-18"]], "1.19 I can\u2019t run the \u201cdisplay relations\u201d feature because the script seems not to know the font face I\u2019m using!": [[6, "i-can-t-run-the-display-relations-feature-because-the-script-seems-not-to-know-the-font-face-i-m-using"]], "1.20 I receive an error about missing mysqli and mysql extensions.": [[6, "i-receive-an-error-about-missing-mysqli-and-mysql-extensions"]], "1.21 I am running the CGI version of PHP under Unix, and I cannot log in using cookie auth.": [[6, "i-am-running-the-cgi-version-of-php-under-unix-and-i-cannot-log-in-using-cookie-auth"]], "1.22 I don\u2019t see the \u201cLocation of text file\u201d field, so I cannot upload.": [[6, "i-don-t-see-the-location-of-text-file-field-so-i-cannot-upload"]], "1.23 I\u2019m running MySQL on a Win32 machine. Each time I create a new table the table and column names are changed to lowercase!": [[6, "i-m-running-mysql-on-a-win32-machine-each-time-i-create-a-new-table-the-table-and-column-names-are-changed-to-lowercase"]], "1.24 (withdrawn).": [[6, "faq1-24"]], "1.25 I am running Apache with mod_gzip-1.3.26.1a on Windows XP, and I get problems, such as undefined variables when I run a SQL query.": [[6, "i-am-running-apache-with-mod-gzip-1-3-26-1a-on-windows-xp-and-i-get-problems-such-as-undefined-variables-when-i-run-a-sql-query"]], "1.26 I just installed phpMyAdmin in my document root of IIS but I get the error \u201cNo input file specified\u201d when trying to run phpMyAdmin.": [[6, "i-just-installed-phpmyadmin-in-my-document-root-of-iis-but-i-get-the-error-no-input-file-specified-when-trying-to-run-phpmyadmin"]], "1.27 I get empty page when I want to view huge page (eg. db_structure.php with plenty of tables).": [[6, "i-get-empty-page-when-i-want-to-view-huge-page-eg-db-structure-php-with-plenty-of-tables"]], "1.28 My MySQL server sometimes refuses queries and returns the message \u2018Errorcode: 13\u2019. What does this mean?": [[6, "my-mysql-server-sometimes-refuses-queries-and-returns-the-message-errorcode-13-what-does-this-mean"]], "1.29 When I create a table or modify a column, I get an error and the columns are duplicated.": [[6, "when-i-create-a-table-or-modify-a-column-i-get-an-error-and-the-columns-are-duplicated"]], "1.30 I get the error \u201cnavigation.php: Missing hash\u201d.": [[6, "i-get-the-error-navigation-php-missing-hash"]], "1.31 Which PHP versions does phpMyAdmin support?": [[6, "which-php-versions-does-phpmyadmin-support"]], "1.32 Can I use HTTP authentication with IIS?": [[6, "can-i-use-http-authentication-with-iis"]], "1.33 (withdrawn).": [[6, "faq1-33"]], "1.34 Can I directly access a database or table pages?": [[6, "can-i-directly-access-a-database-or-table-pages"]], "1.35 Can I use HTTP authentication with Apache CGI?": [[6, "can-i-use-http-authentication-with-apache-cgi"]], "1.36 I get an error \u201c500 Internal Server Error\u201d.": [[6, "i-get-an-error-500-internal-server-error"]], "1.37 I run phpMyAdmin on cluster of different machines and password encryption in cookie auth doesn\u2019t work.": [[6, "i-run-phpmyadmin-on-cluster-of-different-machines-and-password-encryption-in-cookie-auth-doesn-t-work"]], "1.38 Can I use phpMyAdmin on a server on which Suhosin is enabled?": [[6, "can-i-use-phpmyadmin-on-a-server-on-which-suhosin-is-enabled"]], "1.39 When I try to connect via https, I can log in, but then my connection is redirected back to http. What can cause this behavior?": [[6, "when-i-try-to-connect-via-https-i-can-log-in-but-then-my-connection-is-redirected-back-to-http-what-can-cause-this-behavior"]], "1.40 When accessing phpMyAdmin via an Apache reverse proxy, cookie login does not work.": [[6, "when-accessing-phpmyadmin-via-an-apache-reverse-proxy-cookie-login-does-not-work"]], "1.41 When I view a database and ask to see its privileges, I get an error about an unknown column.": [[6, "when-i-view-a-database-and-ask-to-see-its-privileges-i-get-an-error-about-an-unknown-column"]], "1.42 How can I prevent robots from accessing phpMyAdmin?": [[6, "how-can-i-prevent-robots-from-accessing-phpmyadmin"]], "1.43 Why can\u2019t I display the structure of my table containing hundreds of columns?": [[6, "why-can-t-i-display-the-structure-of-my-table-containing-hundreds-of-columns"]], "1.44 How can I reduce the installed size of phpMyAdmin on disk?": [[6, "how-can-i-reduce-the-installed-size-of-phpmyadmin-on-disk"]], "1.45 I get an error message about unknown authentication method caching_sha2_password when trying to log in": [[6, "i-get-an-error-message-about-unknown-authentication-method-caching-sha2-password-when-trying-to-log-in"]], "2.1 The error message \u201cWarning: Cannot add header information - headers already sent by \u2026\u201d is displayed, what\u2019s the problem?": [[6, "the-error-message-warning-cannot-add-header-information-headers-already-sent-by-is-displayed-what-s-the-problem"]], "2.2 phpMyAdmin can\u2019t connect to MySQL. What\u2019s wrong?": [[6, "phpmyadmin-can-t-connect-to-mysql-what-s-wrong"]], "2.3 The error message \u201cWarning: MySQL Connection Failed: Can\u2019t connect to local MySQL server through socket \u2018/tmp/mysql.sock\u2019 (111) \u2026\u201d is displayed. What can I do?": [[6, "the-error-message-warning-mysql-connection-failed-can-t-connect-to-local-mysql-server-through-socket-tmp-mysql-sock-111-is-displayed-what-can-i-do"]], "2.4 Nothing is displayed by my browser when I try to run phpMyAdmin, what can I do?": [[6, "nothing-is-displayed-by-my-browser-when-i-try-to-run-phpmyadmin-what-can-i-do"]], "2.5 Each time I want to insert or change a row or drop a database or a table, an error 404 (page not found) is displayed or, with HTTP or cookie authentication, I\u2019m asked to log in again. What\u2019s wrong?": [[6, "each-time-i-want-to-insert-or-change-a-row-or-drop-a-database-or-a-table-an-error-404-page-not-found-is-displayed-or-with-http-or-cookie-authentication-i-m-asked-to-log-in-again-what-s-wrong"]], "2.6 I get an \u201cAccess denied for user: \u2018root@localhost\u2019 (Using password: YES)\u201d-error when trying to access a MySQL-Server on a host which is port-forwarded for my localhost.": [[6, "i-get-an-access-denied-for-user-root-localhost-using-password-yes-error-when-trying-to-access-a-mysql-server-on-a-host-which-is-port-forwarded-for-my-localhost"]], "2.7 Using and creating themes": [[6, "using-and-creating-themes"]], "2.8 I get \u201cMissing parameters\u201d errors, what can I do?": [[6, "i-get-missing-parameters-errors-what-can-i-do"]], "2.9 Seeing an upload progress bar": [[6, "seeing-an-upload-progress-bar"]], "2.10 How to generate a string of random bytes": [[6, "how-to-generate-a-string-of-random-bytes"]], "Known limitations": [[6, "known-limitations"]], "3.1 When using HTTP authentication, a user who logged out can not log in again in with the same nick.": [[6, "when-using-http-authentication-a-user-who-logged-out-can-not-log-in-again-in-with-the-same-nick"]], "3.2 When dumping a large table in compressed mode, I get a memory limit error or a time limit error.": [[6, "when-dumping-a-large-table-in-compressed-mode-i-get-a-memory-limit-error-or-a-time-limit-error"]], "3.3 With InnoDB tables, I lose foreign key relationships when I rename a table or a column.": [[6, "with-innodb-tables-i-lose-foreign-key-relationships-when-i-rename-a-table-or-a-column"]], "3.4 I am unable to import dumps I created with the mysqldump tool bundled with the MySQL server distribution.": [[6, "i-am-unable-to-import-dumps-i-created-with-the-mysqldump-tool-bundled-with-the-mysql-server-distribution"]], "3.5 When using nested folders, multiple hierarchies are displayed in a wrong manner.": [[6, "when-using-nested-folders-multiple-hierarchies-are-displayed-in-a-wrong-manner"]], "3.6 (withdrawn).": [[6, "faq3-6"]], "3.7 I have table with many (100+) columns and when I try to browse table I get series of errors like \u201cWarning: unable to parse url\u201d. How can this be fixed?": [[6, "i-have-table-with-many-100-columns-and-when-i-try-to-browse-table-i-get-series-of-errors-like-warning-unable-to-parse-url-how-can-this-be-fixed"]], "3.8 I cannot use (clickable) HTML-forms in columns where I put a MIME-Transformation onto!": [[6, "i-cannot-use-clickable-html-forms-in-columns-where-i-put-a-mime-transformation-onto"]], "3.9 I get error messages when using \u201c\u2013sql_mode=ANSI\u201d for the MySQL server.": [[6, "i-get-error-messages-when-using-sql-mode-ansi-for-the-mysql-server"]], "3.10 Homonyms and no primary key: When the results of a SELECT display more that one column with the same value (for example SELECT lastname from employees where firstname like 'A%' and two \u201cSmith\u201d values are displayed), if I click Edit I cannot be sure that I am editing the intended row.": [[6, "homonyms-and-no-primary-key-when-the-results-of-a-select-display-more-that-one-column-with-the-same-value-for-example-select-lastname-from-employees-where-firstname-like-a-and-two-smith-values-are-displayed-if-i-click-edit-i-cannot-be-sure-that-i-am-editing-the-intended-row"]], "3.11 The number of rows for InnoDB tables is not correct.": [[6, "the-number-of-rows-for-innodb-tables-is-not-correct"]], "3.12 (withdrawn).": [[6, "faq3-12"]], "3.13 I get an error when entering USE followed by a db name containing an hyphen.": [[6, "i-get-an-error-when-entering-use-followed-by-a-db-name-containing-an-hyphen"]], "3.14 I am not able to browse a table when I don\u2019t have the right to SELECT one of the columns.": [[6, "i-am-not-able-to-browse-a-table-when-i-don-t-have-the-right-to-select-one-of-the-columns"]], "3.15 (withdrawn).": [[6, "faq3-15"]], "3.16 (withdrawn).": [[6, "faq3-16"]], "3.17 (withdrawn).": [[6, "faq3-17"]], "3.18 When I import a CSV file that contains multiple tables, they are lumped together into a single table.": [[6, "when-i-import-a-csv-file-that-contains-multiple-tables-they-are-lumped-together-into-a-single-table"]], "3.19 When I import a file and have phpMyAdmin determine the appropriate data structure it only uses int, decimal, and varchar types.": [[6, "when-i-import-a-file-and-have-phpmyadmin-determine-the-appropriate-data-structure-it-only-uses-int-decimal-and-varchar-types"]], "3.20 After upgrading, some bookmarks are gone or their content cannot be shown.": [[6, "after-upgrading-some-bookmarks-are-gone-or-their-content-cannot-be-shown"]], "3.21 I am unable to log in with a username containing unicode characters such as \u00e1.": [[6, "i-am-unable-to-log-in-with-a-username-containing-unicode-characters-such-as-a"]], "ISPs, multi-user installations": [[6, "isps-multi-user-installations"]], "4.1 I\u2019m an ISP. Can I setup one central copy of phpMyAdmin or do I need to install it for each customer?": [[6, "i-m-an-isp-can-i-setup-one-central-copy-of-phpmyadmin-or-do-i-need-to-install-it-for-each-customer"]], "4.2 What\u2019s the preferred way of making phpMyAdmin secure against evil access?": [[6, "what-s-the-preferred-way-of-making-phpmyadmin-secure-against-evil-access"]], "4.3 I get errors about not being able to include a file in /lang or in /libraries.": [[6, "i-get-errors-about-not-being-able-to-include-a-file-in-lang-or-in-libraries"]], "4.4 phpMyAdmin always gives \u201cAccess denied\u201d when using HTTP authentication.": [[6, "phpmyadmin-always-gives-access-denied-when-using-http-authentication"]], "4.5 Is it possible to let users create their own databases?": [[6, "is-it-possible-to-let-users-create-their-own-databases"]], "4.6 How can I use the Host-based authentication additions?": [[6, "how-can-i-use-the-host-based-authentication-additions"]], "4.7 Authentication window is displayed more than once, why?": [[6, "authentication-window-is-displayed-more-than-once-why"]], "4.8 Which parameters can I use in the URL that starts phpMyAdmin?": [[6, "which-parameters-can-i-use-in-the-url-that-starts-phpmyadmin"]], "Browsers or client OS": [[6, "browsers-or-client-os"]], "5.1 I get an out of memory error, and my controls are non-functional, when trying to create a table with more than 14 columns.": [[6, "i-get-an-out-of-memory-error-and-my-controls-are-non-functional-when-trying-to-create-a-table-with-more-than-14-columns"]], "5.2 With Xitami 2.5b4, phpMyAdmin won\u2019t process form fields.": [[6, "with-xitami-2-5b4-phpmyadmin-won-t-process-form-fields"]], "5.3 I have problems dumping tables with Konqueror (phpMyAdmin 2.2.2).": [[6, "i-have-problems-dumping-tables-with-konqueror-phpmyadmin-2-2-2"]], "5.4 I can\u2019t use the cookie authentication mode because Internet Explorer never stores the cookies.": [[6, "i-can-t-use-the-cookie-authentication-mode-because-internet-explorer-never-stores-the-cookies"]], "5.5 (withdrawn).": [[6, "faq5-5"]], "5.6 (withdrawn).": [[6, "faq5-6"]], "5.7 I refresh (reload) my browser, and come back to the welcome page.": [[6, "i-refresh-reload-my-browser-and-come-back-to-the-welcome-page"]], "5.8 With Mozilla 0.9.7 I have problems sending a query modified in the query box.": [[6, "with-mozilla-0-9-7-i-have-problems-sending-a-query-modified-in-the-query-box"]], "5.9 With Mozilla 0.9.? to 1.0 and Netscape 7.0-PR1 I can\u2019t type a whitespace in the SQL-Query edit area: the page scrolls down.": [[6, "with-mozilla-0-9-to-1-0-and-netscape-7-0-pr1-i-can-t-type-a-whitespace-in-the-sql-query-edit-area-the-page-scrolls-down"]], "5.10 (withdrawn).": [[6, "faq5-10"]], "5.11 Extended-ASCII characters like German umlauts are displayed wrong.": [[6, "extended-ascii-characters-like-german-umlauts-are-displayed-wrong"]], "5.12 Mac OS X Safari browser changes special characters to \u201c?\u201d.": [[6, "mac-os-x-safari-browser-changes-special-characters-to"]], "5.13 (withdrawn)": [[6, "faq5-13"]], "5.14 (withdrawn)": [[6, "faq5-14"]], "5.15 (withdrawn)": [[6, "faq5-15"]], "5.16 With Internet Explorer, I get \u201cAccess is denied\u201d Javascript errors. Or I cannot make phpMyAdmin work under Windows.": [[6, "with-internet-explorer-i-get-access-is-denied-javascript-errors-or-i-cannot-make-phpmyadmin-work-under-windows"]], "5.17 With Firefox, I cannot delete rows of data or drop a database.": [[6, "with-firefox-i-cannot-delete-rows-of-data-or-drop-a-database"]], "5.18 (withdrawn)": [[6, "faq5-18"]], "5.19 I get JavaScript errors in my browser.": [[6, "i-get-javascript-errors-in-my-browser"]], "5.20 I get errors about violating Content Security Policy.": [[6, "i-get-errors-about-violating-content-security-policy"]], "5.21 I get errors about potentially unsafe operation when browsing table or executing SQL query.": [[6, "i-get-errors-about-potentially-unsafe-operation-when-browsing-table-or-executing-sql-query"]], "Using phpMyAdmin": [[6, "using-phpmyadmin"]], "6.1 I can\u2019t insert new rows into a table / I can\u2019t create a table - MySQL brings up a SQL error.": [[6, "i-can-t-insert-new-rows-into-a-table-i-can-t-create-a-table-mysql-brings-up-a-sql-error"]], "6.2 When I create a table, I set an index for two columns and phpMyAdmin generates only one index with those two columns.": [[6, "when-i-create-a-table-i-set-an-index-for-two-columns-and-phpmyadmin-generates-only-one-index-with-those-two-columns"]], "6.3 How can I insert a null value into my table?": [[6, "how-can-i-insert-a-null-value-into-my-table"]], "6.4 How can I backup my database or table?": [[6, "how-can-i-backup-my-database-or-table"]], "6.5 How can I restore (upload) my database or table using a dump? How can I run a \u201c.sql\u201d file?": [[6, "how-can-i-restore-upload-my-database-or-table-using-a-dump-how-can-i-run-a-sql-file"]], "6.6 How can I use the relation table in Query-by-example?": [[6, "how-can-i-use-the-relation-table-in-query-by-example"]], "6.7 How can I use the \u201cdisplay column\u201d feature?": [[6, "how-can-i-use-the-display-column-feature"]], "6.8 How can I produce a PDF schema of my database?": [[6, "how-can-i-produce-a-pdf-schema-of-my-database"]], "6.9 phpMyAdmin is changing the type of one of my columns!": [[6, "phpmyadmin-is-changing-the-type-of-one-of-my-columns"]], "6.10 When creating a privilege, what happens with underscores in the database name?": [[6, "when-creating-a-privilege-what-happens-with-underscores-in-the-database-name"]], "6.11 What is the curious symbol \u00f8 in the statistics pages?": [[6, "what-is-the-curious-symbol-o-in-the-statistics-pages"]], "6.12 I want to understand some Export options.": [[6, "i-want-to-understand-some-export-options"]], "6.13 I would like to create a database with a dot in its name.": [[6, "i-would-like-to-create-a-database-with-a-dot-in-its-name"]], "6.14 (withdrawn).": [[6, "faqsqlvalidator"]], "6.15 I want to add a BLOB column and put an index on it, but MySQL says \u201cBLOB column \u2018\u2026\u2019 used in key specification without a key length\u201d.": [[6, "i-want-to-add-a-blob-column-and-put-an-index-on-it-but-mysql-says-blob-column-used-in-key-specification-without-a-key-length"]], "6.16 How can I simply move in page with plenty editing fields?": [[6, "how-can-i-simply-move-in-page-with-plenty-editing-fields"]], "6.17 Transformations: I can\u2019t enter my own mimetype! What is this feature then useful for?": [[6, "transformations-i-can-t-enter-my-own-mimetype-what-is-this-feature-then-useful-for"]], "6.18 Bookmarks: Where can I store bookmarks? Why can\u2019t I see any bookmarks below the query box? What are these variables for?": [[6, "bookmarks-where-can-i-store-bookmarks-why-can-t-i-see-any-bookmarks-below-the-query-box-what-are-these-variables-for"]], "6.19 How can I create simple LATEX document to include exported table?": [[6, "how-can-i-create-simple-latex-document-to-include-exported-table"]], "6.20 I see a lot of databases which are not mine, and cannot access them.": [[6, "i-see-a-lot-of-databases-which-are-not-mine-and-cannot-access-them"]], "6.21 In edit/insert mode, how can I see a list of possible values for a column, based on some foreign table?": [[6, "in-edit-insert-mode-how-can-i-see-a-list-of-possible-values-for-a-column-based-on-some-foreign-table"]], "6.22 Bookmarks: Can I execute a default bookmark automatically when entering Browse mode for a table?": [[6, "bookmarks-can-i-execute-a-default-bookmark-automatically-when-entering-browse-mode-for-a-table"]], "6.23 Export: I heard phpMyAdmin can export Microsoft Excel files?": [[6, "export-i-heard-phpmyadmin-can-export-microsoft-excel-files"]], "6.24 Now that phpMyAdmin supports native MySQL 4.1.x column comments, what happens to my column comments stored in pmadb?": [[6, "now-that-phpmyadmin-supports-native-mysql-4-1-x-column-comments-what-happens-to-my-column-comments-stored-in-pmadb"]], "6.25 (withdrawn).": [[6, "faq6-25"]], "6.26 How can I select a range of rows?": [[6, "how-can-i-select-a-range-of-rows"]], "6.27 What format strings can I use?": [[6, "what-format-strings-can-i-use"]], "6.28 (withdrawn).": [[6, "faq6-28"]], "6.29 Why can\u2019t I get a chart from my query result table?": [[6, "why-can-t-i-get-a-chart-from-my-query-result-table"]], "6.30 Import: How can I import ESRI Shapefiles?": [[6, "import-how-can-i-import-esri-shapefiles"]], "6.31 How do I create a relation in designer?": [[6, "how-do-i-create-a-relation-in-designer"]], "6.32 How can I use the zoom search feature?": [[6, "how-can-i-use-the-zoom-search-feature"]], "6.33 When browsing a table, how can I copy a column name?": [[6, "when-browsing-a-table-how-can-i-copy-a-column-name"]], "6.34 How can I use the Favorite Tables feature?": [[6, "how-can-i-use-the-favorite-tables-feature"]], "6.35 How can I use the Range search feature?": [[6, "how-can-i-use-the-range-search-feature"]], "6.36 What is Central columns and how can I use this feature?": [[6, "what-is-central-columns-and-how-can-i-use-this-feature"]], "6.37 How can I use Improve Table structure feature?": [[6, "how-can-i-use-improve-table-structure-feature"]], "6.38 How can I reassign auto-incremented values?": [[6, "how-can-i-reassign-auto-incremented-values"]], "6.39 What is the \u201cAdjust privileges\u201d option when renaming, copying, or moving a database, table, column, or procedure?": [[6, "what-is-the-adjust-privileges-option-when-renaming-copying-or-moving-a-database-table-column-or-procedure"]], "6.40 I see \u201cBind parameters\u201d checkbox in the \u201cSQL\u201d page. How do I write parameterized SQL queries?": [[6, "i-see-bind-parameters-checkbox-in-the-sql-page-how-do-i-write-parameterized-sql-queries"]], "6.41 I get import errors while importing the dumps exported from older MySQL versions (pre-5.7.6) into newer MySQL versions (5.7.7+), but they work fine when imported back on same older versions ?": [[6, "i-get-import-errors-while-importing-the-dumps-exported-from-older-mysql-versions-pre-5-7-6-into-newer-mysql-versions-5-7-7-but-they-work-fine-when-imported-back-on-same-older-versions"]], "phpMyAdmin project": [[6, "phpmyadmin-project"]], "7.1 I have found a bug. How do I inform developers?": [[6, "i-have-found-a-bug-how-do-i-inform-developers"]], "7.2 I want to translate the messages to a new language or upgrade an existing language, where do I start?": [[6, "i-want-to-translate-the-messages-to-a-new-language-or-upgrade-an-existing-language-where-do-i-start"]], "7.3 I would like to help out with the development of phpMyAdmin. How should I proceed?": [[6, "i-would-like-to-help-out-with-the-development-of-phpmyadmin-how-should-i-proceed"]], "Security": [[6, "security"]], "8.1 Where can I get information about the security alerts issued for phpMyAdmin?": [[6, "where-can-i-get-information-about-the-security-alerts-issued-for-phpmyadmin"]], "8.2 How can I protect phpMyAdmin against brute force attacks?": [[6, "how-can-i-protect-phpmyadmin-against-brute-force-attacks"]], "8.3 Why are there path disclosures when directly loading certain files?": [[6, "why-are-there-path-disclosures-when-directly-loading-certain-files"]], "8.4 CSV files exported from phpMyAdmin could allow a formula injection attack.": [[6, "csv-files-exported-from-phpmyadmin-could-allow-a-formula-injection-attack"]], "Synchronization": [[6, "synchronization"]], "9.1 (withdrawn).": [[6, "faq9-1"]], "9.2 (withdrawn).": [[6, "faq9-2"]], "Glossary": [[7, "glossary"]], "Import and export": [[8, "import-and-export"]], "Import": [[8, "import"]], "CSV": [[8, "csv"], [8, "id1"]], "CSV using LOAD DATA": [[8, "csv-using-load-data"]], "ESRI Shape File": [[8, "esri-shape-file"]], "MediaWiki": [[8, "mediawiki"], [8, "id2"]], "Open Document Spreadsheet (ODS)": [[8, "open-document-spreadsheet-ods"]], "Table name": [[8, "table-name"]], "Column names": [[8, "column-names"]], "SQL": [[8, "sql"], [8, "id3"]], "XML": [[8, "xml"], [8, "id5"]], "Export": [[8, "export"]], "CodeGen": [[8, "codegen"]], "CSV for Microsoft Excel": [[8, "csv-for-microsoft-excel"]], "Microsoft Word 2000": [[8, "microsoft-word-2000"]], "JSON": [[8, "json"]], "LaTeX": [[8, "latex"]], "OpenDocument Spreadsheet": [[8, "opendocument-spreadsheet"]], "OpenDocument Text": [[8, "opendocument-text"]], "PDF": [[8, "pdf"]], "PHP Array": [[8, "php-array"]], "Data Options": [[8, "data-options"]], "Texy!": [[8, "texy"]], "YAML": [[8, "yaml"]], "Welcome to phpMyAdmin\u2019s documentation!": [[9, "welcome-to-phpmyadmin-s-documentation"]], "Indices and tables": [[9, "indices-and-tables"]], "Introduction": [[10, "introduction"], [19, "introduction"]], "Supported features": [[10, "supported-features"]], "Shortcut keys": [[10, "shortcut-keys"]], "A word about users": [[10, "a-word-about-users"]], "Other sources of information": [[11, "other-sources-of-information"]], "Printed Book": [[11, "printed-book"]], "Tutorials": [[11, "tutorials"]], "\u010cesky (Czech)": [[11, "cesky-czech"]], "English": [[11, "english"]], "\u0420\u0443\u0441\u0441\u043a\u0438\u0439 (Russian)": [[11, "russian"]], "User management": [[12, "user-management"]], "Creating a new user": [[12, "creating-a-new-user"]], "Editing an existing user": [[12, "editing-an-existing-user"]], "Deleting a user": [[12, "deleting-a-user"]], "Assigning privileges to user for a specific database": [[12, "assigning-privileges-to-user-for-a-specific-database"]], "Configurable menus and user groups": [[12, "configurable-menus-and-user-groups"]], "Relations": [[13, "relations"]], "Technical info": [[13, "technical-info"]], "Relation view": [[13, "relation-view"]], "Relation view example": [[13, "relation-view-example"]], "Designer": [[13, "designer"]], "Requirements": [[14, "requirements"]], "Web server": [[14, "web-server"]], "PHP": [[14, "php"]], "Database": [[14, "database"]], "Web browser": [[14, "web-browser"]], "Security policy": [[15, "security-policy"]], "Typical vulnerabilities": [[15, "typical-vulnerabilities"]], "Cross-site scripting (XSS)": [[15, "cross-site-scripting-xss"]], "Cross-site request forgery (CSRF)": [[15, "cross-site-request-forgery-csrf"]], "SQL injection": [[15, "sql-injection"]], "Brute force attack": [[15, "brute-force-attack"]], "Reporting security issues": [[15, "reporting-security-issues"]], "Configuring phpMyAdmin": [[16, "configuring-phpmyadmin"]], "Installation": [[17, "installation"]], "Linux distributions": [[17, "linux-distributions"]], "Debian and Ubuntu": [[17, "debian-and-ubuntu"]], "OpenSUSE": [[17, "opensuse"]], "Gentoo": [[17, "gentoo"]], "Mandriva": [[17, "mandriva"]], "Fedora": [[17, "fedora"]], "Red Hat Enterprise Linux": [[17, "red-hat-enterprise-linux"]], "Installing on Windows": [[17, "installing-on-windows"]], "Installing from Git": [[17, "installing-from-git"]], "Installing using Composer": [[17, "installing-using-composer"]], "Installing using Docker": [[17, "installing-using-docker"]], "Docker environment variables": [[17, "docker-environment-variables"]], "Customizing configuration": [[17, "customizing-configuration"]], "Docker Volumes": [[17, "docker-volumes"]], "Docker Examples": [[17, "docker-examples"]], "Using docker-compose": [[17, "using-docker-compose"]], "Customizing configuration file using docker-compose": [[17, "customizing-configuration-file-using-docker-compose"]], "Running behind haproxy in a subdirectory": [[17, "running-behind-haproxy-in-a-subdirectory"]], "IBM Cloud": [[17, "ibm-cloud"]], "Quick Install": [[17, "quick-install"]], "Manually creating the file": [[17, "manually-creating-the-file"]], "Using the Setup script": [[17, "using-the-setup-script"]], "Setup script on Debian, Ubuntu and derivatives": [[17, "setup-script-on-debian-ubuntu-and-derivatives"]], "Setup script on openSUSE": [[17, "setup-script-on-opensuse"]], "Verifying phpMyAdmin releases": [[17, "verifying-phpmyadmin-releases"]], "phpMyAdmin configuration storage": [[17, "phpmyadmin-configuration-storage"]], "Zero configuration": [[17, "zero-configuration"]], "Manual configuration": [[17, "manual-configuration"]], "Upgrading from an older version": [[17, "upgrading-from-an-older-version"]], "Using authentication modes": [[17, "using-authentication-modes"]], "HTTP authentication mode": [[17, "http-authentication-mode"]], "Cookie authentication mode": [[17, "cookie-authentication-mode"]], "Signon authentication mode": [[17, "signon-authentication-mode"]], "Config authentication mode": [[17, "config-authentication-mode"]], "Securing your phpMyAdmin installation": [[17, "securing-your-phpmyadmin-installation"]], "Using SSL for connection to database server": [[17, "using-ssl-for-connection-to-database-server"]], "Known issues": [[17, "known-issues"]], "Users with column-specific privileges are unable to \u201cBrowse\u201d": [[17, "users-with-column-specific-privileges-are-unable-to-browse"]], "Trouble logging back in after logging out using \u2018http\u2019 authentication": [[17, "trouble-logging-back-in-after-logging-out-using-http-authentication"]], "Custom Themes": [[18, "custom-themes"]], "Creating custom theme": [[18, "creating-custom-theme"]], "Theme metadata": [[18, "theme-metadata"]], "Sharing images": [[18, "sharing-images"]], "Transformations": [[19, "transformations"]], "Usage": [[19, "usage"]], "File structure": [[19, "file-structure"]], "Two-factor authentication": [[20, "two-factor-authentication"]], "Authentication Application (2FA)": [[20, "authentication-application-2fa"]], "Hardware Security Key (FIDO U2F)": [[20, "hardware-security-key-fido-u2f"]], "Simple two-factor authentication": [[20, "simple-two-factor-authentication"]], "User Guide": [[21, "user-guide"]], "Distributing and packaging phpMyAdmin": [[22, "distributing-and-packaging-phpmyadmin"]], "External libraries": [[22, "external-libraries"]], "Specific files LICENSES": [[22, "specific-files-licenses"]], "Licenses for vendors": [[22, "licenses-for-vendors"]]}, "indexentries": {"$cfg['actionlinksmode']": [[2, "cfg_ActionLinksMode"]], "$cfg['allowarbitraryserver']": [[2, "cfg_AllowArbitraryServer"], [2, "index-142"], [17, "index-0"], [17, "index-44"]], "$cfg['allowthirdpartyframing']": [[2, "cfg_AllowThirdPartyFraming"]], "$cfg['allowuserdropdatabase']": [[2, "cfg_AllowUserDropDatabase"]], "$cfg['arbitraryserverregexp']": [[2, "cfg_ArbitraryServerRegexp"], [2, "index-141"]], "$cfg['authlog']": [[2, "cfg_AuthLog"], [2, "index-4"], [17, "index-66"]], "$cfg['authlogsuccess']": [[2, "cfg_AuthLogSuccess"], [2, "index-2"]], "$cfg['availablecharsets']": [[2, "cfg_AvailableCharsets"]], "$cfg['bzipdump']": [[2, "cfg_BZipDump"]], "$cfg['browsemime']": [[2, "cfg_BrowseMIME"]], "$cfg['browsemarkerenable']": [[2, "cfg_BrowseMarkerEnable"]], "$cfg['browsepointerenable']": [[2, "cfg_BrowsePointerEnable"]], "$cfg['cspallow']": [[2, "cfg_CSPAllow"], [2, "index-147"]], "$cfg['captchaapi']": [[2, "cfg_CaptchaApi"]], "$cfg['captchacsp']": [[2, "cfg_CaptchaCsp"]], "$cfg['captchaloginprivatekey']": [[2, "cfg_CaptchaLoginPrivateKey"], [17, "index-65"]], "$cfg['captchaloginpublickey']": [[2, "cfg_CaptchaLoginPublicKey"], [17, "index-64"]], "$cfg['captchamethod']": [[2, "cfg_CaptchaMethod"]], "$cfg['captcharequestparam']": [[2, "cfg_CaptchaRequestParam"]], "$cfg['captcharesponseparam']": [[2, "cfg_CaptchaResponseParam"]], "$cfg['captchasiteverifyurl']": [[2, "cfg_CaptchaSiteVerifyURL"]], "$cfg['charediting']": [[2, "cfg_CharEditing"], [2, "index-155"]], "$cfg['chartextareacols']": [[2, "cfg_CharTextareaCols"]], "$cfg['chartextarearows']": [[2, "cfg_CharTextareaRows"]], "$cfg['checkconfigurationpermissions']": [[2, "cfg_CheckConfigurationPermissions"]], "$cfg['codemirrorenable']": [[2, "cfg_CodemirrorEnable"]], "$cfg['compressonfly']": [[2, "cfg_CompressOnFly"], [6, "index-12"]], "$cfg['confirm']": [[2, "cfg_Confirm"]], "$cfg['console']['alwaysexpand']": [[2, "cfg_Console_AlwaysExpand"]], "$cfg['console']['currentquery']": [[2, "cfg_Console_CurrentQuery"]], "$cfg['console']['darktheme']": [[2, "cfg_Console_DarkTheme"]], "$cfg['console']['enterexecutes']": [[2, "cfg_Console_EnterExecutes"]], "$cfg['console']['height']": [[2, "cfg_Console_Height"]], "$cfg['console']['mode']": [[2, "cfg_Console_Mode"]], "$cfg['console']['starthistory']": [[2, "cfg_Console_StartHistory"]], "$cfg['consoleenterexecutes']": [[2, "cfg_ConsoleEnterExecutes"]], "$cfg['cookiesamesite']": [[2, "cfg_CookieSameSite"]], "$cfg['dbg']": [[2, "cfg_DBG"]], "$cfg['dbg']['demo']": [[2, "cfg_DBG_demo"]], "$cfg['dbg']['simple2fa']": [[2, "cfg_DBG_simple2fa"], [20, "index-0"]], "$cfg['dbg']['sql']": [[2, "cfg_DBG_sql"], [2, "index-160"]], "$cfg['dbg']['sqllog']": [[2, "cfg_DBG_sqllog"]], "$cfg['defaultconnectioncollation']": [[2, "cfg_DefaultConnectionCollation"]], "$cfg['defaultforeignkeychecks']": [[2, "cfg_DefaultForeignKeyChecks"]], "$cfg['defaultfunctions']": [[2, "cfg_DefaultFunctions"]], "$cfg['defaultlang']": [[2, "cfg_DefaultLang"]], "$cfg['defaultquerydatabase']": [[2, "cfg_DefaultQueryDatabase"]], "$cfg['defaultquerytable']": [[2, "cfg_DefaultQueryTable"]], "$cfg['defaulttabdatabase']": [[2, "cfg_DefaultTabDatabase"]], "$cfg['defaulttabserver']": [[2, "cfg_DefaultTabServer"]], "$cfg['defaulttabtable']": [[2, "cfg_DefaultTabTable"]], "$cfg['defaulttransformations']": [[2, "cfg_DefaultTransformations"], [19, "index-0"]], "$cfg['defaulttransformations']['bool2text']": [[2, "cfg_DefaultTransformations_Bool2Text"]], "$cfg['defaulttransformations']['dateformat']": [[2, "cfg_DefaultTransformations_DateFormat"]], "$cfg['defaulttransformations']['external']": [[2, "cfg_DefaultTransformations_External"]], "$cfg['defaulttransformations']['hex']": [[2, "cfg_DefaultTransformations_Hex"]], "$cfg['defaulttransformations']['inline']": [[2, "cfg_DefaultTransformations_Inline"]], "$cfg['defaulttransformations']['preappend']": [[2, "cfg_DefaultTransformations_PreApPend"]], "$cfg['defaulttransformations']['substring']": [[2, "cfg_DefaultTransformations_Substring"]], "$cfg['defaulttransformations']['textimagelink']": [[2, "cfg_DefaultTransformations_TextImageLink"]], "$cfg['defaulttransformations']['textlink']": [[2, "cfg_DefaultTransformations_TextLink"]], "$cfg['disablemultitablemaintenance']": [[2, "cfg_DisableMultiTableMaintenance"]], "$cfg['disableshortcutkeys']": [[2, "cfg_DisableShortcutKeys"], [2, "index-134"]], "$cfg['displaybinaryashex']": [[2, "cfg_DisplayBinaryAsHex"]], "$cfg['displayserverslist']": [[2, "cfg_DisplayServersList"]], "$cfg['editinwindow']": [[2, "cfg_EditInWindow"]], "$cfg['enableautocompletefortablesandcolumns']": [[2, "cfg_EnableAutocompleteForTablesAndColumns"]], "$cfg['exectimelimit']": [[2, "cfg_ExecTimeLimit"], [6, "index-21"], [17, "index-18"]], "$cfg['export']": [[2, "cfg_Export"]], "$cfg['export']['charset']": [[2, "cfg_Export_charset"], [2, "index-151"]], "$cfg['export']['compression']": [[2, "cfg_Export_compression"]], "$cfg['export']['file_template_database']": [[2, "cfg_Export_file_template_database"]], "$cfg['export']['file_template_server']": [[2, "cfg_Export_file_template_server"]], "$cfg['export']['file_template_table']": [[2, "cfg_Export_file_template_table"]], "$cfg['export']['format']": [[2, "cfg_Export_format"]], "$cfg['export']['method']": [[2, "cfg_Export_method"]], "$cfg['export']['remove_definer_from_definitions']": [[2, "cfg_Export_remove_definer_from_definitions"]], "$cfg['filterlanguages']": [[2, "cfg_FilterLanguages"]], "$cfg['firstdayofcalendar']": [[2, "cfg_FirstDayOfCalendar"]], "$cfg['firstlevelnavigationitems']": [[2, "cfg_FirstLevelNavigationItems"]], "$cfg['fontsize']": [[2, "cfg_FontSize"]], "$cfg['forcessl']": [[2, "cfg_ForceSSL"]], "$cfg['foreignkeydropdownorder']": [[2, "cfg_ForeignKeyDropdownOrder"], [2, "index-149"]], "$cfg['foreignkeymaxlimit']": [[2, "cfg_ForeignKeyMaxLimit"], [6, "index-22"]], "$cfg['gd2available']": [[2, "cfg_GD2Available"]], "$cfg['gzipdump']": [[2, "cfg_GZipDump"]], "$cfg['gridediting']": [[2, "cfg_GridEditing"]], "$cfg['hidestructureactions']": [[2, "cfg_HideStructureActions"]], "$cfg['iconvextraparams']": [[2, "cfg_IconvExtraParams"]], "$cfg['ignoremultisubmiterrors']": [[2, "cfg_IgnoreMultiSubmitErrors"]], "$cfg['import']": [[2, "cfg_Import"]], "$cfg['import']['charset']": [[2, "cfg_Import_charset"], [2, "index-152"]], "$cfg['initialslidersstate']": [[2, "cfg_InitialSlidersState"]], "$cfg['insertrows']": [[2, "cfg_InsertRows"]], "$cfg['lang']": [[2, "cfg_Lang"]], "$cfg['limitchars']": [[2, "cfg_LimitChars"]], "$cfg['linklengthlimit']": [[2, "cfg_LinkLengthLimit"]], "$cfg['lintenable']": [[2, "cfg_LintEnable"]], "$cfg['logincookiedeleteall']": [[2, "cfg_LoginCookieDeleteAll"]], "$cfg['logincookierecall']": [[2, "cfg_LoginCookieRecall"]], "$cfg['logincookiestore']": [[2, "cfg_LoginCookieStore"]], "$cfg['logincookievalidity']": [[2, "cfg_LoginCookieValidity"], [2, "index-140"]], "$cfg['logincookievaliditydisablewarning']": [[2, "cfg_LoginCookieValidityDisableWarning"]], "$cfg['longtextdoubletextarea']": [[2, "cfg_LongtextDoubleTextarea"]], "$cfg['maxcharactersindisplayedsql']": [[2, "cfg_MaxCharactersInDisplayedSQL"]], "$cfg['maxdblist']": [[2, "cfg_MaxDbList"]], "$cfg['maxexactcount']": [[2, "cfg_MaxExactCount"], [6, "index-14"], [6, "index-15"]], "$cfg['maxexactcountviews']": [[2, "cfg_MaxExactCountViews"]], "$cfg['maxnavigationitems']": [[2, "cfg_MaxNavigationItems"]], "$cfg['maxrows']": [[2, "cfg_MaxRows"]], "$cfg['maxsizeforinputfield']": [[2, "cfg_MaxSizeForInputField"]], "$cfg['maxtablelist']": [[2, "cfg_MaxTableList"]], "$cfg['memorylimit']": [[2, "cfg_MemoryLimit"], [17, "index-17"]], "$cfg['minsizeforinputfield']": [[2, "cfg_MinSizeForInputField"]], "$cfg['mysqlminversion']": [[2, "cfg_MysqlMinVersion"]], "$cfg['mysqlsslwarningsafehosts']": [[2, "cfg_MysqlSslWarningSafeHosts"], [17, "index-74"]], "$cfg['naturalorder']": [[2, "cfg_NaturalOrder"]], "$cfg['navigationdisplaylogo']": [[2, "cfg_NavigationDisplayLogo"]], "$cfg['navigationdisplayservers']": [[2, "cfg_NavigationDisplayServers"]], "$cfg['navigationlinkwithmainpanel']": [[2, "cfg_NavigationLinkWithMainPanel"]], "$cfg['navigationlogolink']": [[2, "cfg_NavigationLogoLink"], [2, "index-146"]], "$cfg['navigationlogolinkwindow']": [[2, "cfg_NavigationLogoLinkWindow"], [2, "index-145"]], "$cfg['navigationtreeautoexpandsingledb']": [[2, "cfg_NavigationTreeAutoexpandSingleDb"]], "$cfg['navigationtreedbseparator']": [[2, "cfg_NavigationTreeDbSeparator"], [2, "index-143"]], "$cfg['navigationtreedefaulttabtable']": [[2, "cfg_NavigationTreeDefaultTabTable"], [2, "index-101"], [2, "index-98"]], "$cfg['navigationtreedefaulttabtable2']": [[2, "cfg_NavigationTreeDefaultTabTable2"]], "$cfg['navigationtreedisplaydbfilterminimum']": [[2, "cfg_NavigationTreeDisplayDbFilterMinimum"]], "$cfg['navigationtreedisplayitemfilterminimum']": [[2, "cfg_NavigationTreeDisplayItemFilterMinimum"]], "$cfg['navigationtreeenableexpansion']": [[2, "cfg_NavigationTreeEnableExpansion"]], "$cfg['navigationtreeenablegrouping']": [[2, "cfg_NavigationTreeEnableGrouping"]], "$cfg['navigationtreepointerenable']": [[2, "cfg_NavigationTreePointerEnable"]], "$cfg['navigationtreeshowevents']": [[2, "cfg_NavigationTreeShowEvents"]], "$cfg['navigationtreeshowfunctions']": [[2, "cfg_NavigationTreeShowFunctions"]], "$cfg['navigationtreeshowprocedures']": [[2, "cfg_NavigationTreeShowProcedures"]], "$cfg['navigationtreeshowtables']": [[2, "cfg_NavigationTreeShowTables"]], "$cfg['navigationtreeshowviews']": [[2, "cfg_NavigationTreeShowViews"]], "$cfg['navigationtreetablelevel']": [[2, "cfg_NavigationTreeTableLevel"]], "$cfg['navigationtreetableseparator']": [[2, "cfg_NavigationTreeTableSeparator"], [6, "index-13"]], "$cfg['navigationwidth']": [[2, "cfg_NavigationWidth"]], "$cfg['numfavoritetables']": [[2, "cfg_NumFavoriteTables"], [2, "index-102"], [6, "index-26"]], "$cfg['numrecenttables']": [[2, "cfg_NumRecentTables"], [2, "index-97"]], "$cfg['obgzip']": [[2, "cfg_OBGzip"], [6, "index-0"], [6, "index-3"], [6, "index-8"]], "$cfg['order']": [[2, "cfg_Order"]], "$cfg['pdfdefaultpagesize']": [[2, "cfg_PDFDefaultPageSize"]], "$cfg['pdfpagesizes']": [[2, "cfg_PDFPageSizes"], [2, "index-150"]], "$cfg['persistentconnections']": [[2, "cfg_PersistentConnections"]], "$cfg['pmaabsoluteuri']": [[2, "cfg_PmaAbsoluteUri"], [2, "index-137"], [6, "index-10"], [6, "index-19"], [6, "index-20"], [6, "index-6"], [6, "index-9"], [17, "index-7"]], "$cfg['pmanorelation_disablewarning']": [[2, "cfg_PmaNoRelation_DisableWarning"]], "$cfg['propertiesnumcolumns']": [[2, "cfg_PropertiesNumColumns"]], "$cfg['protectbinary']": [[2, "cfg_ProtectBinary"]], "$cfg['proxypass']": [[2, "cfg_ProxyPass"]], "$cfg['proxyurl']": [[2, "cfg_ProxyUrl"]], "$cfg['proxyuser']": [[2, "cfg_ProxyUser"]], "$cfg['queryhistorydb']": [[2, "cfg_QueryHistoryDB"], [2, "index-156"], [2, "index-158"], [17, "index-10"]], "$cfg['queryhistorymax']": [[2, "cfg_QueryHistoryMax"], [2, "index-159"], [2, "index-94"], [17, "index-11"]], "$cfg['querywindowdeftab']": [[2, "cfg_QueryWindowDefTab"]], "$cfg['querywindowheight']": [[2, "cfg_QueryWindowHeight"]], "$cfg['querywindowwidth']": [[2, "cfg_QueryWindowWidth"]], "$cfg['recodingengine']": [[2, "cfg_RecodingEngine"]], "$cfg['relationaldisplay']": [[2, "cfg_RelationalDisplay"]], "$cfg['remembersorting']": [[2, "cfg_RememberSorting"], [2, "index-105"]], "$cfg['repeatcells']": [[2, "cfg_RepeatCells"]], "$cfg['reservedworddisablewarning']": [[2, "cfg_ReservedWordDisableWarning"]], "$cfg['retainquerybox']": [[2, "cfg_RetainQueryBox"]], "$cfg['rowactionlinks']": [[2, "cfg_RowActionLinks"]], "$cfg['rowactionlinkswithoutunique']": [[2, "cfg_RowActionLinksWithoutUnique"]], "$cfg['rowactiontype']": [[2, "cfg_RowActionType"]], "$cfg['sqlquery']['edit']": [[2, "cfg_SQLQuery_Edit"]], "$cfg['sqlquery']['explain']": [[2, "cfg_SQLQuery_Explain"]], "$cfg['sqlquery']['refresh']": [[2, "cfg_SQLQuery_Refresh"]], "$cfg['sqlquery']['showasphp']": [[2, "cfg_SQLQuery_ShowAsPHP"]], "$cfg['savecellsatonce']": [[2, "cfg_SaveCellsAtOnce"]], "$cfg['savedir']": [[2, "cfg_SaveDir"], [8, "index-1"], [17, "index-20"]], "$cfg['schema']": [[2, "cfg_Schema"]], "$cfg['schema']['format']": [[2, "cfg_Schema_format"]], "$cfg['senderrorreports']": [[2, "cfg_SendErrorReports"]], "$cfg['serverdefault']": [[2, "cfg_ServerDefault"], [2, "index-135"], [2, "index-136"]], "$cfg['serverlibrarydifference_disablewarning']": [[2, "cfg_ServerLibraryDifference_DisableWarning"]], "$cfg['servers']": [[2, "cfg_Servers"], [2, "index-161"], [2, "index-6"], [2, "index-9"], [17, "index-41"]], "$cfg['servers'][$i]['allowdeny']['order']": [[2, "cfg_Servers_AllowDeny_order"], [2, "index-153"], [17, "index-59"]], "$cfg['servers'][$i]['allowdeny']['rules']": [[2, "cfg_Servers_AllowDeny_rules"], [2, "index-130"], [2, "index-154"], [6, "index-18"], [17, "index-60"], [17, "index-62"]], "$cfg['servers'][$i]['allownopassword']": [[2, "cfg_Servers_AllowNoPassword"], [2, "index-76"]], "$cfg['servers'][$i]['allowroot']": [[2, "cfg_Servers_AllowRoot"], [17, "index-63"]], "$cfg['servers'][$i]['disableis']": [[2, "cfg_Servers_DisableIS"]], "$cfg['servers'][$i]['logouturl']": [[2, "cfg_Servers_LogoutURL"]], "$cfg['servers'][$i]['maxtableuiprefs']": [[2, "cfg_Servers_MaxTableUiprefs"]], "$cfg['servers'][$i]['sessiontimezone']": [[2, "cfg_Servers_SessionTimeZone"], [17, "index-30"]], "$cfg['servers'][$i]['signoncookieparams']": [[2, "cfg_Servers_SignonCookieParams"], [17, "index-47"], [17, "index-53"]], "$cfg['servers'][$i]['signonscript']": [[2, "cfg_Servers_SignonScript"], [2, "index-132"], [2, "index-133"], [17, "index-48"], [17, "index-50"], [17, "index-54"]], "$cfg['servers'][$i]['signonsession']": [[2, "cfg_Servers_SignonSession"], [17, "index-46"], [17, "index-52"]], "$cfg['servers'][$i]['signonurl']": [[2, "cfg_Servers_SignonURL"], [17, "index-49"], [17, "index-55"]], "$cfg['servers'][$i]['auth_http_realm']": [[2, "cfg_Servers_auth_http_realm"]], "$cfg['servers'][$i]['auth_swekey_config']": [[2, "cfg_Servers_auth_swekey_config"]], "$cfg['servers'][$i]['auth_type']": [[2, "cfg_Servers_auth_type"], [2, "index-75"], [17, "index-51"]], "$cfg['servers'][$i]['bookmarktable']": [[2, "cfg_Servers_bookmarktable"], [2, "index-79"]], "$cfg['servers'][$i]['central_columns']": [[2, "cfg_Servers_central_columns"], [2, "index-115"]], "$cfg['servers'][$i]['column_info']": [[2, "cfg_Servers_column_info"], [2, "index-91"], [2, "index-92"]], "$cfg['servers'][$i]['compress']": [[2, "cfg_Servers_compress"]], "$cfg['servers'][$i]['connect_type']": [[2, "cfg_Servers_connect_type"]], "$cfg['servers'][$i]['control_*']": [[2, "cfg_Servers_control_*"], [2, "index-60"], [2, "index-61"], [2, "index-65"]], "$cfg['servers'][$i]['controlhost']": [[2, "cfg_Servers_controlhost"], [2, "index-63"], [17, "index-12"]], "$cfg['servers'][$i]['controlpass']": [[2, "cfg_Servers_controlpass"], [6, "index-17"], [17, "index-14"], [17, "index-38"]], "$cfg['servers'][$i]['controlport']": [[2, "cfg_Servers_controlport"], [2, "index-64"], [17, "index-15"]], "$cfg['servers'][$i]['controluser']": [[2, "cfg_Servers_controluser"], [2, "index-93"], [6, "index-16"], [17, "index-13"], [17, "index-37"], [17, "index-40"]], "$cfg['servers'][$i]['designer_coords']": [[2, "cfg_Servers_designer_coords"], [2, "index-89"]], "$cfg['servers'][$i]['designer_settings']": [[2, "cfg_Servers_designer_settings"], [2, "index-117"]], "$cfg['servers'][$i]['export_templates']": [[2, "cfg_Servers_export_templates"], [2, "index-121"]], "$cfg['servers'][$i]['extension']": [[2, "cfg_Servers_extension"]], "$cfg['servers'][$i]['favorite']": [[2, "cfg_Servers_favorite"], [2, "index-104"]], "$cfg['servers'][$i]['hide_connection_errors']": [[2, "cfg_Servers_hide_connection_errors"]], "$cfg['servers'][$i]['hide_db']": [[2, "cfg_Servers_hide_db"]], "$cfg['servers'][$i]['history']": [[2, "cfg_Servers_history"], [2, "index-157"], [2, "index-96"], [17, "index-9"]], "$cfg['servers'][$i]['host']": [[2, "cfg_Servers_host"], [2, "index-12"], [2, "index-13"], [2, "index-138"], [2, "index-14"], [2, "index-15"], [2, "index-59"], [2, "index-7"], [2, "index-74"], [2, "index-8"], [17, "index-1"]], "$cfg['servers'][$i]['navigationhiding']": [[2, "cfg_Servers_navigationhiding"], [2, "index-113"]], "$cfg['servers'][$i]['nopassword']": [[2, "cfg_Servers_nopassword"]], "$cfg['servers'][$i]['only_db']": [[2, "cfg_Servers_only_db"]], "$cfg['servers'][$i]['password']": [[2, "cfg_Servers_password"], [17, "index-58"]], "$cfg['servers'][$i]['pdf_pages']": [[2, "cfg_Servers_pdf_pages"], [2, "index-84"], [2, "index-87"]], "$cfg['servers'][$i]['pmadb']": [[2, "cfg_Servers_pmadb"], [2, "index-1"], [2, "index-103"], [2, "index-106"], [2, "index-109"], [2, "index-112"], [2, "index-114"], [2, "index-116"], [2, "index-118"], [2, "index-120"], [2, "index-122"], [2, "index-124"], [2, "index-125"], [2, "index-144"], [2, "index-62"], [2, "index-77"], [2, "index-78"], [2, "index-80"], [2, "index-82"], [2, "index-85"], [2, "index-90"], [2, "index-95"], [2, "index-99"], [17, "index-16"], [17, "index-8"]], "$cfg['servers'][$i]['port']": [[2, "cfg_Servers_port"], [2, "index-11"]], "$cfg['servers'][$i]['recent']": [[2, "cfg_Servers_recent"], [2, "index-100"]], "$cfg['servers'][$i]['relation']": [[2, "cfg_Servers_relation"], [2, "index-81"]], "$cfg['servers'][$i]['savedsearches']": [[2, "cfg_Servers_savedsearches"], [2, "index-119"]], "$cfg['servers'][$i]['socket']": [[2, "cfg_Servers_socket"], [2, "index-10"], [6, "index-7"]], "$cfg['servers'][$i]['ssl']": [[2, "cfg_Servers_ssl"], [2, "index-162"], [2, "index-167"], [2, "index-22"], [2, "index-28"], [2, "index-34"], [2, "index-40"], [2, "index-46"], [2, "index-52"], [2, "index-66"], [17, "index-21"], [17, "index-68"], [17, "index-75"]], "$cfg['servers'][$i]['ssl_ca']": [[2, "cfg_Servers_ssl_ca"], [2, "index-165"], [2, "index-168"], [2, "index-18"], [2, "index-24"], [2, "index-30"], [2, "index-43"], [2, "index-49"], [2, "index-55"], [2, "index-69"], [17, "index-24"], [17, "index-25"], [17, "index-71"], [17, "index-78"]], "$cfg['servers'][$i]['ssl_ca_path']": [[2, "cfg_Servers_ssl_ca_path"], [2, "index-19"], [2, "index-25"], [2, "index-31"], [2, "index-37"], [2, "index-50"], [2, "index-56"], [2, "index-70"], [17, "index-72"], [17, "index-79"]], "$cfg['servers'][$i]['ssl_cert']": [[2, "cfg_Servers_ssl_cert"], [2, "index-164"], [2, "index-17"], [2, "index-23"], [2, "index-36"], [2, "index-42"], [2, "index-48"], [2, "index-54"], [2, "index-68"], [17, "index-26"], [17, "index-27"], [17, "index-70"], [17, "index-77"]], "$cfg['servers'][$i]['ssl_ciphers']": [[2, "cfg_Servers_ssl_ciphers"], [2, "index-20"], [2, "index-26"], [2, "index-32"], [2, "index-38"], [2, "index-44"], [2, "index-57"], [2, "index-71"], [17, "index-80"]], "$cfg['servers'][$i]['ssl_key']": [[2, "cfg_Servers_ssl_key"], [2, "index-16"], [2, "index-163"], [2, "index-29"], [2, "index-35"], [2, "index-41"], [2, "index-47"], [2, "index-53"], [2, "index-67"], [17, "index-28"], [17, "index-29"], [17, "index-69"], [17, "index-76"]], "$cfg['servers'][$i]['ssl_verify']": [[2, "cfg_Servers_ssl_verify"], [2, "index-166"], [2, "index-169"], [2, "index-21"], [2, "index-27"], [2, "index-33"], [2, "index-39"], [2, "index-45"], [2, "index-51"], [2, "index-58"], [2, "index-72"], [17, "index-22"], [17, "index-23"], [17, "index-73"], [17, "index-81"]], "$cfg['servers'][$i]['table_coords']": [[2, "cfg_Servers_table_coords"], [2, "index-86"], [2, "index-88"], [13, "index-0"]], "$cfg['servers'][$i]['table_info']": [[2, "cfg_Servers_table_info"], [2, "index-83"]], "$cfg['servers'][$i]['table_uiprefs']": [[2, "cfg_Servers_table_uiprefs"], [2, "index-107"], [2, "index-127"], [2, "index-128"], [2, "index-129"]], "$cfg['servers'][$i]['tracking']": [[2, "cfg_Servers_tracking"], [2, "index-123"]], "$cfg['servers'][$i]['tracking_add_drop_database']": [[2, "cfg_Servers_tracking_add_drop_database"]], "$cfg['servers'][$i]['tracking_add_drop_table']": [[2, "cfg_Servers_tracking_add_drop_table"]], "$cfg['servers'][$i]['tracking_add_drop_view']": [[2, "cfg_Servers_tracking_add_drop_view"]], "$cfg['servers'][$i]['tracking_default_statements']": [[2, "cfg_Servers_tracking_default_statements"]], "$cfg['servers'][$i]['tracking_version_auto_create']": [[2, "cfg_Servers_tracking_version_auto_create"]], "$cfg['servers'][$i]['user']": [[2, "cfg_Servers_user"], [17, "index-57"]], "$cfg['servers'][$i]['userconfig']": [[2, "cfg_Servers_userconfig"], [2, "index-126"]], "$cfg['servers'][$i]['usergroups']": [[2, "cfg_Servers_usergroups"], [2, "index-108"], [2, "index-111"], [12, "index-1"]], "$cfg['servers'][$i]['users']": [[2, "cfg_Servers_users"], [2, "index-110"], [12, "index-0"]], "$cfg['servers'][$i]['verbose']": [[2, "cfg_Servers_verbose"], [2, "index-148"], [2, "index-73"], [6, "index-23"], [17, "index-3"]], "$cfg['sessionsavepath']": [[2, "cfg_SessionSavePath"], [17, "index-67"]], "$cfg['showall']": [[2, "cfg_ShowAll"]], "$cfg['showbrowsecomments']": [[2, "cfg_ShowBrowseComments"]], "$cfg['showchgpassword']": [[2, "cfg_ShowChgPassword"]], "$cfg['showcolumncomments']": [[2, "cfg_ShowColumnComments"]], "$cfg['showcreatedb']": [[2, "cfg_ShowCreateDb"]], "$cfg['showdatabasesnavigationastree']": [[2, "cfg_ShowDatabasesNavigationAsTree"]], "$cfg['showdbstructurecharset']": [[2, "cfg_ShowDbStructureCharset"]], "$cfg['showdbstructurecomment']": [[2, "cfg_ShowDbStructureComment"]], "$cfg['showdbstructurecreation']": [[2, "cfg_ShowDbStructureCreation"]], "$cfg['showdbstructurelastcheck']": [[2, "cfg_ShowDbStructureLastCheck"]], "$cfg['showdbstructurelastupdate']": [[2, "cfg_ShowDbStructureLastUpdate"]], "$cfg['showfieldtypesindataeditview']": [[2, "cfg_ShowFieldTypesInDataEditView"]], "$cfg['showfunctionfields']": [[2, "cfg_ShowFunctionFields"]], "$cfg['showgitrevision']": [[2, "cfg_ShowGitRevision"]], "$cfg['showhint']": [[2, "cfg_ShowHint"]], "$cfg['showphpinfo']": [[2, "cfg_ShowPhpInfo"]], "$cfg['showpropertycomments']": [[2, "cfg_ShowPropertyComments"]], "$cfg['showsql']": [[2, "cfg_ShowSQL"]], "$cfg['showserverinfo']": [[2, "cfg_ShowServerInfo"]], "$cfg['showstats']": [[2, "cfg_ShowStats"]], "$cfg['skiplockedtables']": [[2, "cfg_SkipLockedTables"]], "$cfg['suhosindisablewarning']": [[2, "cfg_SuhosinDisableWarning"], [6, "index-5"]], "$cfg['tablenavigationlinksmode']": [[2, "cfg_TableNavigationLinksMode"]], "$cfg['tableprimarykeyorder']": [[2, "cfg_TablePrimaryKeyOrder"]], "$cfg['tabsmode']": [[2, "cfg_TabsMode"]], "$cfg['tempdir']": [[2, "cfg_TempDir"], [2, "index-3"], [6, "index-1"], [6, "index-25"], [17, "index-61"]], "$cfg['textareaautoselect']": [[2, "cfg_TextareaAutoSelect"]], "$cfg['textareacols']": [[2, "cfg_TextareaCols"]], "$cfg['textarearows']": [[2, "cfg_TextareaRows"]], "$cfg['themedefault']": [[2, "cfg_ThemeDefault"], [18, "index-1"]], "$cfg['thememanager']": [[2, "cfg_ThemeManager"], [18, "index-0"], [18, "index-2"]], "$cfg['themeperserver']": [[2, "cfg_ThemePerServer"]], "$cfg['titledatabase']": [[2, "cfg_TitleDatabase"]], "$cfg['titledefault']": [[2, "cfg_TitleDefault"]], "$cfg['titleserver']": [[2, "cfg_TitleServer"]], "$cfg['titletable']": [[2, "cfg_TitleTable"]], "$cfg['translationwarningthreshold']": [[2, "cfg_TranslationWarningThreshold"]], "$cfg['trustedproxies']": [[2, "cfg_TrustedProxies"], [2, "index-131"]], "$cfg['urlqueryencryption']": [[2, "cfg_URLQueryEncryption"]], "$cfg['urlqueryencryptionsecretkey']": [[2, "cfg_URLQueryEncryptionSecretKey"]], "$cfg['uploaddir']": [[2, "cfg_UploadDir"], [6, "index-2"], [6, "index-24"], [8, "index-0"], [17, "index-19"]], "$cfg['usedbsearch']": [[2, "cfg_UseDbSearch"]], "$cfg['userprefsdevelopertab']": [[2, "cfg_UserprefsDeveloperTab"]], "$cfg['userprefsdisallow']": [[2, "cfg_UserprefsDisallow"], [2, "index-5"]], "$cfg['versioncheck']": [[2, "cfg_VersionCheck"]], "$cfg['zeroconf']": [[2, "cfg_ZeroConf"], [17, "index-36"]], "$cfg['zipdump']": [[2, "cfg_ZipDump"]], "$cfg['blowfish_secret']": [[2, "cfg_blowfish_secret"], [2, "index-139"], [6, "index-11"]], "$cfg['enable_drag_drop_import']": [[2, "cfg_enable_drag_drop_import"]], "$cfg['environment']": [[2, "cfg_environment"]], "$cfg['maxrowplotlimit']": [[2, "cfg_maxRowPlotLimit"]], "actionlinksmode": [[2, "cfg_ActionLinksMode"]], "allowarbitraryserver": [[2, "cfg_AllowArbitraryServer"]], "allowdeny, order": [[2, "cfg_Servers_AllowDeny_order"]], "allowdeny, rules": [[2, "cfg_Servers_AllowDeny_rules"]], "allownopassword": [[2, "cfg_Servers_AllowNoPassword"]], "allowroot": [[2, "cfg_Servers_AllowRoot"]], "allowthirdpartyframing": [[2, "cfg_AllowThirdPartyFraming"]], "allowuserdropdatabase": [[2, "cfg_AllowUserDropDatabase"]], "arbitraryserverregexp": [[2, "cfg_ArbitraryServerRegexp"]], "authlog": [[2, "cfg_AuthLog"]], "authlogsuccess": [[2, "cfg_AuthLogSuccess"]], "availablecharsets": [[2, "cfg_AvailableCharsets"]], "bzipdump": [[2, "cfg_BZipDump"]], "browsemime": [[2, "cfg_BrowseMIME"]], "browsemarkerenable": [[2, "cfg_BrowseMarkerEnable"]], "browsepointerenable": [[2, "cfg_BrowsePointerEnable"]], "cspallow": [[2, "cfg_CSPAllow"]], "captchaapi": [[2, "cfg_CaptchaApi"]], "captchacsp": [[2, "cfg_CaptchaCsp"]], "captchaloginprivatekey": [[2, "cfg_CaptchaLoginPrivateKey"]], "captchaloginpublickey": [[2, "cfg_CaptchaLoginPublicKey"]], "captchamethod": [[2, "cfg_CaptchaMethod"]], "captcharequestparam": [[2, "cfg_CaptchaRequestParam"]], "captcharesponseparam": [[2, "cfg_CaptchaResponseParam"]], "captchasiteverifyurl": [[2, "cfg_CaptchaSiteVerifyURL"]], "charediting": [[2, "cfg_CharEditing"]], "chartextareacols": [[2, "cfg_CharTextareaCols"]], "chartextarearows": [[2, "cfg_CharTextareaRows"]], "checkconfigurationpermissions": [[2, "cfg_CheckConfigurationPermissions"]], "codemirrorenable": [[2, "cfg_CodemirrorEnable"]], "compressonfly": [[2, "cfg_CompressOnFly"]], "confirm": [[2, "cfg_Confirm"]], "console, alwaysexpand": [[2, "cfg_Console_AlwaysExpand"]], "console, currentquery": [[2, "cfg_Console_CurrentQuery"]], "console, darktheme": [[2, "cfg_Console_DarkTheme"]], "console, enterexecutes": [[2, "cfg_Console_EnterExecutes"]], "console, height": [[2, "cfg_Console_Height"]], "console, mode": [[2, "cfg_Console_Mode"]], "console, starthistory": [[2, "cfg_Console_StartHistory"]], "consoleenterexecutes": [[2, "cfg_ConsoleEnterExecutes"]], "cookiesamesite": [[2, "cfg_CookieSameSite"]], "dbg": [[2, "cfg_DBG"]], "dbg, demo": [[2, "cfg_DBG_demo"]], "dbg, simple2fa": [[2, "cfg_DBG_simple2fa"]], "dbg, sql": [[2, "cfg_DBG_sql"]], "dbg, sqllog": [[2, "cfg_DBG_sqllog"]], "defaultconnectioncollation": [[2, "cfg_DefaultConnectionCollation"]], "defaultforeignkeychecks": [[2, "cfg_DefaultForeignKeyChecks"]], "defaultfunctions": [[2, "cfg_DefaultFunctions"]], "defaultlang": [[2, "cfg_DefaultLang"]], "defaultquerydatabase": [[2, "cfg_DefaultQueryDatabase"]], "defaultquerytable": [[2, "cfg_DefaultQueryTable"]], "defaulttabdatabase": [[2, "cfg_DefaultTabDatabase"]], "defaulttabserver": [[2, "cfg_DefaultTabServer"]], "defaulttabtable": [[2, "cfg_DefaultTabTable"]], "defaulttransformations": [[2, "cfg_DefaultTransformations"]], "defaulttransformations, bool2text": [[2, "cfg_DefaultTransformations_Bool2Text"]], "defaulttransformations, dateformat": [[2, "cfg_DefaultTransformations_DateFormat"]], "defaulttransformations, external": [[2, "cfg_DefaultTransformations_External"]], "defaulttransformations, hex": [[2, "cfg_DefaultTransformations_Hex"]], "defaulttransformations, inline": [[2, "cfg_DefaultTransformations_Inline"]], "defaulttransformations, preappend": [[2, "cfg_DefaultTransformations_PreApPend"]], "defaulttransformations, substring": [[2, "cfg_DefaultTransformations_Substring"]], "defaulttransformations, textimagelink": [[2, "cfg_DefaultTransformations_TextImageLink"]], "defaulttransformations, textlink": [[2, "cfg_DefaultTransformations_TextLink"]], "disableis": [[2, "cfg_Servers_DisableIS"]], "disablemultitablemaintenance": [[2, "cfg_DisableMultiTableMaintenance"]], "disableshortcutkeys": [[2, "cfg_DisableShortcutKeys"]], "displaybinaryashex": [[2, "cfg_DisplayBinaryAsHex"]], "displayserverslist": [[2, "cfg_DisplayServersList"]], "editinwindow": [[2, "cfg_EditInWindow"]], "enableautocompletefortablesandcolumns": [[2, "cfg_EnableAutocompleteForTablesAndColumns"]], "exectimelimit": [[2, "cfg_ExecTimeLimit"]], "export": [[2, "cfg_Export"]], "export, charset": [[2, "cfg_Export_charset"]], "export, compression": [[2, "cfg_Export_compression"]], "export, file_template_database": [[2, "cfg_Export_file_template_database"]], "export, file_template_server": [[2, "cfg_Export_file_template_server"]], "export, file_template_table": [[2, "cfg_Export_file_template_table"]], "export, format": [[2, "cfg_Export_format"]], "export, method": [[2, "cfg_Export_method"]], "export, remove_definer_from_definitions": [[2, "cfg_Export_remove_definer_from_definitions"]], "filterlanguages": [[2, "cfg_FilterLanguages"]], "firstdayofcalendar": [[2, "cfg_FirstDayOfCalendar"]], "firstlevelnavigationitems": [[2, "cfg_FirstLevelNavigationItems"]], "fontsize": [[2, "cfg_FontSize"]], "forcessl": [[2, "cfg_ForceSSL"]], "foreignkeydropdownorder": [[2, "cfg_ForeignKeyDropdownOrder"]], "foreignkeymaxlimit": [[2, "cfg_ForeignKeyMaxLimit"]], "gd2available": [[2, "cfg_GD2Available"]], "gzipdump": [[2, "cfg_GZipDump"]], "gridediting": [[2, "cfg_GridEditing"]], "hidestructureactions": [[2, "cfg_HideStructureActions"]], "iconvextraparams": [[2, "cfg_IconvExtraParams"]], "ignoremultisubmiterrors": [[2, "cfg_IgnoreMultiSubmitErrors"]], "import": [[2, "cfg_Import"]], "import, charset": [[2, "cfg_Import_charset"]], "initialslidersstate": [[2, "cfg_InitialSlidersState"]], "insertrows": [[2, "cfg_InsertRows"]], "lang": [[2, "cfg_Lang"]], "limitchars": [[2, "cfg_LimitChars"]], "linklengthlimit": [[2, "cfg_LinkLengthLimit"]], "lintenable": [[2, "cfg_LintEnable"]], "logincookiedeleteall": [[2, "cfg_LoginCookieDeleteAll"]], "logincookierecall": [[2, "cfg_LoginCookieRecall"]], "logincookiestore": [[2, "cfg_LoginCookieStore"]], "logincookievalidity": [[2, "cfg_LoginCookieValidity"]], "logincookievaliditydisablewarning": [[2, "cfg_LoginCookieValidityDisableWarning"]], "logouturl": [[2, "cfg_Servers_LogoutURL"]], "longtextdoubletextarea": [[2, "cfg_LongtextDoubleTextarea"]], "maxcharactersindisplayedsql": [[2, "cfg_MaxCharactersInDisplayedSQL"]], "maxdblist": [[2, "cfg_MaxDbList"]], "maxexactcount": [[2, "cfg_MaxExactCount"]], "maxexactcountviews": [[2, "cfg_MaxExactCountViews"]], "maxnavigationitems": [[2, "cfg_MaxNavigationItems"]], "maxrows": [[2, "cfg_MaxRows"]], "maxsizeforinputfield": [[2, "cfg_MaxSizeForInputField"]], "maxtablelist": [[2, "cfg_MaxTableList"]], "maxtableuiprefs": [[2, "cfg_Servers_MaxTableUiprefs"]], "memorylimit": [[2, "cfg_MemoryLimit"]], "minsizeforinputfield": [[2, "cfg_MinSizeForInputField"]], "mysqlminversion": [[2, "cfg_MysqlMinVersion"]], "mysqlsslwarningsafehosts": [[2, "cfg_MysqlSslWarningSafeHosts"]], "naturalorder": [[2, "cfg_NaturalOrder"]], "navigationdisplaylogo": [[2, "cfg_NavigationDisplayLogo"]], "navigationdisplayservers": [[2, "cfg_NavigationDisplayServers"]], "navigationlinkwithmainpanel": [[2, "cfg_NavigationLinkWithMainPanel"]], "navigationlogolink": [[2, "cfg_NavigationLogoLink"]], "navigationlogolinkwindow": [[2, "cfg_NavigationLogoLinkWindow"]], "navigationtreeautoexpandsingledb": [[2, "cfg_NavigationTreeAutoexpandSingleDb"]], "navigationtreedbseparator": [[2, "cfg_NavigationTreeDbSeparator"]], "navigationtreedefaulttabtable": [[2, "cfg_NavigationTreeDefaultTabTable"]], "navigationtreedefaulttabtable2": [[2, "cfg_NavigationTreeDefaultTabTable2"]], "navigationtreedisplaydbfilterminimum": [[2, "cfg_NavigationTreeDisplayDbFilterMinimum"]], "navigationtreedisplayitemfilterminimum": [[2, "cfg_NavigationTreeDisplayItemFilterMinimum"]], "navigationtreeenableexpansion": [[2, "cfg_NavigationTreeEnableExpansion"]], "navigationtreeenablegrouping": [[2, "cfg_NavigationTreeEnableGrouping"]], "navigationtreepointerenable": [[2, "cfg_NavigationTreePointerEnable"]], "navigationtreeshowevents": [[2, "cfg_NavigationTreeShowEvents"]], "navigationtreeshowfunctions": [[2, "cfg_NavigationTreeShowFunctions"]], "navigationtreeshowprocedures": [[2, "cfg_NavigationTreeShowProcedures"]], "navigationtreeshowtables": [[2, "cfg_NavigationTreeShowTables"]], "navigationtreeshowviews": [[2, "cfg_NavigationTreeShowViews"]], "navigationtreetablelevel": [[2, "cfg_NavigationTreeTableLevel"]], "navigationtreetableseparator": [[2, "cfg_NavigationTreeTableSeparator"]], "navigationwidth": [[2, "cfg_NavigationWidth"]], "numfavoritetables": [[2, "cfg_NumFavoriteTables"]], "numrecenttables": [[2, "cfg_NumRecentTables"]], "obgzip": [[2, "cfg_OBGzip"]], "order": [[2, "cfg_Order"]], "pdfdefaultpagesize": [[2, "cfg_PDFDefaultPageSize"]], "pdfpagesizes": [[2, "cfg_PDFPageSizes"]], "persistentconnections": [[2, "cfg_PersistentConnections"]], "pmaabsoluteuri": [[2, "cfg_PmaAbsoluteUri"]], "pmanorelation_disablewarning": [[2, "cfg_PmaNoRelation_DisableWarning"]], "propertiesnumcolumns": [[2, "cfg_PropertiesNumColumns"]], "protectbinary": [[2, "cfg_ProtectBinary"]], "proxypass": [[2, "cfg_ProxyPass"]], "proxyurl": [[2, "cfg_ProxyUrl"]], "proxyuser": [[2, "cfg_ProxyUser"]], "queryhistorydb": [[2, "cfg_QueryHistoryDB"]], "queryhistorymax": [[2, "cfg_QueryHistoryMax"]], "querywindowdeftab": [[2, "cfg_QueryWindowDefTab"]], "querywindowheight": [[2, "cfg_QueryWindowHeight"]], "querywindowwidth": [[2, "cfg_QueryWindowWidth"]], "recodingengine": [[2, "cfg_RecodingEngine"]], "relationaldisplay": [[2, "cfg_RelationalDisplay"]], "remembersorting": [[2, "cfg_RememberSorting"]], "repeatcells": [[2, "cfg_RepeatCells"]], "reservedworddisablewarning": [[2, "cfg_ReservedWordDisableWarning"]], "retainquerybox": [[2, "cfg_RetainQueryBox"]], "rowactionlinks": [[2, "cfg_RowActionLinks"]], "rowactionlinkswithoutunique": [[2, "cfg_RowActionLinksWithoutUnique"]], "rowactiontype": [[2, "cfg_RowActionType"]], "sqlquery, edit": [[2, "cfg_SQLQuery_Edit"]], "sqlquery, explain": [[2, "cfg_SQLQuery_Explain"]], "sqlquery, refresh": [[2, "cfg_SQLQuery_Refresh"]], "sqlquery, showasphp": [[2, "cfg_SQLQuery_ShowAsPHP"]], "savecellsatonce": [[2, "cfg_SaveCellsAtOnce"]], "savedir": [[2, "cfg_SaveDir"]], "schema": [[2, "cfg_Schema"]], "schema, format": [[2, "cfg_Schema_format"]], "senderrorreports": [[2, "cfg_SendErrorReports"]], "serverdefault": [[2, "cfg_ServerDefault"]], "serverlibrarydifference_disablewarning": [[2, "cfg_ServerLibraryDifference_DisableWarning"]], "servers": [[2, "cfg_Servers"]], "sessionsavepath": [[2, "cfg_SessionSavePath"]], "sessiontimezone": [[2, "cfg_Servers_SessionTimeZone"]], "showall": [[2, "cfg_ShowAll"]], "showbrowsecomments": [[2, "cfg_ShowBrowseComments"]], "showchgpassword": [[2, "cfg_ShowChgPassword"]], "showcolumncomments": [[2, "cfg_ShowColumnComments"]], "showcreatedb": [[2, "cfg_ShowCreateDb"]], "showdatabasesnavigationastree": [[2, "cfg_ShowDatabasesNavigationAsTree"]], "showdbstructurecharset": [[2, "cfg_ShowDbStructureCharset"]], "showdbstructurecomment": [[2, "cfg_ShowDbStructureComment"]], "showdbstructurecreation": [[2, "cfg_ShowDbStructureCreation"]], "showdbstructurelastcheck": [[2, "cfg_ShowDbStructureLastCheck"]], "showdbstructurelastupdate": [[2, "cfg_ShowDbStructureLastUpdate"]], "showfieldtypesindataeditview": [[2, "cfg_ShowFieldTypesInDataEditView"]], "showfunctionfields": [[2, "cfg_ShowFunctionFields"]], "showgitrevision": [[2, "cfg_ShowGitRevision"]], "showhint": [[2, "cfg_ShowHint"]], "showphpinfo": [[2, "cfg_ShowPhpInfo"]], "showpropertycomments": [[2, "cfg_ShowPropertyComments"]], "showsql": [[2, "cfg_ShowSQL"]], "showserverinfo": [[2, "cfg_ShowServerInfo"]], "showstats": [[2, "cfg_ShowStats"]], "signoncookieparams": [[2, "cfg_Servers_SignonCookieParams"]], "signonscript": [[2, "cfg_Servers_SignonScript"]], "signonsession": [[2, "cfg_Servers_SignonSession"]], "signonurl": [[2, "cfg_Servers_SignonURL"]], "skiplockedtables": [[2, "cfg_SkipLockedTables"]], "suhosindisablewarning": [[2, "cfg_SuhosinDisableWarning"]], "tablenavigationlinksmode": [[2, "cfg_TableNavigationLinksMode"]], "tableprimarykeyorder": [[2, "cfg_TablePrimaryKeyOrder"]], "tabsmode": [[2, "cfg_TabsMode"]], "tempdir": [[2, "cfg_TempDir"]], "textareaautoselect": [[2, "cfg_TextareaAutoSelect"]], "textareacols": [[2, "cfg_TextareaCols"]], "textarearows": [[2, "cfg_TextareaRows"]], "themedefault": [[2, "cfg_ThemeDefault"]], "thememanager": [[2, "cfg_ThemeManager"]], "themeperserver": [[2, "cfg_ThemePerServer"]], "titledatabase": [[2, "cfg_TitleDatabase"]], "titledefault": [[2, "cfg_TitleDefault"]], "titleserver": [[2, "cfg_TitleServer"]], "titletable": [[2, "cfg_TitleTable"]], "translationwarningthreshold": [[2, "cfg_TranslationWarningThreshold"]], "trustedproxies": [[2, "cfg_TrustedProxies"]], "urlqueryencryption": [[2, "cfg_URLQueryEncryption"]], "urlqueryencryptionsecretkey": [[2, "cfg_URLQueryEncryptionSecretKey"]], "uploaddir": [[2, "cfg_UploadDir"]], "usedbsearch": [[2, "cfg_UseDbSearch"]], "userprefsdevelopertab": [[2, "cfg_UserprefsDeveloperTab"]], "userprefsdisallow": [[2, "cfg_UserprefsDisallow"]], "versioncheck": [[2, "cfg_VersionCheck"]], "zeroconf": [[2, "cfg_ZeroConf"]], "zipdump": [[2, "cfg_ZipDump"]], "auth_http_realm": [[2, "cfg_Servers_auth_http_realm"]], "auth_swekey_config": [[2, "cfg_Servers_auth_swekey_config"]], "auth_type": [[2, "cfg_Servers_auth_type"]], "blowfish_secret": [[2, "cfg_blowfish_secret"]], "bookmarktable": [[2, "cfg_Servers_bookmarktable"]], "central_columns": [[2, "cfg_Servers_central_columns"]], "column_info": [[2, "cfg_Servers_column_info"]], "compress": [[2, "cfg_Servers_compress"]], "config.inc.php": [[2, "index-0"]], "configuration option": [[2, "cfg_ActionLinksMode"], [2, "cfg_AllowArbitraryServer"], [2, "cfg_AllowThirdPartyFraming"], [2, "cfg_AllowUserDropDatabase"], [2, "cfg_ArbitraryServerRegexp"], [2, "cfg_AuthLog"], [2, "cfg_AuthLogSuccess"], [2, "cfg_AvailableCharsets"], [2, "cfg_BZipDump"], [2, "cfg_BrowseMIME"], [2, "cfg_BrowseMarkerEnable"], [2, "cfg_BrowsePointerEnable"], [2, "cfg_CSPAllow"], [2, "cfg_CaptchaApi"], [2, "cfg_CaptchaCsp"], [2, "cfg_CaptchaLoginPrivateKey"], [2, "cfg_CaptchaLoginPublicKey"], [2, "cfg_CaptchaMethod"], [2, "cfg_CaptchaRequestParam"], [2, "cfg_CaptchaResponseParam"], [2, "cfg_CaptchaSiteVerifyURL"], [2, "cfg_CharEditing"], [2, "cfg_CharTextareaCols"], [2, "cfg_CharTextareaRows"], [2, "cfg_CheckConfigurationPermissions"], [2, "cfg_CodemirrorEnable"], [2, "cfg_CompressOnFly"], [2, "cfg_Confirm"], [2, "cfg_ConsoleEnterExecutes"], [2, "cfg_Console_AlwaysExpand"], [2, "cfg_Console_CurrentQuery"], [2, "cfg_Console_DarkTheme"], [2, "cfg_Console_EnterExecutes"], [2, "cfg_Console_Height"], [2, "cfg_Console_Mode"], [2, "cfg_Console_StartHistory"], [2, "cfg_CookieSameSite"], [2, "cfg_DBG"], [2, "cfg_DBG_demo"], [2, "cfg_DBG_simple2fa"], [2, "cfg_DBG_sql"], [2, "cfg_DBG_sqllog"], [2, "cfg_DefaultConnectionCollation"], [2, "cfg_DefaultForeignKeyChecks"], [2, "cfg_DefaultFunctions"], [2, "cfg_DefaultLang"], [2, "cfg_DefaultQueryDatabase"], [2, "cfg_DefaultQueryTable"], [2, "cfg_DefaultTabDatabase"], [2, "cfg_DefaultTabServer"], [2, "cfg_DefaultTabTable"], [2, "cfg_DefaultTransformations"], [2, "cfg_DefaultTransformations_Bool2Text"], [2, "cfg_DefaultTransformations_DateFormat"], [2, "cfg_DefaultTransformations_External"], [2, "cfg_DefaultTransformations_Hex"], [2, "cfg_DefaultTransformations_Inline"], [2, "cfg_DefaultTransformations_PreApPend"], [2, "cfg_DefaultTransformations_Substring"], [2, "cfg_DefaultTransformations_TextImageLink"], [2, "cfg_DefaultTransformations_TextLink"], [2, "cfg_DisableMultiTableMaintenance"], [2, "cfg_DisableShortcutKeys"], [2, "cfg_DisplayBinaryAsHex"], [2, "cfg_DisplayServersList"], [2, "cfg_EditInWindow"], [2, "cfg_EnableAutocompleteForTablesAndColumns"], [2, "cfg_ExecTimeLimit"], [2, "cfg_Export"], [2, "cfg_Export_charset"], [2, "cfg_Export_compression"], [2, "cfg_Export_file_template_database"], [2, "cfg_Export_file_template_server"], [2, "cfg_Export_file_template_table"], [2, "cfg_Export_format"], [2, "cfg_Export_method"], [2, "cfg_Export_remove_definer_from_definitions"], [2, "cfg_FilterLanguages"], [2, "cfg_FirstDayOfCalendar"], [2, "cfg_FirstLevelNavigationItems"], [2, "cfg_FontSize"], [2, "cfg_ForceSSL"], [2, "cfg_ForeignKeyDropdownOrder"], [2, "cfg_ForeignKeyMaxLimit"], [2, "cfg_GD2Available"], [2, "cfg_GZipDump"], [2, "cfg_GridEditing"], [2, "cfg_HideStructureActions"], [2, "cfg_IconvExtraParams"], [2, "cfg_IgnoreMultiSubmitErrors"], [2, "cfg_Import"], [2, "cfg_Import_charset"], [2, "cfg_InitialSlidersState"], [2, "cfg_InsertRows"], [2, "cfg_Lang"], [2, "cfg_LimitChars"], [2, "cfg_LinkLengthLimit"], [2, "cfg_LintEnable"], [2, "cfg_LoginCookieDeleteAll"], [2, "cfg_LoginCookieRecall"], [2, "cfg_LoginCookieStore"], [2, "cfg_LoginCookieValidity"], [2, "cfg_LoginCookieValidityDisableWarning"], [2, "cfg_LongtextDoubleTextarea"], [2, "cfg_MaxCharactersInDisplayedSQL"], [2, "cfg_MaxDbList"], [2, "cfg_MaxExactCount"], [2, "cfg_MaxExactCountViews"], [2, "cfg_MaxNavigationItems"], [2, "cfg_MaxRows"], [2, "cfg_MaxSizeForInputField"], [2, "cfg_MaxTableList"], [2, "cfg_MemoryLimit"], [2, "cfg_MinSizeForInputField"], [2, "cfg_MysqlMinVersion"], [2, "cfg_MysqlSslWarningSafeHosts"], [2, "cfg_NaturalOrder"], [2, "cfg_NavigationDisplayLogo"], [2, "cfg_NavigationDisplayServers"], [2, "cfg_NavigationLinkWithMainPanel"], [2, "cfg_NavigationLogoLink"], [2, "cfg_NavigationLogoLinkWindow"], [2, "cfg_NavigationTreeAutoexpandSingleDb"], [2, "cfg_NavigationTreeDbSeparator"], [2, "cfg_NavigationTreeDefaultTabTable"], [2, "cfg_NavigationTreeDefaultTabTable2"], [2, "cfg_NavigationTreeDisplayDbFilterMinimum"], [2, "cfg_NavigationTreeDisplayItemFilterMinimum"], [2, "cfg_NavigationTreeEnableExpansion"], [2, "cfg_NavigationTreeEnableGrouping"], [2, "cfg_NavigationTreePointerEnable"], [2, "cfg_NavigationTreeShowEvents"], [2, "cfg_NavigationTreeShowFunctions"], [2, "cfg_NavigationTreeShowProcedures"], [2, "cfg_NavigationTreeShowTables"], [2, "cfg_NavigationTreeShowViews"], [2, "cfg_NavigationTreeTableLevel"], [2, "cfg_NavigationTreeTableSeparator"], [2, "cfg_NavigationWidth"], [2, "cfg_NumFavoriteTables"], [2, "cfg_NumRecentTables"], [2, "cfg_OBGzip"], [2, "cfg_Order"], [2, "cfg_PDFDefaultPageSize"], [2, "cfg_PDFPageSizes"], [2, "cfg_PersistentConnections"], [2, "cfg_PmaAbsoluteUri"], [2, "cfg_PmaNoRelation_DisableWarning"], [2, "cfg_PropertiesNumColumns"], [2, "cfg_ProtectBinary"], [2, "cfg_ProxyPass"], [2, "cfg_ProxyUrl"], [2, "cfg_ProxyUser"], [2, "cfg_QueryHistoryDB"], [2, "cfg_QueryHistoryMax"], [2, "cfg_QueryWindowDefTab"], [2, "cfg_QueryWindowHeight"], [2, "cfg_QueryWindowWidth"], [2, "cfg_RecodingEngine"], [2, "cfg_RelationalDisplay"], [2, "cfg_RememberSorting"], [2, "cfg_RepeatCells"], [2, "cfg_ReservedWordDisableWarning"], [2, "cfg_RetainQueryBox"], [2, "cfg_RowActionLinks"], [2, "cfg_RowActionLinksWithoutUnique"], [2, "cfg_RowActionType"], [2, "cfg_SQLQuery_Edit"], [2, "cfg_SQLQuery_Explain"], [2, "cfg_SQLQuery_Refresh"], [2, "cfg_SQLQuery_ShowAsPHP"], [2, "cfg_SaveCellsAtOnce"], [2, "cfg_SaveDir"], [2, "cfg_Schema"], [2, "cfg_Schema_format"], [2, "cfg_SendErrorReports"], [2, "cfg_ServerDefault"], [2, "cfg_ServerLibraryDifference_DisableWarning"], [2, "cfg_Servers"], [2, "cfg_Servers_AllowDeny_order"], [2, "cfg_Servers_AllowDeny_rules"], [2, "cfg_Servers_AllowNoPassword"], [2, "cfg_Servers_AllowRoot"], [2, "cfg_Servers_DisableIS"], [2, "cfg_Servers_LogoutURL"], [2, "cfg_Servers_MaxTableUiprefs"], [2, "cfg_Servers_SessionTimeZone"], [2, "cfg_Servers_SignonCookieParams"], [2, "cfg_Servers_SignonScript"], [2, "cfg_Servers_SignonSession"], [2, "cfg_Servers_SignonURL"], [2, "cfg_Servers_auth_http_realm"], [2, "cfg_Servers_auth_swekey_config"], [2, "cfg_Servers_auth_type"], [2, "cfg_Servers_bookmarktable"], [2, "cfg_Servers_central_columns"], [2, "cfg_Servers_column_info"], [2, "cfg_Servers_compress"], [2, "cfg_Servers_connect_type"], [2, "cfg_Servers_control_*"], [2, "cfg_Servers_controlhost"], [2, "cfg_Servers_controlpass"], [2, "cfg_Servers_controlport"], [2, "cfg_Servers_controluser"], [2, "cfg_Servers_designer_coords"], [2, "cfg_Servers_designer_settings"], [2, "cfg_Servers_export_templates"], [2, "cfg_Servers_extension"], [2, "cfg_Servers_favorite"], [2, "cfg_Servers_hide_connection_errors"], [2, "cfg_Servers_hide_db"], [2, "cfg_Servers_history"], [2, "cfg_Servers_host"], [2, "cfg_Servers_navigationhiding"], [2, "cfg_Servers_nopassword"], [2, "cfg_Servers_only_db"], [2, "cfg_Servers_password"], [2, "cfg_Servers_pdf_pages"], [2, "cfg_Servers_pmadb"], [2, "cfg_Servers_port"], [2, "cfg_Servers_recent"], [2, "cfg_Servers_relation"], [2, "cfg_Servers_savedsearches"], [2, "cfg_Servers_socket"], [2, "cfg_Servers_ssl"], [2, "cfg_Servers_ssl_ca"], [2, "cfg_Servers_ssl_ca_path"], [2, "cfg_Servers_ssl_cert"], [2, "cfg_Servers_ssl_ciphers"], [2, "cfg_Servers_ssl_key"], [2, "cfg_Servers_ssl_verify"], [2, "cfg_Servers_table_coords"], [2, "cfg_Servers_table_info"], [2, "cfg_Servers_table_uiprefs"], [2, "cfg_Servers_tracking"], [2, "cfg_Servers_tracking_add_drop_database"], [2, "cfg_Servers_tracking_add_drop_table"], [2, "cfg_Servers_tracking_add_drop_view"], [2, "cfg_Servers_tracking_default_statements"], [2, "cfg_Servers_tracking_version_auto_create"], [2, "cfg_Servers_user"], [2, "cfg_Servers_userconfig"], [2, "cfg_Servers_usergroups"], [2, "cfg_Servers_users"], [2, "cfg_Servers_verbose"], [2, "cfg_SessionSavePath"], [2, "cfg_ShowAll"], [2, "cfg_ShowBrowseComments"], [2, "cfg_ShowChgPassword"], [2, "cfg_ShowColumnComments"], [2, "cfg_ShowCreateDb"], [2, "cfg_ShowDatabasesNavigationAsTree"], [2, "cfg_ShowDbStructureCharset"], [2, "cfg_ShowDbStructureComment"], [2, "cfg_ShowDbStructureCreation"], [2, "cfg_ShowDbStructureLastCheck"], [2, "cfg_ShowDbStructureLastUpdate"], [2, "cfg_ShowFieldTypesInDataEditView"], [2, "cfg_ShowFunctionFields"], [2, "cfg_ShowGitRevision"], [2, "cfg_ShowHint"], [2, "cfg_ShowPhpInfo"], [2, "cfg_ShowPropertyComments"], [2, "cfg_ShowSQL"], [2, "cfg_ShowServerInfo"], [2, "cfg_ShowStats"], [2, "cfg_SkipLockedTables"], [2, "cfg_SuhosinDisableWarning"], [2, "cfg_TableNavigationLinksMode"], [2, "cfg_TablePrimaryKeyOrder"], [2, "cfg_TabsMode"], [2, "cfg_TempDir"], [2, "cfg_TextareaAutoSelect"], [2, "cfg_TextareaCols"], [2, "cfg_TextareaRows"], [2, "cfg_ThemeDefault"], [2, "cfg_ThemeManager"], [2, "cfg_ThemePerServer"], [2, "cfg_TitleDatabase"], [2, "cfg_TitleDefault"], [2, "cfg_TitleServer"], [2, "cfg_TitleTable"], [2, "cfg_TranslationWarningThreshold"], [2, "cfg_TrustedProxies"], [2, "cfg_URLQueryEncryption"], [2, "cfg_URLQueryEncryptionSecretKey"], [2, "cfg_UploadDir"], [2, "cfg_UseDbSearch"], [2, "cfg_UserprefsDeveloperTab"], [2, "cfg_UserprefsDisallow"], [2, "cfg_VersionCheck"], [2, "cfg_ZeroConf"], [2, "cfg_ZipDump"], [2, "cfg_blowfish_secret"], [2, "cfg_enable_drag_drop_import"], [2, "cfg_environment"], [2, "cfg_maxRowPlotLimit"], [2, "index-1"], [2, "index-10"], [2, "index-100"], [2, "index-101"], [2, "index-102"], [2, "index-103"], [2, "index-104"], [2, "index-105"], [2, "index-106"], [2, "index-107"], [2, "index-108"], [2, "index-109"], [2, "index-11"], [2, "index-110"], [2, "index-111"], [2, "index-112"], [2, "index-113"], [2, "index-114"], [2, "index-115"], [2, "index-116"], [2, "index-117"], [2, "index-118"], [2, "index-119"], [2, "index-12"], [2, "index-120"], [2, "index-121"], [2, "index-122"], [2, "index-123"], [2, "index-124"], [2, "index-125"], [2, "index-126"], [2, "index-127"], [2, "index-128"], [2, "index-129"], [2, "index-13"], [2, "index-130"], [2, "index-131"], [2, "index-132"], [2, "index-133"], [2, "index-134"], [2, "index-135"], [2, "index-136"], [2, "index-137"], [2, "index-138"], [2, "index-139"], [2, "index-14"], [2, "index-140"], [2, "index-141"], [2, "index-142"], [2, "index-143"], [2, "index-144"], [2, "index-145"], [2, "index-146"], [2, "index-147"], [2, "index-148"], [2, "index-149"], [2, "index-15"], [2, "index-150"], [2, "index-151"], [2, "index-152"], [2, "index-153"], [2, "index-154"], [2, "index-155"], [2, "index-156"], [2, "index-157"], [2, "index-158"], [2, "index-159"], [2, "index-16"], [2, "index-160"], [2, "index-161"], [2, "index-162"], [2, "index-163"], [2, "index-164"], [2, "index-165"], [2, "index-166"], [2, "index-167"], [2, "index-168"], [2, "index-169"], [2, "index-17"], [2, "index-18"], [2, "index-19"], [2, "index-2"], [2, "index-20"], [2, "index-21"], [2, "index-22"], [2, "index-23"], [2, "index-24"], [2, "index-25"], [2, "index-26"], [2, "index-27"], [2, "index-28"], [2, "index-29"], [2, "index-3"], [2, "index-30"], [2, "index-31"], [2, "index-32"], [2, "index-33"], [2, "index-34"], [2, "index-35"], [2, "index-36"], [2, "index-37"], [2, "index-38"], [2, "index-39"], [2, "index-4"], [2, "index-40"], [2, "index-41"], [2, "index-42"], [2, "index-43"], [2, "index-44"], [2, "index-45"], [2, "index-46"], [2, "index-47"], [2, "index-48"], [2, "index-49"], [2, "index-5"], [2, "index-50"], [2, "index-51"], [2, "index-52"], [2, "index-53"], [2, "index-54"], [2, "index-55"], [2, "index-56"], [2, "index-57"], [2, "index-58"], [2, "index-59"], [2, "index-6"], [2, "index-60"], [2, "index-61"], [2, "index-62"], [2, "index-63"], [2, "index-64"], [2, "index-65"], [2, "index-66"], [2, "index-67"], [2, "index-68"], [2, "index-69"], [2, "index-7"], [2, "index-70"], [2, "index-71"], [2, "index-72"], [2, "index-73"], [2, "index-74"], [2, "index-75"], [2, "index-76"], [2, "index-77"], [2, "index-78"], [2, "index-79"], [2, "index-8"], [2, "index-80"], [2, "index-81"], [2, "index-82"], [2, "index-83"], [2, "index-84"], [2, "index-85"], [2, "index-86"], [2, "index-87"], [2, "index-88"], [2, "index-89"], [2, "index-9"], [2, "index-90"], [2, "index-91"], [2, "index-92"], [2, "index-93"], [2, "index-94"], [2, "index-95"], [2, "index-96"], [2, "index-97"], [2, "index-98"], [2, "index-99"], [6, "index-0"], [6, "index-1"], [6, "index-10"], [6, "index-11"], [6, "index-12"], [6, "index-13"], [6, "index-14"], [6, "index-15"], [6, "index-16"], [6, "index-17"], [6, "index-18"], [6, "index-19"], [6, "index-2"], [6, "index-20"], [6, "index-21"], [6, "index-22"], [6, "index-23"], [6, "index-24"], [6, "index-25"], [6, "index-26"], [6, "index-3"], [6, "index-5"], [6, "index-6"], [6, "index-7"], [6, "index-8"], [6, "index-9"], [8, "index-0"], [8, "index-1"], [12, "index-0"], [12, "index-1"], [13, "index-0"], [17, "index-0"], [17, "index-1"], [17, "index-10"], [17, "index-11"], [17, "index-12"], [17, "index-13"], [17, "index-14"], [17, "index-15"], [17, "index-16"], [17, "index-17"], [17, "index-18"], [17, "index-19"], [17, "index-20"], [17, "index-21"], [17, "index-22"], [17, "index-23"], [17, "index-24"], [17, "index-25"], [17, "index-26"], [17, "index-27"], [17, "index-28"], [17, "index-29"], [17, "index-3"], [17, "index-30"], [17, "index-36"], [17, "index-37"], [17, "index-38"], [17, "index-40"], [17, "index-41"], [17, "index-44"], [17, "index-46"], [17, "index-47"], [17, "index-48"], [17, "index-49"], [17, "index-50"], [17, "index-51"], [17, "index-52"], [17, "index-53"], [17, "index-54"], [17, "index-55"], [17, "index-57"], [17, "index-58"], [17, "index-59"], [17, "index-60"], [17, "index-61"], [17, "index-62"], [17, "index-63"], [17, "index-64"], [17, "index-65"], [17, "index-66"], [17, "index-67"], [17, "index-68"], [17, "index-69"], [17, "index-7"], [17, "index-70"], [17, "index-71"], [17, "index-72"], [17, "index-73"], [17, "index-74"], [17, "index-75"], [17, "index-76"], [17, "index-77"], [17, "index-78"], [17, "index-79"], [17, "index-8"], [17, "index-80"], [17, "index-81"], [17, "index-9"], [18, "index-0"], [18, "index-1"], [18, "index-2"], [19, "index-0"], [20, "index-0"]], "connect_type": [[2, "cfg_Servers_connect_type"]], "control_*": [[2, "cfg_Servers_control_*"]], "controlhost": [[2, "cfg_Servers_controlhost"]], "controlpass": [[2, "cfg_Servers_controlpass"]], "controlport": [[2, "cfg_Servers_controlport"]], "controluser": [[2, "cfg_Servers_controluser"]], "designer_coords": [[2, "cfg_Servers_designer_coords"]], "designer_settings": [[2, "cfg_Servers_designer_settings"]], "enable_drag_drop_import": [[2, "cfg_enable_drag_drop_import"]], "environment": [[2, "cfg_environment"]], "export_templates": [[2, "cfg_Servers_export_templates"]], "extension": [[2, "cfg_Servers_extension"]], "favorite": [[2, "cfg_Servers_favorite"]], "hide_connection_errors": [[2, "cfg_Servers_hide_connection_errors"]], "hide_db": [[2, "cfg_Servers_hide_db"]], "history": [[2, "cfg_Servers_history"]], "host": [[2, "cfg_Servers_host"], [7, "term-host"]], "maxrowplotlimit": [[2, "cfg_maxRowPlotLimit"]], "navigationhiding": [[2, "cfg_Servers_navigationhiding"]], "nopassword": [[2, "cfg_Servers_nopassword"]], "only_db": [[2, "cfg_Servers_only_db"]], "password": [[2, "cfg_Servers_password"]], "pdf_pages": [[2, "cfg_Servers_pdf_pages"]], "pmadb": [[2, "cfg_Servers_pmadb"], [17, "index-35"]], "port": [[2, "cfg_Servers_port"], [7, "term-port"]], "recent": [[2, "cfg_Servers_recent"]], "relation": [[2, "cfg_Servers_relation"]], "savedsearches": [[2, "cfg_Servers_savedsearches"]], "server configuration": [[2, "cfg_Servers_AllowDeny_order"], [2, "cfg_Servers_AllowDeny_rules"], [2, "cfg_Servers_AllowNoPassword"], [2, "cfg_Servers_AllowRoot"], [2, "cfg_Servers_DisableIS"], [2, "cfg_Servers_LogoutURL"], [2, "cfg_Servers_MaxTableUiprefs"], [2, "cfg_Servers_SessionTimeZone"], [2, "cfg_Servers_SignonCookieParams"], [2, "cfg_Servers_SignonScript"], [2, "cfg_Servers_SignonSession"], [2, "cfg_Servers_SignonURL"], [2, "cfg_Servers_auth_http_realm"], [2, "cfg_Servers_auth_swekey_config"], [2, "cfg_Servers_auth_type"], [2, "cfg_Servers_bookmarktable"], [2, "cfg_Servers_central_columns"], [2, "cfg_Servers_column_info"], [2, "cfg_Servers_compress"], [2, "cfg_Servers_connect_type"], [2, "cfg_Servers_control_*"], [2, "cfg_Servers_controlhost"], [2, "cfg_Servers_controlpass"], [2, "cfg_Servers_controlport"], [2, "cfg_Servers_controluser"], [2, "cfg_Servers_designer_coords"], [2, "cfg_Servers_designer_settings"], [2, "cfg_Servers_export_templates"], [2, "cfg_Servers_extension"], [2, "cfg_Servers_favorite"], [2, "cfg_Servers_hide_connection_errors"], [2, "cfg_Servers_hide_db"], [2, "cfg_Servers_history"], [2, "cfg_Servers_host"], [2, "cfg_Servers_navigationhiding"], [2, "cfg_Servers_nopassword"], [2, "cfg_Servers_only_db"], [2, "cfg_Servers_password"], [2, "cfg_Servers_pdf_pages"], [2, "cfg_Servers_pmadb"], [2, "cfg_Servers_port"], [2, "cfg_Servers_recent"], [2, "cfg_Servers_relation"], [2, "cfg_Servers_savedsearches"], [2, "cfg_Servers_socket"], [2, "cfg_Servers_ssl"], [2, "cfg_Servers_ssl_ca"], [2, "cfg_Servers_ssl_ca_path"], [2, "cfg_Servers_ssl_cert"], [2, "cfg_Servers_ssl_ciphers"], [2, "cfg_Servers_ssl_key"], [2, "cfg_Servers_ssl_verify"], [2, "cfg_Servers_table_coords"], [2, "cfg_Servers_table_info"], [2, "cfg_Servers_table_uiprefs"], [2, "cfg_Servers_tracking"], [2, "cfg_Servers_tracking_add_drop_database"], [2, "cfg_Servers_tracking_add_drop_table"], [2, "cfg_Servers_tracking_add_drop_view"], [2, "cfg_Servers_tracking_default_statements"], [2, "cfg_Servers_tracking_version_auto_create"], [2, "cfg_Servers_user"], [2, "cfg_Servers_userconfig"], [2, "cfg_Servers_usergroups"], [2, "cfg_Servers_users"], [2, "cfg_Servers_verbose"]], "socket": [[2, "cfg_Servers_socket"], [7, "term-socket"]], "ssl": [[2, "cfg_Servers_ssl"], [7, "term-SSL"]], "ssl_ca": [[2, "cfg_Servers_ssl_ca"]], "ssl_ca_path": [[2, "cfg_Servers_ssl_ca_path"]], "ssl_cert": [[2, "cfg_Servers_ssl_cert"]], "ssl_ciphers": [[2, "cfg_Servers_ssl_ciphers"]], "ssl_key": [[2, "cfg_Servers_ssl_key"]], "ssl_verify": [[2, "cfg_Servers_ssl_verify"]], "table_coords": [[2, "cfg_Servers_table_coords"]], "table_info": [[2, "cfg_Servers_table_info"]], "table_uiprefs": [[2, "cfg_Servers_table_uiprefs"]], "tracking": [[2, "cfg_Servers_tracking"]], "tracking_add_drop_database": [[2, "cfg_Servers_tracking_add_drop_database"]], "tracking_add_drop_table": [[2, "cfg_Servers_tracking_add_drop_table"]], "tracking_add_drop_view": [[2, "cfg_Servers_tracking_add_drop_view"]], "tracking_default_statements": [[2, "cfg_Servers_tracking_default_statements"]], "tracking_version_auto_create": [[2, "cfg_Servers_tracking_version_auto_create"]], "user": [[2, "cfg_Servers_user"]], "userconfig": [[2, "cfg_Servers_userconfig"]], "usergroups": [[2, "cfg_Servers_usergroups"]], "users": [[2, "cfg_Servers_users"]], "verbose": [[2, "cfg_Servers_verbose"]], "rfc": [[6, "index-4"], [7, "index-0"], [7, "term-RFC"]], "rfc 2616": [[6, "index-4"]], ".htaccess": [[7, "term-.htaccess"]], "acl": [[7, "term-ACL"]], "blowfish": [[7, "term-Blowfish"]], "browser": [[7, "term-Browser"]], "cgi": [[7, "term-CGI"]], "csv": [[7, "term-CSV"]], "changelog": [[7, "term-Changelog"]], "client": [[7, "term-Client"]], "content security policy": [[7, "term-Content-Security-Policy"]], "cookie": [[7, "term-Cookie"], [17, "index-43"]], "db": [[7, "term-DB"]], "database": [[7, "term-Database"]], "engine": [[7, "term-Engine"]], "faq": [[7, "term-FAQ"]], "field": [[7, "term-Field"]], "foreign key": [[7, "term-Foreign-key"]], "gd": [[7, "term-GD"]], "gd2": [[7, "term-GD2"]], "gzip": [[7, "term-GZip"]], "http": [[7, "term-HTTP"], [17, "index-42"]], "https": [[7, "term-HTTPS"]], "iec": [[7, "term-IEC"]], "iis": [[7, "term-IIS"]], "ip": [[7, "term-IP"]], "ip address": [[7, "term-IP-Address"]], "ipv6": [[7, "term-IPv6"]], "isapi": [[7, "term-ISAPI"]], "iso": [[7, "term-ISO"]], "isp": [[7, "term-ISP"]], "index": [[7, "term-Index"]], "jpeg": [[7, "term-JPEG"]], "jpg": [[7, "term-JPG"]], "key": [[7, "term-Key"]], "latex": [[7, "term-LATEX"]], "mime": [[7, "term-MIME"]], "mac": [[7, "term-Mac"]], "media type": [[7, "term-Media-type"]], "mysql": [[7, "term-MySQL"], [7, "term-mysql"]], "mysqli": [[7, "term-MySQLi"]], "os x": [[7, "term-OS-X"]], "opendocument": [[7, "term-OpenDocument"]], "pcre": [[7, "term-PCRE"]], "pdf": [[7, "term-PDF"]], "pear": [[7, "term-PEAR"]], "php": [[7, "term-PHP"]], "php extension": [[7, "term-PHP-extension"]], "rfc 1952": [[7, "index-0"], [7, "term-RFC-1952"]], "row (record, tuple)": [[7, "term-Row-record-tuple"]], "sql": [[7, "term-SQL"]], "server": [[7, "term-Server"]], "sodium": [[7, "term-Sodium"]], "storage engines": [[7, "term-Storage-Engines"]], "stored procedure": [[7, "term-Stored-procedure"]], "tcp": [[7, "term-TCP"]], "tcpdf": [[7, "term-TCPDF"]], "url": [[7, "term-URL"]], "web server": [[7, "term-Web-server"]], "xml": [[7, "term-XML"]], "zip": [[7, "term-ZIP"]], "zlib": [[7, "term-Zlib"]], "bzip2": [[7, "term-bzip2"]], "column": [[7, "term-column"]], "hostname": [[7, "term-hostname"]], "macos": [[7, "term-macOS"]], "mbstring": [[7, "term-mbstring"]], "mod_proxy_fcgi": [[7, "term-mod_proxy_fcgi"]], "module": [[7, "term-module"]], "primary key": [[7, "term-primary-key"]], "table": [[7, "term-table"]], "tar": [[7, "term-tar"]], "trigger": [[7, "term-trigger"]], "unique key": [[7, "term-unique-key"]], "comment (global variable or constant)": [[8, "comment"]], "data (global variable or constant)": [[8, "data"]], "database (global variable or constant)": [[8, "database"]], "name (global variable or constant)": [[8, "name"]], "type (global variable or constant)": [[8, "type"]], "version (global variable or constant)": [[8, "version"]], "apache_port": [[17, "envvar-APACHE_PORT"]], "authentication mode": [[17, "index-39"], [17, "index-42"], [17, "index-43"], [17, "index-45"], [17, "index-56"]], "config": [[17, "index-56"]], "configuration storage": [[17, "index-35"]], "hide_php_version": [[17, "envvar-HIDE_PHP_VERSION"]], "max_execution_time": [[17, "envvar-MAX_EXECUTION_TIME"]], "memory_limit": [[17, "envvar-MEMORY_LIMIT"]], "pma_absolute_uri": [[17, "envvar-PMA_ABSOLUTE_URI"], [17, "index-33"]], "pma_arbitrary": [[17, "envvar-PMA_ARBITRARY"]], "pma_config_base64": [[17, "envvar-PMA_CONFIG_BASE64"]], "pma_controlhost": [[17, "envvar-PMA_CONTROLHOST"]], "pma_controlpass": [[17, "envvar-PMA_CONTROLPASS"]], "pma_controlport": [[17, "envvar-PMA_CONTROLPORT"]], "pma_controluser": [[17, "envvar-PMA_CONTROLUSER"]], "pma_host": [[17, "envvar-PMA_HOST"], [17, "index-2"]], "pma_hosts": [[17, "envvar-PMA_HOSTS"]], "pma_password": [[17, "envvar-PMA_PASSWORD"], [17, "index-32"]], "pma_pmadb": [[17, "envvar-PMA_PMADB"]], "pma_port": [[17, "envvar-PMA_PORT"], [17, "index-5"]], "pma_ports": [[17, "envvar-PMA_PORTS"]], "pma_queryhistorydb": [[17, "envvar-PMA_QUERYHISTORYDB"]], "pma_queryhistorymax": [[17, "envvar-PMA_QUERYHISTORYMAX"]], "pma_savedir": [[17, "envvar-PMA_SAVEDIR"]], "pma_socket": [[17, "envvar-PMA_SOCKET"], [17, "index-6"]], "pma_sockets": [[17, "envvar-PMA_SOCKETS"]], "pma_ssl": [[17, "envvar-PMA_SSL"]], "pma_ssls": [[17, "envvar-PMA_SSLS"]], "pma_ssl_ca": [[17, "envvar-PMA_SSL_CA"]], "pma_ssl_cas": [[17, "envvar-PMA_SSL_CAS"]], "pma_ssl_cas_base64": [[17, "envvar-PMA_SSL_CAS_BASE64"]], "pma_ssl_ca_base64": [[17, "envvar-PMA_SSL_CA_BASE64"]], "pma_ssl_cert": [[17, "envvar-PMA_SSL_CERT"]], "pma_ssl_certs": [[17, "envvar-PMA_SSL_CERTS"]], "pma_ssl_certs_base64": [[17, "envvar-PMA_SSL_CERTS_BASE64"]], "pma_ssl_cert_base64": [[17, "envvar-PMA_SSL_CERT_BASE64"]], "pma_ssl_dir": [[17, "envvar-PMA_SSL_DIR"]], "pma_ssl_key": [[17, "envvar-PMA_SSL_KEY"]], "pma_ssl_keys": [[17, "envvar-PMA_SSL_KEYS"]], "pma_ssl_keys_base64": [[17, "envvar-PMA_SSL_KEYS_BASE64"]], "pma_ssl_key_base64": [[17, "envvar-PMA_SSL_KEY_BASE64"]], "pma_ssl_verifies": [[17, "envvar-PMA_SSL_VERIFIES"]], "pma_ssl_verify": [[17, "envvar-PMA_SSL_VERIFY"]], "pma_uploaddir": [[17, "envvar-PMA_UPLOADDIR"]], "pma_user": [[17, "envvar-PMA_USER"], [17, "index-31"]], "pma_user_config_base64": [[17, "envvar-PMA_USER_CONFIG_BASE64"]], "pma_verbose": [[17, "envvar-PMA_VERBOSE"], [17, "index-4"]], "pma_verboses": [[17, "envvar-PMA_VERBOSES"]], "setup script": [[17, "index-34"]], "signon": [[17, "index-45"]], "tz": [[17, "envvar-TZ"]], "upload_limit": [[17, "envvar-UPLOAD_LIMIT"]], "environment variable": [[17, "envvar-APACHE_PORT"], [17, "envvar-HIDE_PHP_VERSION"], [17, "envvar-MAX_EXECUTION_TIME"], [17, "envvar-MEMORY_LIMIT"], [17, "envvar-PMA_ABSOLUTE_URI"], [17, "envvar-PMA_ARBITRARY"], [17, "envvar-PMA_CONFIG_BASE64"], [17, "envvar-PMA_CONTROLHOST"], [17, "envvar-PMA_CONTROLPASS"], [17, "envvar-PMA_CONTROLPORT"], [17, "envvar-PMA_CONTROLUSER"], [17, "envvar-PMA_HOST"], [17, "envvar-PMA_HOSTS"], [17, "envvar-PMA_PASSWORD"], [17, "envvar-PMA_PMADB"], [17, "envvar-PMA_PORT"], [17, "envvar-PMA_PORTS"], [17, "envvar-PMA_QUERYHISTORYDB"], [17, "envvar-PMA_QUERYHISTORYMAX"], [17, "envvar-PMA_SAVEDIR"], [17, "envvar-PMA_SOCKET"], [17, "envvar-PMA_SOCKETS"], [17, "envvar-PMA_SSL"], [17, "envvar-PMA_SSLS"], [17, "envvar-PMA_SSL_CA"], [17, "envvar-PMA_SSL_CAS"], [17, "envvar-PMA_SSL_CAS_BASE64"], [17, "envvar-PMA_SSL_CA_BASE64"], [17, "envvar-PMA_SSL_CERT"], [17, "envvar-PMA_SSL_CERTS"], [17, "envvar-PMA_SSL_CERTS_BASE64"], [17, "envvar-PMA_SSL_CERT_BASE64"], [17, "envvar-PMA_SSL_DIR"], [17, "envvar-PMA_SSL_KEY"], [17, "envvar-PMA_SSL_KEYS"], [17, "envvar-PMA_SSL_KEYS_BASE64"], [17, "envvar-PMA_SSL_KEY_BASE64"], [17, "envvar-PMA_SSL_VERIFIES"], [17, "envvar-PMA_SSL_VERIFY"], [17, "envvar-PMA_UPLOADDIR"], [17, "envvar-PMA_USER"], [17, "envvar-PMA_USER_CONFIG_BASE64"], [17, "envvar-PMA_VERBOSE"], [17, "envvar-PMA_VERBOSES"], [17, "envvar-TZ"], [17, "envvar-UPLOAD_LIMIT"], [17, "index-2"], [17, "index-31"], [17, "index-32"], [17, "index-33"], [17, "index-4"], [17, "index-5"], [17, "index-6"]], "phpmyadmin configuration storage": [[17, "index-35"]]}})